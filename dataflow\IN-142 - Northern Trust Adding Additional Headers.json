{"name": "IN-142 - Northern Trust Adding Additional Headers", "properties": {"folder": {"name": "Inbound"}, "type": "MappingDataFlow", "typeProperties": {"sources": [{"dataset": {"referenceName": "AzureDataLakeStarage_FX_Additional_Headers", "type": "DatasetReference"}, "name": "AdditionalHeaderSource"}, {"dataset": {"referenceName": "ADL_NorthernTrust_TEMP_FILE", "type": "DatasetReference"}, "name": "NorthernTrustDataSource"}], "sinks": [{"dataset": {"referenceName": "ADL_NorthernTrust_OUT", "type": "DatasetReference"}, "name": "OutputToCSV"}], "transformations": [{"name": "UnionToAddHeaders"}], "script": "parameters{\n\tOutputFileName as string\n}\nsource(output(\n\t\tId as string,\n\t\tCustodian_Account_Number__c as string,\n\t\tUS_Custodian_Account_Number__c as string,\n\t\tFinServ__SourceSystemId__c as string\n\t),\n\tallowSchemaDrift: true,\n\tvalidateSchema: false,\n\tignoreNoFilesFound: false) ~> AdditionalHeaderSource\nsource(allowSchemaDrift: true,\n\tvalidateSchema: false,\n\tignoreNoFilesFound: false,\n\twildcardPaths:['FX/Northern Trust/TEMP/temp.csv']) ~> NorthernTrustDataSource\nAdditionalHeaderSource, NorthernTrustDataSource union(byName: false)~> UnionToAddHeaders\nUnionToAddHeaders sink(allowSchemaDrift: true,\n\tvalidateSchema: false,\n\tpartitionFileNames:[($OutputFileName)],\n\tumask: 0022,\n\tpreCommands: [],\n\tpostCommands: [],\n\tskipDuplicateMapInputs: true,\n\tskipDuplicateMapOutputs: true,\n\tpartitionBy('hash', 1)) ~> OutputToCSV"}}}