{"name": "DECOM- IN-472 TCAPostTradeOutput- GRANDPARENT", "properties": {"activities": [{"name": "Execute PARENT", "type": "ExecutePipeline", "dependsOn": [], "policy": {"secureInput": false}, "userProperties": [], "typeProperties": {"pipeline": {"referenceName": "IN-472 TCAPostTradeOutput - PARENT", "type": "PipelineReference"}, "waitOnCompletion": true}}, {"name": "Send Failure Email", "type": "WebActivity", "dependsOn": [{"activity": "Execute PARENT", "dependencyConditions": ["Failed"]}], "policy": {"timeout": "0.05:00:00", "retry": 0, "retryIntervalInSeconds": 30, "secureOutput": false, "secureInput": false}, "userProperties": [], "typeProperties": {"method": "POST", "headers": {"Content-Type": "application/json"}, "url": {"value": "@pipeline().globalParameters.SendEmailLogicFunctionUrl", "type": "Expression"}, "body": {"value": "{\n\"BodyContents\": \"@{replace(activity('Execute PARENT').Error.Message,'\"','''')}\",\n\"ToContents\": \"@{pipeline().globalParameters.PipelineFailureNotificationEmailAddress}\",\n\"FolderLocation\": \"\",\n\"SubjectContents\":\"Beta TCA PostTrade Output - Workflow ADF Pipeline Failed\",\n}\n\n", "type": "Expression"}}}], "folder": {"name": "Internal/DDI/IN-329 DDI Migration/IN-472 TCAPostTradeOutput - Workflow"}, "annotations": [], "lastPublishTime": "2023-12-01T04:12:42Z"}, "type": "Microsoft.DataFactory/factories/pipelines"}