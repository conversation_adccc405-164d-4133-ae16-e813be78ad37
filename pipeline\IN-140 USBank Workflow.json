{"name": "IN-140 USBank Workflow", "properties": {"activities": [{"name": "Run Only In PROD", "type": "IfCondition", "dependsOn": [], "userProperties": [], "typeProperties": {"expression": {"value": "@equals(pipeline().DataFactory,'AZ-PRD-UW-DF-SCD')", "type": "Expression"}, "ifTrueActivities": [{"name": "Set Last Date", "type": "SetVariable", "dependsOn": [], "policy": {"secureOutput": false, "secureInput": false}, "userProperties": [], "typeProperties": {"variableName": "vLastDate", "value": {"value": "@formatDatetime(adddays(formatDatetime(convertFromUtc(utcnow(), 'Mountain Standard Time'), 'yyyy-MM-dd'), -1), 'yyyyMMdd')", "type": "Expression"}}}, {"name": "Execute Recon Pipeline", "type": "ExecutePipeline", "dependsOn": [{"activity": "Set Last Date", "dependencyConditions": ["Succeeded"]}], "policy": {"secureInput": false}, "userProperties": [], "typeProperties": {"pipeline": {"referenceName": "IN-141 USBank Recon", "type": "PipelineReference"}, "waitOnCompletion": true, "parameters": {"pDate": {"value": "@variables('vLastDate')", "type": "Expression"}, "pADLPath": {"value": "@pipeline().parameters.pADLPath", "type": "Expression"}, "pADLContainer": {"value": "@pipeline().parameters.pADLContainer", "type": "Expression"}, "pSendToSCD": {"value": "@pipeline().parameters.pSendToSCD", "type": "Expression"}, "pSCDsftpPath": {"value": "@concat(pipeline().globalParameters.SCD_ENV, \n'/ActiveData/Reconciliation/USBank/Working'\n)", "type": "Expression"}, "pDataType": {"value": "@pipeline().parameters.pDataType", "type": "Expression"}}}}, {"name": "Execute FX Pipeline", "type": "ExecutePipeline", "dependsOn": [{"activity": "Execute Recon Pipeline", "dependencyConditions": ["Succeeded"]}], "policy": {"secureInput": false}, "userProperties": [], "typeProperties": {"pipeline": {"referenceName": "IN-142 USBank FX", "type": "PipelineReference"}, "waitOnCompletion": true, "parameters": {"pDate": {"value": "@variables('vLastDate')", "type": "Expression"}, "pADLPath": "Custodian Data/USBank/USBank FX", "pADLContainer": {"value": "@pipeline().parameters.pADLContainer", "type": "Expression"}, "pSendToSCD": {"value": "@pipeline().parameters.pSendToSCD", "type": "Expression"}, "pSCDsftpPath": {"value": "@concat(pipeline().globalParameters.SCD_ENV, \r\n'/ActiveData/Interfaces/In/SFTP/TRADE_CAPTURE/Working'\r\n)", "type": "Expression"}, "pDataType": {"value": "@pipeline().parameters.pDataType", "type": "Expression"}}}}]}}], "parameters": {"pDate": {"type": "string"}, "pADLPath": {"type": "string", "defaultValue": "Custodian Data/USBank/USBank Recon"}, "pADLContainer": {"type": "string", "defaultValue": "scdintegration"}, "pSendToSCD": {"type": "bool", "defaultValue": true}, "pSCDsftpPath": {"type": "string", "defaultValue": "X"}, "pDataType": {"type": "string", "defaultValue": "X"}}, "variables": {"vLastDate": {"type": "String"}}, "folder": {"name": "Custodians and Vendors/USBank/USBank Workflow"}, "annotations": [], "lastPublishTime": "2024-08-14T21:31:36Z"}, "type": "Microsoft.DataFactory/factories/pipelines"}