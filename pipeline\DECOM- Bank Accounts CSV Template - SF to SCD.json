{"name": "DECOM- Bank Accounts CSV Template - SF to SCD", "properties": {"description": "Integrate BankAccounts data from salesforce to SCD\nhttps://beboringmakedata.atlassian.net/browse/IN-396", "activities": [{"name": "BankAccounts", "type": "Switch", "dependsOn": [{"activity": "Set File Name", "dependencyConditions": ["Succeeded"]}], "userProperties": [], "typeProperties": {"on": {"value": "@pipeline().parameters.ObjectName", "type": "Expression"}, "cases": [{"value": "FinancialAccount", "activities": [{"name": "Copy Financial Account data", "type": "Copy", "dependsOn": [], "policy": {"timeout": "7.00:00:00", "retry": 0, "retryIntervalInSeconds": 30, "secureOutput": false, "secureInput": false}, "userProperties": [], "typeProperties": {"source": {"type": "SqlMISource", "sqlReaderQuery": {"value": "@concat(\n' SELECT [Portfolio Group] ,[Portfolio ID] ,[Custodian] ,[Custody] ,[Bank Account Currencies] ,[Income bank account] ,[Identification System: RC_I_MT940] from scd.[vBankAccountsChange] where FinancialAccountId in (',pipeline().parameters.ObjectId,')')", "type": "Expression"}, "partitionOption": "None"}, "sink": {"type": "DelimitedTextSink", "storeSettings": {"type": "AzureBlobFSWriteSettings"}, "formatSettings": {"type": "DelimitedTextWriteSettings", "quoteAllText": true, "fileExtension": ".txt"}}, "enableStaging": false, "translator": {"type": "TabularTranslator", "typeConversion": true, "typeConversionSettings": {"allowDataTruncation": true, "treatBooleanAsNumber": false}}}, "inputs": [{"referenceName": "SalesforceViews", "type": "DatasetReference"}], "outputs": [{"referenceName": "AzureDataLakeStorage_SalesforceToScd_PortfolioFile", "type": "DatasetReference", "parameters": {"FileName": {"value": "@variables('FileName')", "type": "Expression"}, "FolderName": {"value": "@concat('Salesforce To SCD/Daily Loads/',formatDateTime(convertFromUtc(utcnow(),'Mountain Standard Time'),'yyyyMMdd'))", "type": "Expression"}}}]}]}, {"value": "<PERSON><PERSON>", "activities": [{"name": "Copy Merge Data", "type": "Copy", "dependsOn": [], "policy": {"timeout": "7.00:00:00", "retry": 0, "retryIntervalInSeconds": 30, "secureOutput": false, "secureInput": false}, "userProperties": [], "typeProperties": {"source": {"type": "SqlMISource", "sqlReaderQuery": {"value": "@concat(\n' SELECT [Portfolio Group] ,[Portfolio ID] ,[Custodian] ,[Custody] ,[Bank Account Currencies] ,[Income bank account] ,[Identification System: RC_I_MT940] from scd.[vBankAccountsChange] where MergeId in (',pipeline().parameters.ObjectId,')')", "type": "Expression"}, "partitionOption": "None"}, "sink": {"type": "DelimitedTextSink", "storeSettings": {"type": "AzureBlobFSWriteSettings"}, "formatSettings": {"type": "DelimitedTextWriteSettings", "quoteAllText": true, "fileExtension": ".txt"}}, "enableStaging": false, "translator": {"type": "TabularTranslator", "typeConversion": true, "typeConversionSettings": {"allowDataTruncation": true, "treatBooleanAsNumber": false}}}, "inputs": [{"referenceName": "SalesforceViews", "type": "DatasetReference"}], "outputs": [{"referenceName": "AzureDataLakeStorage_SalesforceToScd_PortfolioFile", "type": "DatasetReference", "parameters": {"FileName": {"value": "@variables('FileName')", "type": "Expression"}, "FolderName": {"value": "@concat('Salesforce To SCD/Daily Loads/',formatDateTime(convertFromUtc(utcnow(),'Mountain Standard Time'),'yyyyMMdd'))", "type": "Expression"}}}]}]}]}}, {"name": "Set File Name", "type": "SetVariable", "dependsOn": [], "policy": {"secureOutput": false, "secureInput": false}, "userProperties": [], "typeProperties": {"variableName": "FileName", "value": {"value": "@concat('BankAccounts_',formatDateTime(convertFromUtc(utcnow(),'Mountain Standard Time'),'MMddyyyy'),'_',formatDateTime(convertFromUtc(utcnow(),'Mountain Standard Time'),'hhmmss'),'.csv')", "type": "Expression"}}}, {"name": "Copy File to SCD SFTP", "type": "Copy", "dependsOn": [{"activity": "BankAccounts", "dependencyConditions": ["Succeeded"]}], "policy": {"timeout": "7.00:00:00", "retry": 0, "retryIntervalInSeconds": 30, "secureOutput": false, "secureInput": false}, "userProperties": [], "typeProperties": {"source": {"type": "BinarySource", "storeSettings": {"type": "AzureBlobFSReadSettings", "recursive": true}, "formatSettings": {"type": "BinaryReadSettings"}}, "sink": {"type": "BinarySink", "storeSettings": {"type": "SftpWriteSettings", "operationTimeout": "01:00:00", "useTempFileRename": true}}, "enableStaging": false}, "inputs": [{"referenceName": "AzureDataLakeStorage_Portfolio_Binary", "type": "DatasetReference", "parameters": {"SourceDirectory": {"value": "@concat('Salesforce To SCD/Daily Loads/',formatDateTime(convertFromUtc(utcnow(),'Mountain Standard Time'),'yyyyMMdd'))", "type": "Expression"}, "FileName": {"value": "@variables('FileName')", "type": "Expression"}}}], "outputs": [{"referenceName": "SFTP_SCD_DIR_BINARY", "type": "DatasetReference", "parameters": {"Path": {"value": "@concat(pipeline().globalParameters.SCD_ENV,pipeline().globalParameters.SCD_SFTP_Portfolio)", "type": "Expression"}}}]}], "parameters": {"ObjectId": {"type": "string", "defaultValue": "'a095G00001accYoQAI'"}, "ObjectName": {"type": "string", "defaultValue": "FinancialAccount"}}, "variables": {"FileName": {"type": "String"}}, "folder": {"name": "Internal/SFtoSCD/SF-SCD Real Time"}, "annotations": [], "lastPublishTime": "2023-12-01T04:11:32Z"}, "type": "Microsoft.DataFactory/factories/pipelines"}