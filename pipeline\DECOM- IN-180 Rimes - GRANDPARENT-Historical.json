{"name": "DECOM- IN-180 Rimes - GRANDPARENT-Historical", "properties": {"activities": [{"name": "Select files with no revision by date", "type": "Filter", "dependsOn": [{"activity": "Get File Names", "dependencyConditions": ["Succeeded"]}], "userProperties": [], "typeProperties": {"items": {"value": "@activity('Get File Names').output.childItems", "type": "Expression"}, "condition": {"value": "@and(equals(item().type, 'File'), \n        equals(length(\n                        last(\n                                split(\n                                        substring(\n                                                item().name, 0, lastindexof(item().name, '.csv')\n                                                ), '_'\n                                        )\n                             )\n                        )\n                , 8)\n    )", "type": "Expression"}}}, {"name": "Select files with revision by date", "type": "Filter", "dependsOn": [{"activity": "Get File Names", "dependencyConditions": ["Succeeded"]}], "userProperties": [], "typeProperties": {"items": {"value": "@activity('Get File Names').output.childItems", "type": "Expression"}, "condition": {"value": "@and(equals(item().type, 'File'), \n        equals(length(\n                        last(\n                                split(\n                                        substring(item().name, 0, lastindexof(item().name, '_'))\n                                        , '_'\n                                     )\n                              )\n                        ), 8\n                      )\n                      \n    )", "type": "Expression"}}}, {"name": "Process files with no revision date", "type": "ForEach", "dependsOn": [{"activity": "Select files with no revision by date", "dependencyConditions": ["Succeeded"]}], "userProperties": [], "typeProperties": {"items": {"value": "@activity('Select files with no revision by date').output.Value", "type": "Expression"}, "isSequential": false, "activities": [{"name": "Rimes No Revision Outer Loop Daily", "type": "ExecutePipeline", "dependsOn": [], "policy": {"secureInput": false}, "userProperties": [], "typeProperties": {"pipeline": {"referenceName": "DECOM- IN-180 Rimes No Revision - Parent_History", "type": "PipelineReference"}, "waitOnCompletion": true, "parameters": {"origin_run_id": {"value": "@pipeline().RunId", "type": "Expression"}, "send_to_scd": {"value": "@pipeline().parameters.send_to_scd", "type": "Expression"}, "file_name": {"value": "@item().name", "type": "Expression"}}}}]}}, {"name": "Process files with revisions outer loop", "type": "ForEach", "dependsOn": [{"activity": "Select files with revision by date", "dependencyConditions": ["Succeeded"]}], "userProperties": [], "typeProperties": {"items": {"value": "@activity('Select files with revision by date').output.Value", "type": "Expression"}, "isSequential": true, "activities": [{"name": "If name in unique file names array", "type": "IfCondition", "dependsOn": [], "userProperties": [], "typeProperties": {"expression": {"value": "@contains(\n    variables('unique_file_names'),\n    substring(\n        item().name,\n        0,\n        lastindexof(item().name, '_')\n    )\n)", "type": "Expression"}, "ifFalseActivities": [{"name": "Append to unique file names", "type": "AppendVariable", "dependsOn": [], "userProperties": [], "typeProperties": {"variableName": "unique_file_names", "value": {"value": "@substring(\n    item().name,\n    0,\n    lastindexof(item().name, '_')\n)", "type": "Expression"}}}, {"name": "Copy latest revision", "type": "ExecutePipeline", "dependsOn": [{"activity": "Append to unique file names", "dependencyConditions": ["Succeeded"]}], "policy": {"secureInput": false}, "userProperties": [], "typeProperties": {"pipeline": {"referenceName": "DECOM- IN-180 Rimes Revision Outer Loop Daily - CHILD_V2", "type": "PipelineReference"}, "waitOnCompletion": true, "parameters": {"file_name": {"value": "@last(variables('unique_file_names'))", "type": "Expression"}, "file_names": {"value": "@activity('Select files with revision by date').output.Value", "type": "Expression"}, "origin_run_id": {"value": "@pipeline().RunId", "type": "Expression"}, "send_to_scd": {"value": "@pipeline().parameters.send_to_scd", "type": "Expression"}}}}]}}]}}, {"name": "Get File Names", "type": "GetMetadata", "dependsOn": [], "policy": {"timeout": "7.00:00:00", "retry": 0, "retryIntervalInSeconds": 30, "secureOutput": false, "secureInput": false}, "userProperties": [], "typeProperties": {"dataset": {"referenceName": "AzureDataLakeStorage_Rimes_Dir", "type": "DatasetReference", "parameters": {"dir_name": "IN/HISTORY_2021-2022_ALLFILES"}}, "fieldList": ["childItems"], "storeSettings": {"type": "AzureBlobFSReadSettings", "recursive": true, "enablePartitionDiscovery": false}, "formatSettings": {"type": "DelimitedTextReadSettings"}}}], "concurrency": 1, "parameters": {"send_to_scd": {"type": "bool", "defaultValue": false}}, "variables": {"successful_files": {"type": "Array"}, "unique_file_names": {"type": "Array"}, "BatchStartTime": {"type": "String"}, "BatchEndTime": {"type": "String"}, "filedate": {"type": "String"}}, "folder": {"name": "Internal/RIMES to SCD"}, "annotations": [], "lastPublishTime": "2023-12-01T04:12:43Z"}, "type": "Microsoft.DataFactory/factories/pipelines"}