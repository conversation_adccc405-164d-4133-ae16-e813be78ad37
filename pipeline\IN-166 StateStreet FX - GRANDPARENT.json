{"name": "IN-166 StateStreet FX - GRANDPARENT", "properties": {"activities": [{"name": "Set Today Date", "type": "SetVariable", "dependsOn": [], "policy": {"secureOutput": false, "secureInput": false}, "userProperties": [], "typeProperties": {"variableName": "Date", "value": {"value": "@formatDatetime(convertFromUtc(utcnow(), 'Mountain Standard Time')\n, 'yyyy-MM-dd')", "type": "Expression"}}}, {"name": "Execute IN-166 StateStreet FX - PARENT", "type": "ExecutePipeline", "dependsOn": [{"activity": "Set Today Date", "dependencyConditions": ["Succeeded"]}], "policy": {"secureInput": false}, "userProperties": [], "typeProperties": {"pipeline": {"referenceName": "IN-166 StateStreet FX - PARENT", "type": "PipelineReference"}, "waitOnCompletion": true, "parameters": {"Date": {"value": "@variables('Date')", "type": "Expression"}}}}], "variables": {"Date": {"type": "String"}}, "folder": {"name": "State Street/SSCA FX"}, "annotations": [], "lastPublishTime": "2023-12-11T22:28:55Z"}, "type": "Microsoft.DataFactory/factories/pipelines"}