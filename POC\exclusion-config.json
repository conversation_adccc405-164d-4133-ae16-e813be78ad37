{"exclusionSettings": {"annotations": ["DEV_ONLY", "EXCLUDE_FROM_PROD", "DEVELOPMENT_ONLY", "TESTING_PIPELINE", "EXPERIMENTAL", "POC_PIPELINE"], "pipelineNamePatterns": ["DEV-ONLY-*", "TEST-*", "EXPERIMENTAL-*", "*-DEV", "*-TESTING"], "folderExclusions": ["POC/DEV-Only-Pipelines", "Testing", "Experimental", "Development"]}, "environments": {"dev": {"dataFactoryName": "AZ-DEV-UW-DF-SCD", "resourceGroupName": "rg-dev-adf", "subscriptionId": "your-dev-subscription-id", "allowAllPipelines": true}, "prod": {"dataFactoryName": "AZ-PRD-UW-DF-SCD", "resourceGroupName": "rg-prod-adf", "subscriptionId": "your-prod-subscription-id", "allowAllPipelines": false, "applyExclusions": true}}, "deploymentSettings": {"generateExclusionReport": true, "validateBeforeDeployment": true, "stopTriggersBeforeDeployment": true, "startTriggersAfterDeployment": true, "backupBeforeDeployment": false}, "notificationSettings": {"sendExclusionReport": true, "emailRecipients": ["<EMAIL>", "<EMAIL>"], "includeExcludedPipelinesList": true, "includeDeploymentSummary": true}, "auditSettings": {"logExclusions": true, "retainExclusionReports": true, "reportRetentionDays": 90, "auditLogPath": "./audit-logs"}}