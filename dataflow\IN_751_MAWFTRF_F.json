{"name": "IN_751_MAWFTRF_F", "properties": {"folder": {"name": "Inbound"}, "type": "MappingDataFlow", "typeProperties": {"sources": [{"dataset": {"referenceName": "IN_751_ADL_IFDS_CashFlow_Delimated", "type": "DatasetReference"}, "name": "SrcFileMAWFTRFCADF"}], "sinks": [{"dataset": {"referenceName": "Sink_751_ADL_IFDS_CashFlow_Delimated", "type": "DatasetReference"}, "name": "Generatesinkfile"}], "transformations": [{"name": "MapDriftedColumns", "description": "Creates an explicit mapping for each drifted column"}, {"name": "AddLoadedDate"}, {"name": "FinalSelectionColumns"}], "scriptLines": ["source(allowSchemaDrift: true,", "     validateSchema: false,", "     ignoreNoFilesFound: false,", "     wildcardPaths:[(concat(\"IFDS_Outbond_CashFlow/IFDS_CashFlow/Temp/MAWFTRE_CAD_F/*.csv\"))]) ~> SrcFileMAWFTRFCADF", "SrcFileMAWFTRFCADF derive({File Name} = toString(byName('File Name')),", "          {Custodian Name} = toString(byName('Custodian Name')),", "          {Acct Grp Name} = toString(byName('Acct Grp Name')),", "          {Current Date} = toString(byName('Current Date')),", "          {Client Code} = toString(byName('Client Code')),", "          {Company Code} = toString(byName('Company Code')),", "          Currency = toString(byName('Currency')),", "          {Cycle Date} = toString(byName('Cycle Date')),", "          {Fund Name} = toString(byName('Fund Name')),", "          PricingLevel = toString(byName('PricingLevel')),", "          Fund = toString(byName('Fund')),", "          {FundCustodianRef#} = toString(byName('FundCustodianRef#')),", "          Pur = toString(byName('Pur')),", "          ExchIn = toString(byName('ExchIn')),", "          TfrIn = toString(byName('TfrIn')),", "          Red = toString(by<PERSON><PERSON>('Red')),", "          ExchOut = toString(byName('ExchOut')),", "          TfrOut = toString(byName('TfrOut')),", "          CashDiv = toString(byName('CashDiv')),", "          CashMFR = toString(byName('CashMFR')),", "          AdmFee = toString(byName('AdmFee')),", "          MgmtFee = toString(byName('MgmtFee')),", "          GurFee = toString(byName('GurFee')),", "          DlrFee = toString(byName('DlrFee')),", "          NRTax = toString(byName('NRTax')),", "          NetCash = toString(byName('NetCash')),", "          {N$MPur} = toString(byName('N$MPur')),", "          {N$MRed} = toString(byName('N$MRed')),", "          {N$MPurRev} = toString(byName('N$MPurRev')),", "          {N$MRedRev} = toString(byName('N$MRedRev')),", "          ICTXls = toString(byName('ICTXls')),", "          {N$MRej} = toString(byName('N$MRej')),", "          ICTXlsRev = toString(byName('ICTXlsRev')),", "          {N$MRejRev} = toString(byName('N$MRejRev')),", "          DivOut = toString(byName('DivOut')),", "          DivIn = toString(byName('DivIn')),", "          SpPurRev = toString(byName('SpPurRev')),", "          SpRedRev = toString(byName('SpRedRev')),", "          DirPayPur = toString(byName('DirPayPur')),", "          DSCFee = toString(by<PERSON><PERSON>('DSCFee')),", "          OTHFee = toString(byName('OTHFee')),", "          FinalTrfReq = toString(byName('FinalTrfReq'))) ~> MapDriftedColumns", "MapDriftedColumns derive(LoadDate = currentDate('yyyy-MM-dd HH:mm:ss.fff')) ~> AddLoadedDate", "AddLoadedDate select(mapColumn(", "          {File Name},", "          {<PERSON><PERSON>odian Name},", "          {Acct Grp Name},", "          {Current Date},", "          {Client Code},", "          {Company Code},", "          <PERSON><PERSON><PERSON><PERSON>,", "          {Cycle Date},", "          {Fund Name},", "          PricingLevel,", "          Fund,", "          {FundCustodianRef#},", "          <PERSON><PERSON>,", "          ExchIn,", "          TfrIn,", "          Red,", "          ExchOut,", "          Tfr<PERSON>ut,", "          CashDiv,", "          CashMFR,", "          <PERSON><PERSON><PERSON><PERSON>,", "          MgmtFee,", "          <PERSON><PERSON><PERSON><PERSON>,", "          <PERSON><PERSON><PERSON><PERSON><PERSON>,", "          NRTax,", "          NetCash,", "          {N$MPur},", "          {N$MRed},", "          {N$MPurRev},", "          {N$MRedRev},", "          ICTXls,", "          {N$MRej},", "          ICTXlsRev,", "          {N$MRejRev},", "          Div<PERSON>ut,", "          DivIn,", "          <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,", "          <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,", "          <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,", "          <PERSON><PERSON><PERSON>,", "          <PERSON><PERSON><PERSON><PERSON>,", "          FinalTrfReq,", "          LoadDate", "     ),", "     skipDuplicateMapInputs: true,", "     skipDuplicateMapOutputs: true) ~> FinalSelectionColumns", "FinalSelectionColumns sink(allowSchemaDrift: true,", "     validateSchema: false,", "     partitionFileNames:['MAWFTRF_CAD_F.csv'],", "     umask: 0022,", "     preCommands: [],", "     postCommands: [],", "     skipDuplicateMapInputs: true,", "     skipDuplicateMapOutputs: true,", "     partitionBy('hash', 1)) ~> Generatesinkfile"]}}}