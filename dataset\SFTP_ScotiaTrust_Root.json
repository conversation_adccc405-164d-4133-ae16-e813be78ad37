{"name": "SFTP_ScotiaTrust_Root", "properties": {"linkedServiceName": {"referenceName": "ScotiaTrustFileShare", "type": "LinkedServiceReference"}, "folder": {"name": "Inbound/Reconciliation - Holdings & Payments/SR-6 ScotiaTrust Recon"}, "annotations": [], "type": "DelimitedText", "typeProperties": {"location": {"type": "AzureFileStorageLocation"}, "columnDelimiter": ",", "escapeChar": "\\", "quoteChar": "\""}, "schema": []}, "type": "Microsoft.DataFactory/factories/datasets"}