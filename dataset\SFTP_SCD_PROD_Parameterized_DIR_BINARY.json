{"name": "SFTP_SCD_PROD_Parameterized_DIR_BINARY", "properties": {"linkedServiceName": {"referenceName": "SCD_SFTP_Prod", "type": "LinkedServiceReference"}, "parameters": {"FilePath": {"type": "string"}}, "folder": {"name": "SCD"}, "annotations": [], "type": "DelimitedText", "typeProperties": {"location": {"type": "SftpLocation", "folderPath": {"value": "@dataset().FilePath", "type": "Expression"}}, "columnDelimiter": ",", "escapeChar": "\\", "firstRowAsHeader": true, "quoteChar": "\""}, "schema": []}, "type": "Microsoft.DataFactory/factories/datasets"}