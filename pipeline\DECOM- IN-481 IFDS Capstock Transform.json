{"name": "DECOM- IN-481 IFDS Capstock Transform", "properties": {"activities": [{"name": "GetMetadataRaw", "description": "Read meta data of each file which belong to IFDS Raw files container", "type": "GetMetadata", "dependsOn": [{"activity": "Set Temp File Name of Fund Dim", "dependencyConditions": ["Succeeded"]}], "policy": {"timeout": "7.00:00:00", "retry": 0, "retryIntervalInSeconds": 30, "secureOutput": false, "secureInput": false}, "userProperties": [], "typeProperties": {"dataset": {"referenceName": "AzurDataLakeStorage_IFDS_CapstockTransform_DIR", "type": "DatasetReference", "parameters": {"DIrectory": {"value": "@variables('RawFilesFolderLastBizDate')", "type": "Expression"}}}, "fieldList": ["childItems"], "storeSettings": {"type": "AzureBlobFSReadSettings", "recursive": true, "enablePartitionDiscovery": false}, "formatSettings": {"type": "DelimitedTextReadSettings"}}}, {"name": "Convert File To CSV", "type": "Copy", "dependsOn": [{"activity": "Check for CAD File", "dependencyConditions": ["Succeeded"]}], "policy": {"timeout": "7.00:00:00", "retry": 0, "retryIntervalInSeconds": 30, "secureOutput": false, "secureInput": false}, "userProperties": [], "typeProperties": {"source": {"type": "DelimitedTextSource", "storeSettings": {"type": "AzureBlobFSReadSettings", "recursive": false, "wildcardFolderPath": {"value": "@variables('RawFilesFolderLastBizDate')", "type": "Expression"}, "wildcardFileName": {"value": "@variables('Input_CashFlowFfile')", "type": "Expression"}, "enablePartitionDiscovery": false}, "formatSettings": {"type": "DelimitedTextReadSettings"}}, "sink": {"type": "DelimitedTextSink", "storeSettings": {"type": "AzureBlobFSWriteSettings"}, "formatSettings": {"type": "DelimitedTextWriteSettings", "quoteAllText": true, "fileExtension": ".txt"}}, "enableStaging": false, "translator": {"type": "TabularTranslator", "typeConversion": true, "typeConversionSettings": {"allowDataTruncation": true, "treatBooleanAsNumber": false}}}, "inputs": [{"referenceName": "AzurDataLakeStorage_IFDS_CapstockTransform_DIR", "type": "DatasetReference", "parameters": {"DIrectory": {"value": "@variables('RawFilesFolderLastBizDate')", "type": "Expression"}}}], "outputs": [{"referenceName": "AzurDataLakeStorage_IFDS_CapstockTransform_CSV_DIR_FILE", "type": "DatasetReference", "parameters": {"FileName": {"value": "@variables('TempCashFlow_F_FileName')", "type": "Expression"}, "Directory": {"value": "@variables('TempCashFlow_Directory')", "type": "Expression"}}}]}, {"name": "Set Temp File Name of Cash Flow", "type": "SetVariable", "dependsOn": [{"activity": "Set Temp Directory Path", "dependencyConditions": ["Succeeded"]}], "policy": {"secureOutput": false, "secureInput": false}, "userProperties": [], "typeProperties": {"variableName": "TempCashFlow_F_FileName", "value": {"value": "@concat('MAWFTRF_CAD_F_',variables('LastBizDate'),'.csv')", "type": "Expression"}}}, {"name": "Set Temp Directory Path", "type": "SetVariable", "dependsOn": [{"activity": "Set OutPut File Name", "dependencyConditions": ["Succeeded"]}], "policy": {"secureOutput": false, "secureInput": false}, "userProperties": [], "typeProperties": {"variableName": "TempCashFlow_Directory", "value": "IFDS/IFDSCapstockTransform/TEMP"}}, {"name": "FundDim", "type": "Copy", "dependsOn": [{"activity": "Convert File To CSV", "dependencyConditions": ["Succeeded"]}], "policy": {"timeout": "7.00:00:00", "retry": 0, "retryIntervalInSeconds": 30, "secureOutput": false, "secureInput": false}, "userProperties": [], "typeProperties": {"source": {"type": "SqlServerSource", "sqlReaderQuery": "SELECT DISTINCT StateStreetID, ApxPortfolioCd\n        FROM DATAMART.dbo.[FundDim]\n        WHERE \n            FundSeriesDesc <> 'Discontinued.' AND  FundKey > 0 ", "queryTimeout": "02:00:00", "partitionOption": "None"}, "sink": {"type": "DelimitedTextSink", "storeSettings": {"type": "AzureBlobFSWriteSettings"}, "formatSettings": {"type": "DelimitedTextWriteSettings", "quoteAllText": true, "fileExtension": ".txt"}}, "enableStaging": false, "translator": {"type": "TabularTranslator", "typeConversion": true, "typeConversionSettings": {"allowDataTruncation": true, "treatBooleanAsNumber": false}}}, "inputs": [{"referenceName": "SqlServerDB_IFDS_Capstock_Transform", "type": "DatasetReference"}], "outputs": [{"referenceName": "AzurDataLakeStorage_IFDS_CapstockTransform_FundDim_CSV_DIR_FILE", "type": "DatasetReference", "parameters": {"FileName": {"value": "@variables('Temp_FundDim_FileName')", "type": "Expression"}, "Directory": {"value": "@variables('TempCashFlow_Directory')", "type": "Expression"}}}]}, {"name": "Set Temp File Name of Fund Dim", "type": "SetVariable", "dependsOn": [{"activity": "Set Temp File Name of Cash Flow", "dependencyConditions": ["Succeeded"]}], "policy": {"secureOutput": false, "secureInput": false}, "userProperties": [], "typeProperties": {"variableName": "Temp_FundDim_FileName", "value": {"value": "@concat('FundDim_',variables('LastBizDate'),'.csv')", "type": "Expression"}}}, {"name": "Generate Final CSV File", "type": "ExecuteDataFlow", "dependsOn": [{"activity": "FundDim", "dependencyConditions": ["Succeeded"]}], "policy": {"timeout": "1.00:00:00", "retry": 0, "retryIntervalInSeconds": 30, "secureOutput": false, "secureInput": false}, "userProperties": [], "typeProperties": {"dataflow": {"referenceName": "DF_IFDS_Capstock_Transform", "type": "DataFlowReference", "parameters": {"OutPutFileName": {"value": "'@{variables('OutPut_FileName')}'", "type": "Expression"}}, "datasetParameters": {"IFDSMawerFundCapstockTemp": {"FileName": {"value": "@variables('TempCashFlow_F_FileName')", "type": "Expression"}, "Directory": {"value": "@variables('TempCashFlow_Directory')", "type": "Expression"}}, "FundDim": {"FileName": {"value": "@variables('Temp_FundDim_FileName')", "type": "Expression"}, "Directory": {"value": "@variables('TempCashFlow_Directory')", "type": "Expression"}}, "GenerateCSVFile": {"DIrectory": {"value": "@variables('OutPut_Directory')", "type": "Expression"}}}}, "compute": {"coreCount": 8, "computeType": "General"}, "traceLevel": "Fine"}}, {"name": "Set OutPut Directory Path", "type": "SetVariable", "dependsOn": [{"activity": "Set Archive Directory Path", "dependencyConditions": ["Succeeded"]}], "policy": {"secureOutput": false, "secureInput": false}, "userProperties": [], "typeProperties": {"variableName": "OutPut_Directory", "value": "IFDS/IFDSCapstockTransform/OUT"}}, {"name": "Set OutPut File Name", "type": "SetVariable", "dependsOn": [{"activity": "Set OutPut Directory Path", "dependencyConditions": ["Succeeded"]}], "policy": {"secureOutput": false, "secureInput": false}, "userProperties": [], "typeProperties": {"variableName": "OutPut_FileName", "value": {"value": "@concat('MawerFundCapstock_',variables('LastBizDate'),'.csv')", "type": "Expression"}}}, {"name": "Set Archive Directory Path", "type": "SetVariable", "dependsOn": [{"activity": "RawFilesFolderLastBizDate", "dependencyConditions": ["Succeeded"]}], "policy": {"secureOutput": false, "secureInput": false}, "userProperties": [], "typeProperties": {"variableName": "Archive_Directory", "value": {"value": "@concat('IFDS/IFDSCapstockTransform/ARCHIVE/',variables('LastBizDate'))", "type": "Expression"}}}, {"name": "Filter MAWFTRF_CAD_F", "type": "Filter", "dependsOn": [{"activity": "GetMetadataRaw", "dependencyConditions": ["Succeeded"]}], "userProperties": [], "typeProperties": {"items": {"value": "@activity('GetMetadataRaw').output.childItems", "type": "Expression"}, "condition": {"value": "@and(startswith(item().name,'MAWFTRF_CAD_F'),not( endswith(item().name,'d')))", "type": "Expression"}}}, {"name": "Check for CAD File", "type": "IfCondition", "dependsOn": [{"activity": "Filter MAWFTRF_CAD_F", "dependencyConditions": ["Succeeded"]}], "userProperties": [], "typeProperties": {"expression": {"value": "@greater(activity('Filter MAWFTRF_CAD_F').output.FilteredItemsCount,0)", "type": "Expression"}, "ifTrueActivities": [{"name": "Set Mawer Cad F File name", "type": "SetVariable", "dependsOn": [], "policy": {"secureOutput": false, "secureInput": false}, "userProperties": [], "typeProperties": {"variableName": "Input_CashFlowFfile", "value": {"value": "@activity('Filter MAWFTRF_CAD_F').output.Value[0].name", "type": "Expression"}}}]}}, {"name": "Find the last Biz Date", "type": "Lookup", "dependsOn": [], "policy": {"timeout": "7.00:00:00", "retry": 0, "retryIntervalInSeconds": 30, "secureOutput": false, "secureInput": false}, "userProperties": [], "typeProperties": {"source": {"type": "AzureDataExplorerSource", "query": {"value": "@concat(\n'Calendar\n| where format_datetime(Date,\"yyyyMMdd\") == \"',\nformatDateTime(convertFromUtc(utcnow(),'Mountain Standard Time'),'yyyyMMdd'),\n'\"\n| project TDid=BD_Id-1\n| join\n( Calendar\n| where Type == \"Working\"\n| project TD_1id= BD_Id, TD_1Date = Date)\non $left.TDid == $right.TD_1id\n| project TD_1Date=format_datetime(TD_1Date,\"yyyyMMdd\")'\n)", "type": "Expression"}, "queryTimeout": "00:10:00", "noTruncation": false}, "dataset": {"referenceName": "ADX_CustodianData", "type": "DatasetReference", "parameters": {"table": "Calendar"}}}}, {"name": "Set Last Biz Date Variable", "type": "SetVariable", "dependsOn": [{"activity": "Find the last Biz Date", "dependencyConditions": ["Succeeded"]}], "policy": {"secureOutput": false, "secureInput": false}, "userProperties": [], "typeProperties": {"variableName": "LastBizDate", "value": {"value": "@activity('Find the last Biz Date').output.firstRow.TD_1Date", "type": "Expression"}}}, {"name": "RawFilesFolderLastBizDate", "type": "SetVariable", "dependsOn": [{"activity": "Set Last Biz Date Variable", "dependencyConditions": ["Succeeded"]}], "policy": {"secureOutput": false, "secureInput": false}, "userProperties": [], "typeProperties": {"variableName": "RawFilesFolderLastBizDate", "value": {"value": "@concat('IFDS/Raw Files/',variables('LastBizDate'))", "type": "Expression"}}}, {"name": "Archive OUT", "type": "Copy", "dependsOn": [{"activity": "If Sending File to SCD SFTP", "dependencyConditions": ["Succeeded"]}], "policy": {"timeout": "7.00:00:00", "retry": 0, "retryIntervalInSeconds": 30, "secureOutput": false, "secureInput": false}, "userProperties": [], "typeProperties": {"source": {"type": "BinarySource", "storeSettings": {"type": "AzureBlobFSReadSettings", "recursive": true, "deleteFilesAfterCompletion": true}, "formatSettings": {"type": "BinaryReadSettings"}}, "sink": {"type": "BinarySink", "storeSettings": {"type": "AzureBlobFSWriteSettings"}}, "enableStaging": false}, "inputs": [{"referenceName": "ADL_Binary_Dir", "type": "DatasetReference", "parameters": {"Path": {"value": "@variables('OutPut_Directory')", "type": "Expression"}}}], "outputs": [{"referenceName": "ADL_Binary_Dir", "type": "DatasetReference", "parameters": {"Path": {"value": "@concat(variables('Archive_Directory'),'/OUT')", "type": "Expression"}}}]}, {"name": "Archive TEMP", "type": "Copy", "dependsOn": [{"activity": "If Sending File to SCD SFTP", "dependencyConditions": ["Succeeded"]}], "policy": {"timeout": "7.00:00:00", "retry": 0, "retryIntervalInSeconds": 30, "secureOutput": false, "secureInput": false}, "userProperties": [], "typeProperties": {"source": {"type": "BinarySource", "storeSettings": {"type": "AzureBlobFSReadSettings", "recursive": true, "deleteFilesAfterCompletion": true}, "formatSettings": {"type": "BinaryReadSettings"}}, "sink": {"type": "BinarySink", "storeSettings": {"type": "AzureBlobFSWriteSettings"}}, "enableStaging": false}, "inputs": [{"referenceName": "ADL_Binary_Dir", "type": "DatasetReference", "parameters": {"Path": {"value": "@variables('TempCashFlow_Directory')", "type": "Expression"}}}], "outputs": [{"referenceName": "ADL_Binary_Dir", "type": "DatasetReference", "parameters": {"Path": {"value": "@concat(variables('Archive_Directory'),'/TEMP')", "type": "Expression"}}}]}, {"name": "If Sending File to SCD SFTP", "type": "IfCondition", "dependsOn": [{"activity": "Generate Final CSV File", "dependencyConditions": ["Succeeded"]}], "userProperties": [], "typeProperties": {"expression": {"value": "@pipeline().globalParameters.SendToSCD_IFDS", "type": "Expression"}, "ifTrueActivities": [{"name": "Copy file to SCD SFTP", "type": "Copy", "dependsOn": [], "policy": {"timeout": "7.00:00:00", "retry": 0, "retryIntervalInSeconds": 30, "secureOutput": false, "secureInput": false}, "userProperties": [], "typeProperties": {"source": {"type": "BinarySource", "storeSettings": {"type": "AzureBlobFSReadSettings", "recursive": true, "deleteFilesAfterCompletion": false}, "formatSettings": {"type": "BinaryReadSettings"}}, "sink": {"type": "BinarySink", "storeSettings": {"type": "SftpWriteSettings", "operationTimeout": "01:00:00", "useTempFileRename": true}}, "enableStaging": false}, "inputs": [{"referenceName": "ADL_Binary_Dir", "type": "DatasetReference", "parameters": {"Path": {"value": "@variables('OutPut_Directory')", "type": "Expression"}}}], "outputs": [{"referenceName": "SCD_SFTP_Binary_Dir", "type": "DatasetReference", "parameters": {"Path": {"value": "@concat(pipeline().globalParameters.SCD_ENV, \n'/ActiveData/Interfaces/In/SFTP/FundServ/Working')\n", "type": "Expression"}}}]}]}}], "variables": {"Input_CashFlowFfile": {"type": "String"}, "TempCashFlow_F_FileName": {"type": "String"}, "TempCashFlow_Directory": {"type": "String"}, "Temp_FundDim_FileName": {"type": "String"}, "OutPut_FileName": {"type": "String"}, "OutPut_Directory": {"type": "String"}, "Archive_Directory": {"type": "String"}, "LastBizDate": {"type": "String"}, "RawFilesFolderLastBizDate": {"type": "String"}}, "folder": {"name": "ZzzIFDS\\IN-108 IFDS Cash Flow"}, "annotations": [], "lastPublishTime": "2024-02-02T23:41:24Z"}, "type": "Microsoft.DataFactory/factories/pipelines"}