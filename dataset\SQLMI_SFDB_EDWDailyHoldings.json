{"name": "SQLMI_SFDB_EDWDailyHoldings", "properties": {"linkedServiceName": {"referenceName": "AzureSQLManagedInstance", "type": "LinkedServiceReference", "parameters": {"dbName": {"value": "@dataset().DBName", "type": "Expression"}}}, "parameters": {"DBName": {"type": "string"}}, "folder": {"name": "General"}, "annotations": [], "type": "AzureSqlMITable", "schema": [], "typeProperties": {"schema": "dbo", "table": "EDWDailyHoldings"}}, "type": "Microsoft.DataFactory/factories/datasets"}