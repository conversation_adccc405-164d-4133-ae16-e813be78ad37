{"name": "Alliance Partner Workflow", "properties": {"activities": [{"name": "Generate QFM Templates", "type": "IfCondition", "dependsOn": [{"activity": "Insert New Valid Accounts to AlliancePartnerAccounts", "dependencyConditions": ["Succeeded"]}], "userProperties": [], "typeProperties": {"expression": {"value": "@greater(activity('Insert New Valid Accounts to AlliancePartnerAccounts').output.count,0 )", "type": "Expression"}, "ifTrueActivities": [{"name": "Generate Alliance Partner Files", "type": "ExecutePipeline", "dependsOn": [], "policy": {"secureInput": false}, "userProperties": [], "typeProperties": {"pipeline": {"referenceName": "DECOM- Generate AlliancePartner File", "type": "PipelineReference"}, "waitOnCompletion": true, "parameters": {"RunDate": {"value": "@formatDateTime(convertFromUtc(utcnow(),'Mountain Standard Time' ),'yyyyMMdd')\n", "type": "Expression"}}}}]}}, {"name": "Update Valid Accounts Flag", "type": "Lookup", "dependsOn": [{"activity": "Identify Alliance Portfolio from IMW to ADL_copy1_copy1", "dependencyConditions": ["Succeeded"]}], "policy": {"timeout": "7.00:00:00", "retry": 0, "retryIntervalInSeconds": 30, "secureOutput": false, "secureInput": false}, "userProperties": [], "typeProperties": {"source": {"type": "SqlMISource", "sqlReaderQuery": "update ap\nset [IsApprovedDealerRepCode] = 'Yes'\nfrom Billing.QFM.AlliancePartnerAccountsDaily as ap\njoin Billing.QFM.[AlliancePartnerDealerRepCode] b on b.[Dealer Code] = ap.[DealerCode] and b.[Rep Code] = ap.[RepCode]\n\nselect * from  Billing.QFM.AlliancePartnerAccountsDaily where  [IsApprovedDealerRepCode] = 'Yes'", "partitionOption": "None"}, "dataset": {"referenceName": "SQLMI_MOW_PARAM", "type": "DatasetReference", "parameters": {"DBName": "Billing", "SchemaName": "QFM", "TableName": "AlliancePartnerAccountsDaily"}}, "firstRowOnly": false}}, {"name": "Insert New Valid Accounts to AlliancePartnerAccounts", "description": "", "type": "Lookup", "dependsOn": [{"activity": "Update Valid Accounts Flag", "dependencyConditions": ["Succeeded"]}], "policy": {"timeout": "7.00:00:00", "retry": 0, "retryIntervalInSeconds": 30, "secureOutput": false, "secureInput": false}, "userProperties": [], "typeProperties": {"source": {"type": "SqlMISource", "sqlReaderQuery": "drop table if exists #newAccounts\nselect *\ninto #newAccounts\nfrom Billing.QFM.AlliancePartnerAccountsDaily\nwhere [IsApprovedDealerRepCode] ='Yes' and \nAccountNumber not in (select distinct AccountNumber from Billing.QFM.AlliancePartnerAccounts)\n\ninsert into Billing.QFM.AlliancePartnerAccounts\nselect * from  Billing.QFM.AlliancePartnerAccountsDaily\nwhere [IsApprovedDealerRepCode] ='Yes' and AccountNumber in (select distinct AccountNumber from #newAccounts)\n\nselect  * from #newAccounts\n", "partitionOption": "None"}, "dataset": {"referenceName": "SQLMI_MOW_PARAM", "type": "DatasetReference", "parameters": {"DBName": "Billing", "SchemaName": "QFM", "TableName": "AlliancePartnerAccounts"}}, "firstRowOnly": false}}, {"name": "Qry List of Invalid Accounts", "type": "Lookup", "dependsOn": [{"activity": "Update Valid Accounts Flag", "dependencyConditions": ["Succeeded"]}], "policy": {"timeout": "7.00:00:00", "retry": 0, "retryIntervalInSeconds": 30, "secureOutput": false, "secureInput": false}, "userProperties": [], "typeProperties": {"source": {"type": "SqlMISource", "sqlReaderQuery": "select * from Billing.QFM.AlliancePartnerAccountsDaily\nwhere [IsApprovedDealerRepCode] is null", "partitionOption": "None"}, "dataset": {"referenceName": "SQLMI_MOW_PARAM", "type": "DatasetReference", "parameters": {"DBName": "Billing", "SchemaName": "QFM", "TableName": "AlliancePartnerAccountsDaily"}}, "firstRowOnly": false}}, {"name": "Notification for Invalid Accounts", "type": "IfCondition", "dependsOn": [{"activity": "Qry List of Invalid Accounts", "dependencyConditions": ["Succeeded"]}], "userProperties": [], "typeProperties": {"expression": {"value": "@and(pipeline().parameters.SendEmail,greater(activity('Qry List of Invalid Accounts').output.count,0 ))", "type": "Expression"}, "ifTrueActivities": [{"name": "Send Notification Email", "type": "WebActivity", "dependsOn": [{"activity": "Send List of Invalid Account to ADL", "dependencyConditions": ["Succeeded"]}], "policy": {"timeout": "7.00:00:00", "retry": 0, "retryIntervalInSeconds": 30, "secureOutput": false, "secureInput": false}, "userProperties": [], "typeProperties": {"method": "POST", "url": "https://prod-63.westus.logic.azure.com:443/workflows/b03f5c7d88a343f3a9d1cc61f5e28c25/triggers/manual/paths/invoke?api-version=2016-10-01&sp=%2Ftriggers%2Fmanual%2Frun&sv=1.0&sig=EI8Tem1AoU0xS6Y99RAVQngONt9_OnFfwbRm5ImSygg", "body": {"value": "{\"ADL_File_With_Attachments\": \"scdintegration/Billing/QFM/Alliance Partner/@{variables('AttachmentFileName')}\",\n        \"Attachment_Name\": \"Alliance Partners Invalid Accounts.csv\",\n        \"Email_Body\": \"Please see attached list for Alliance Partner Accounts that does not have valid Dealer or Rep Code.\",\n        \"Email_Recipients\": \"<EMAIL>;<EMAIL>;<EMAIL>;<EMAIL>;<EMAIL>;<EMAIL>;<EMAIL>;<EMAIL>\",\n        \"Subject\": \"Alliance Partner - Invalid Accounts\"\n    }", "type": "Expression"}}}, {"name": "Send List of Invalid Account to ADL", "type": "Copy", "dependsOn": [{"activity": "Attachment File Name", "dependencyConditions": ["Succeeded"]}], "policy": {"timeout": "7.00:00:00", "retry": 0, "retryIntervalInSeconds": 30, "secureOutput": false, "secureInput": false}, "userProperties": [], "typeProperties": {"source": {"type": "SqlMISource", "sqlReaderQuery": "select * from QFM.AlliancePartnerAccountsDaily \nwhere [IsApprovedDealerRepCode] is null", "partitionOption": "None"}, "sink": {"type": "DelimitedTextSink", "storeSettings": {"type": "AzureBlobFSWriteSettings"}, "formatSettings": {"type": "DelimitedTextWriteSettings", "quoteAllText": true, "fileExtension": ".txt"}}, "enableStaging": false, "translator": {"type": "TabularTranslator", "typeConversion": true, "typeConversionSettings": {"allowDataTruncation": true, "treatBooleanAsNumber": false}}}, "inputs": [{"referenceName": "SQLMI_MOW_PARAM", "type": "DatasetReference", "parameters": {"DBName": "Billing", "SchemaName": "dbo", "TableName": "AlliancePartnerAccountsDaily"}}], "outputs": [{"referenceName": "ADL_Billing_Parameterized_v3", "type": "DatasetReference", "parameters": {"FilePath": "Billing/QFM/Alliance Partner", "FileName": {"value": "@variables('AttachmentFileName')", "type": "Expression"}}}]}, {"name": "Attachment File Name", "type": "SetVariable", "dependsOn": [], "policy": {"secureOutput": false, "secureInput": false}, "userProperties": [], "typeProperties": {"variableName": "AttachmentFileName", "value": {"value": "@concat('Invalid Alliance Partner Accounts_',pipeline().parameters.RunDate,'.csv')", "type": "Expression"}}}]}}, {"name": "Identify Alliance Portfolio from IMW to ADL_copy1_copy1", "type": "Copy", "dependsOn": [], "policy": {"timeout": "7.00:00:00", "retry": 0, "retryIntervalInSeconds": 30, "secureOutput": false, "secureInput": false}, "userProperties": [], "typeProperties": {"source": {"type": "AzureDataExplorerSource", "additionalColumns": [{"name": "LastUpdated", "value": {"value": "@utcNow()", "type": "Expression"}}, {"name": "UpdatedBy", "value": {"value": "@concat('ADF')", "type": "Expression"}}], "query": {"value": "AlliancePartnerAccounts(\"@{pipeline().parameters.RunDate}\")\n| distinct Rep_Code = ['Rep Code'] , Dealer_Code = ['Dealer Code'], Account_Number = ['Account Number'], Related_Partner, Customer_Name, Partner_Relation_Date, Customer_Number, Partner_Relation_Type, Province,Country, Valuation_Currency, Alt_Account_Number", "type": "Expression"}, "queryTimeout": "00:10:00", "noTruncation": false}, "sink": {"type": "SqlMISink", "preCopyScript": "TRUNCATE TABLE \nBilling.QFM.AlliancePartnerAccountsDaily", "writeBehavior": "insert", "sqlWriterUseTableLock": false}, "enableStaging": false, "translator": {"type": "TabularTranslator", "mappings": [{"source": {"name": "Rep_Code", "type": "String", "physicalType": "string"}, "sink": {"name": "RepCode", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}, {"source": {"name": "Dealer_Code", "type": "String", "physicalType": "string"}, "sink": {"name": "DealerCode", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}, {"source": {"name": "Account_Number", "type": "String", "physicalType": "string"}, "sink": {"name": "AccountNumber", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}, {"source": {"name": "Related_Partner", "type": "String", "physicalType": "string"}, "sink": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}, {"source": {"name": "Customer_Name", "type": "String", "physicalType": "string"}, "sink": {"name": "CustomerName", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}, {"source": {"name": "Partner_Relation_Date", "type": "DateTime", "physicalType": "datetime"}, "sink": {"name": "PartnerRelationDate", "type": "DateTime", "physicalType": "date"}}, {"source": {"name": "Partner_Relation_Type", "type": "String", "physicalType": "string"}, "sink": {"name": "PartnerRelationType", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}, {"source": {"name": "Province", "type": "String", "physicalType": "string"}, "sink": {"name": "Province", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}, {"source": {"name": "Country", "type": "String", "physicalType": "string"}, "sink": {"name": "Country", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}, {"source": {"name": "Valuation_Currency", "type": "String", "physicalType": "string"}, "sink": {"name": "ValuationCurrency", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}, {"source": {"name": "Alt_Account_Number", "type": "String", "physicalType": "string"}, "sink": {"name": "AltAccountNumber", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}, {"source": {"name": "LastUpdated", "type": "String"}, "sink": {"name": "LastUpdated", "type": "DateTime", "physicalType": "datetime"}}, {"source": {"name": "UpdatedBy", "type": "String"}, "sink": {"name": "UpdatedBy", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}], "typeConversion": true, "typeConversionSettings": {"allowDataTruncation": true, "treatBooleanAsNumber": false}}}, "inputs": [{"referenceName": "ADX_PROD", "type": "DatasetReference", "parameters": {"Database": "Billing", "Table": "BillablePortfolio"}}], "outputs": [{"referenceName": "SQLMI_MOW_PARAM", "type": "DatasetReference", "parameters": {"DBName": "Billing", "SchemaName": "QFM", "TableName": "AlliancePartnerAccountsDaily"}}]}, {"name": "Sync RSRCHMI AlliancePartnerAccounts tbl from MOW", "type": "Copy", "dependsOn": [{"activity": "Generate QFM Templates", "dependencyConditions": ["Succeeded"]}], "policy": {"timeout": "0.12:00:00", "retry": 0, "retryIntervalInSeconds": 30, "secureOutput": false, "secureInput": false}, "userProperties": [], "typeProperties": {"source": {"type": "SqlMISource", "partitionOption": "None"}, "sink": {"type": "SqlMISink", "preCopyScript": "TRUNCATE TABLE [Billing].[dbo].[QFM_AlliancePartnerAccounts]", "writeBehavior": "insert", "sqlWriterUseTableLock": false}, "enableStaging": false, "translator": {"type": "TabularTranslator", "typeConversion": true, "typeConversionSettings": {"allowDataTruncation": true, "treatBooleanAsNumber": false}}}, "inputs": [{"referenceName": "MOW_PARAM", "type": "DatasetReference", "parameters": {"DBName": "Billing", "SchemaName": "QFM", "TableName": "AlliancePartnerAccounts"}}], "outputs": [{"referenceName": "SQLMI_PRD", "type": "DatasetReference", "parameters": {"DBName": "Billing", "Schema": "dbo", "Table": "QFM_AlliancePartnerAccounts"}}]}], "parameters": {"RunDate": {"type": "string", "defaultValue": "********"}, "SendEmail": {"type": "bool", "defaultValue": false}}, "variables": {"SQLOutput": {"type": "String"}, "AttachmentFileName": {"type": "String"}}, "folder": {"name": "Internal/Billing and QFM/Alliance Partner"}, "annotations": [], "lastPublishTime": "2024-04-01T21:22:27Z"}, "type": "Microsoft.DataFactory/factories/pipelines"}