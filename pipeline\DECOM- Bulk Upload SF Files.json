{"name": "DECOM- Bulk Upload SF Files", "properties": {"activities": [{"name": "Set DateHour Variable", "type": "SetVariable", "dependsOn": [], "policy": {"secureOutput": false, "secureInput": false}, "userProperties": [], "typeProperties": {"variableName": "DateHours", "value": {"value": "@concat(formatDateTime(convertFromUtc(utcnow(),'Mountain Standard Time'),'MMddyyyy'),'_',formatDateTime(convertFromUtc(utcnow(),'Mountain Standard Time'),'hhmm'))", "type": "Expression"}}}, {"name": "Get Metadata From ADL", "type": "GetMetadata", "dependsOn": [{"activity": "Set DateHour Variable", "dependencyConditions": ["Succeeded"]}, {"activity": "Set Date Variable", "dependencyConditions": ["Succeeded"]}], "policy": {"timeout": "7.00:00:00", "retry": 0, "retryIntervalInSeconds": 30, "secureOutput": false, "secureInput": false}, "userProperties": [], "typeProperties": {"dataset": {"referenceName": "AzureDataLakeStorage_Portfolio_Binary_1", "type": "DatasetReference", "parameters": {"SourceDirectory": {"value": "@concat('Salesforce To SCD/Daily Loads/',variables('Date'))", "type": "Expression"}}}, "fieldList": ["childItems"], "storeSettings": {"type": "AzureBlobFSReadSettings", "recursive": true, "modifiedDatetimeStart": {"value": "@addMinutes(utcnow(), -15)", "type": "Expression"}, "modifiedDatetimeEnd": {"value": "@utcNow()", "type": "Expression"}, "enablePartitionDiscovery": false}, "formatSettings": {"type": "BinaryReadSettings"}}}, {"name": "Set Date Variable", "type": "SetVariable", "dependsOn": [], "policy": {"secureOutput": false, "secureInput": false}, "userProperties": [], "typeProperties": {"variableName": "Date", "value": {"value": "@concat(formatDateTime(convertFromUtc(utcnow(),'Mountain Standard Time'),'yyyyMMdd'))", "type": "Expression"}}}, {"name": "Filter Todays Files", "type": "Filter", "dependsOn": [{"activity": "Get Metadata From ADL", "dependencyConditions": ["Succeeded"]}], "userProperties": [], "typeProperties": {"items": {"value": "@activity('Get Metadata From ADL').output.childItems", "type": "Expression"}, "condition": {"value": "@contains(item().name,'.csv' )", "type": "Expression"}}}, {"name": "Loop Thru All Files", "type": "ForEach", "dependsOn": [{"activity": "Filter Todays Files", "dependencyConditions": ["Succeeded"]}], "userProperties": [], "typeProperties": {"items": {"value": "@activity('Filter Todays Files').output.Value", "type": "Expression"}, "activities": [{"name": "Copy File to SCD SFTP", "type": "Copy", "dependsOn": [], "policy": {"timeout": "7.00:00:00", "retry": 0, "retryIntervalInSeconds": 30, "secureOutput": false, "secureInput": false}, "userProperties": [], "typeProperties": {"source": {"type": "BinarySource", "storeSettings": {"type": "AzureBlobFSReadSettings", "recursive": true}, "formatSettings": {"type": "BinaryReadSettings"}}, "sink": {"type": "BinarySink", "storeSettings": {"type": "SftpWriteSettings", "operationTimeout": "01:00:00", "useTempFileRename": false}}, "enableStaging": false}, "inputs": [{"referenceName": "AzureDataLakeStorage_Portfolio_Binary", "type": "DatasetReference", "parameters": {"SourceDirectory": {"value": "@concat('Salesforce To SCD/Daily Loads/',formatDateTime(convertFromUtc(utcnow(),'Mountain Standard Time'),'yyyyMMdd'))", "type": "Expression"}, "FileName": {"value": "@item().name", "type": "Expression"}}}], "outputs": [{"referenceName": "SFTP_SCD_DIR_BINARY", "type": "DatasetReference", "parameters": {"Path": {"value": "@concat(pipeline().globalParameters.SCD_ENV,pipeline().globalParameters.SCD_SFTP_Portfolio)", "type": "Expression"}}}]}]}}], "variables": {"DateHours": {"type": "String"}, "Date": {"type": "String"}}, "folder": {"name": "Internal/SFtoSCD/SF-SCD Real Time"}, "annotations": [], "lastPublishTime": "2023-12-01T04:11:50Z"}, "type": "Microsoft.DataFactory/factories/pipelines"}