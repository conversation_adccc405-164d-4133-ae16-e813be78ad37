{"name": "DECOM- IN-180 Rimes No Revision - Parent_History", "properties": {"activities": [{"name": "Send to SCD", "type": "IfCondition", "dependsOn": [{"activity": "Copy latest file to IN", "dependencyConditions": ["Succeeded"]}, {"activity": "Copy from IN to OUT", "dependencyConditions": ["Succeeded"]}], "userProperties": [], "typeProperties": {"expression": {"value": "@pipeline().parameters.send_to_scd", "type": "Expression"}, "ifTrueActivities": [{"name": "Send to SCD Rimes", "type": "Copy", "dependsOn": [], "policy": {"timeout": "7.00:00:00", "retry": 8, "retryIntervalInSeconds": 30, "secureOutput": false, "secureInput": false}, "userProperties": [], "typeProperties": {"source": {"type": "DelimitedTextSource", "storeSettings": {"type": "AzureBlobFSReadSettings", "recursive": true, "enablePartitionDiscovery": false}, "formatSettings": {"type": "DelimitedTextReadSettings"}}, "sink": {"type": "DelimitedTextSink", "storeSettings": {"type": "SftpWriteSettings", "operationTimeout": "01:00:00", "useTempFileRename": false}, "formatSettings": {"type": "DelimitedTextWriteSettings", "quoteAllText": true, "fileExtension": ""}}, "enableStaging": false, "translator": {"type": "TabularTranslator", "typeConversion": true, "typeConversionSettings": {"allowDataTruncation": true, "treatBooleanAsNumber": false}}}, "inputs": [{"referenceName": "AzureDataLakeStorage_Rimes_File", "type": "DatasetReference", "parameters": {"dir_name": "OUT/@{variables('file_date')}", "file_name": {"value": "@pipeline().parameters.file_name", "type": "Expression"}}}], "outputs": [{"referenceName": "SFTP_SCD_RIMES_Parameterized_Directory", "type": "DatasetReference", "parameters": {"FilePath": {"value": "@concat(pipeline().globalParameters.SCD_ENV, '/ActiveData/Interfaces/In/SFTP/Rimes/Working')", "type": "Expression"}, "FileName": {"value": "@pipeline().parameters.file_name", "type": "Expression"}, "col_delim": ";"}}]}, {"name": "Send to SCD StaticData", "type": "Copy", "dependsOn": [], "policy": {"timeout": "7.00:00:00", "retry": 8, "retryIntervalInSeconds": 30, "secureOutput": false, "secureInput": false}, "userProperties": [], "typeProperties": {"source": {"type": "DelimitedTextSource", "storeSettings": {"type": "AzureBlobFSReadSettings", "recursive": true, "enablePartitionDiscovery": false}, "formatSettings": {"type": "DelimitedTextReadSettings"}}, "sink": {"type": "DelimitedTextSink", "storeSettings": {"type": "SftpWriteSettings", "operationTimeout": "01:00:00", "useTempFileRename": false}, "formatSettings": {"type": "DelimitedTextWriteSettings", "quoteAllText": true, "fileExtension": ""}}, "enableStaging": false, "translator": {"type": "TabularTranslator", "typeConversion": true, "typeConversionSettings": {"allowDataTruncation": true, "treatBooleanAsNumber": false}}}, "inputs": [{"referenceName": "AzureDataLakeStorage_Rimes_File", "type": "DatasetReference", "parameters": {"dir_name": "OUT/@{variables('file_date')}", "file_name": {"value": "@pipeline().parameters.file_name", "type": "Expression"}}}], "outputs": [{"referenceName": "SFTP_SCD_RIMES_Parameterized_Directory", "type": "DatasetReference", "parameters": {"FilePath": {"value": "@concat(pipeline().globalParameters.SCD_ENV, '/ActiveData/Interfaces/In/SFTP/STATIC_DATA/Working')", "type": "Expression"}, "FileName": {"value": "@pipeline().parameters.file_name", "type": "Expression"}, "col_delim": ";"}}]}]}}, {"name": "Copy from IN to OUT", "type": "Copy", "dependsOn": [{"activity": "Set file date", "dependencyConditions": ["Succeeded"]}], "policy": {"timeout": "7.00:00:00", "retry": 8, "retryIntervalInSeconds": 30, "secureOutput": false, "secureInput": false}, "userProperties": [], "typeProperties": {"source": {"type": "DelimitedTextSource", "storeSettings": {"type": "AzureBlobFSReadSettings", "recursive": true, "wildcardFolderPath": "IN/HISTORY_2021-2022_ALLFILES", "wildcardFileName": {"value": "@pipeline().parameters.file_name", "type": "Expression"}, "enablePartitionDiscovery": false}, "formatSettings": {"type": "DelimitedTextReadSettings"}}, "sink": {"type": "DelimitedTextSink", "storeSettings": {"type": "AzureBlobFSWriteSettings"}, "formatSettings": {"type": "DelimitedTextWriteSettings", "quoteAllText": true, "fileExtension": ".txt"}}, "enableStaging": false, "translator": {"type": "TabularTranslator", "typeConversion": true, "typeConversionSettings": {"allowDataTruncation": true, "treatBooleanAsNumber": false}}}, "inputs": [{"referenceName": "AzureDataLakeStorage_Rimes_Dir", "type": "DatasetReference", "parameters": {"dir_name": "IN/HISTORY_2021-2022_ALLFILES"}}], "outputs": [{"referenceName": "AzureDataLakeStorage_Rimes_File", "type": "DatasetReference", "parameters": {"dir_name": "OUT/@{variables('file_date')}", "file_name": {"value": "@pipeline().parameters.file_name", "type": "Expression"}}}]}, {"name": "Copy latest file to IN", "type": "Copy", "dependsOn": [{"activity": "Set file date", "dependencyConditions": ["Succeeded"]}], "policy": {"timeout": "7.00:00:00", "retry": 8, "retryIntervalInSeconds": 30, "secureOutput": false, "secureInput": false}, "userProperties": [], "typeProperties": {"source": {"type": "DelimitedTextSource", "storeSettings": {"type": "AzureBlobFSReadSettings", "recursive": false, "wildcardFolderPath": "IN/HISTORY_2021-2022_ALLFILES", "wildcardFileName": {"value": "@pipeline().parameters.file_name", "type": "Expression"}, "enablePartitionDiscovery": false}, "formatSettings": {"type": "DelimitedTextReadSettings"}}, "sink": {"type": "DelimitedTextSink", "storeSettings": {"type": "AzureBlobFSWriteSettings"}, "formatSettings": {"type": "DelimitedTextWriteSettings", "quoteAllText": true, "fileExtension": ".txt"}}, "enableStaging": false, "translator": {"type": "TabularTranslator", "typeConversion": true, "typeConversionSettings": {"allowDataTruncation": true, "treatBooleanAsNumber": false}}}, "inputs": [{"referenceName": "AzureDataLakeStorage_Rimes_Dir", "type": "DatasetReference", "parameters": {"dir_name": "IN/HISTORY_2021-2022_ALLFILES"}}], "outputs": [{"referenceName": "AzureDataLakeStorage_Rimes_File", "type": "DatasetReference", "parameters": {"dir_name": "IN/@{variables('file_date')}", "file_name": {"value": "@pipeline().parameters.file_name", "type": "Expression"}}}]}, {"name": "Copy from OUT to ADX", "type": "Copy", "dependsOn": [{"activity": "Send to SCD", "dependencyConditions": ["Succeeded"]}], "policy": {"timeout": "7.00:00:00", "retry": 8, "retryIntervalInSeconds": 30, "secureOutput": false, "secureInput": false}, "userProperties": [], "typeProperties": {"source": {"type": "DelimitedTextSource", "additionalColumns": [{"name": "FileName", "value": {"value": "@pipeline().parameters.file_name", "type": "Expression"}}, {"name": "ADFRunTimeUTC", "value": {"value": "@utcnow()", "type": "Expression"}}], "storeSettings": {"type": "AzureBlobFSReadSettings", "recursive": true, "enablePartitionDiscovery": false}, "formatSettings": {"type": "DelimitedTextReadSettings", "skipLineCount": 1}}, "sink": {"type": "AzureDataExplorerSink", "ingestionMappingName": ""}, "enableStaging": false, "translator": {"type": "TabularTranslator", "typeConversion": true, "typeConversionSettings": {"allowDataTruncation": true, "treatBooleanAsNumber": false}}}, "inputs": [{"referenceName": "AzureDataLakeStorage_Rimes_File", "type": "DatasetReference", "parameters": {"dir_name": "OUT/@{variables('file_date')}", "file_name": {"value": "@pipeline().parameters.file_name", "type": "Expression"}}}], "outputs": [{"referenceName": "ADX_RIMES_Index_Data", "type": "DatasetReference", "parameters": {"table_name": {"value": "@if(\n    contains(\n        pipeline().parameters.file_name,\n        '_IDX_'\n    ),\n    'RimesIdx',\n    if(\n        startswith(\n            pipeline().parameters.file_name,\n            'FTX'\n        ),\n        if(\n            contains(\n                pipeline().parameters.file_name,\n                '_SOD_'\n            ),\n            'RimesConsFISOD',\n            'RimesConsFINDO'\n        ),\n        if(\n            contains(\n                pipeline().parameters.file_name,\n                '_SOD_'\n            ),\n            'RimesConsSOD',\n            'RimesConsNDO'\n        )\n    )\n)", "type": "Expression"}}}]}, {"name": "Set file date", "type": "SetVariable", "dependsOn": [], "policy": {"secureOutput": false, "secureInput": false}, "userProperties": [], "typeProperties": {"variableName": "file_date", "value": {"value": "@last(split(\n            substring(pipeline().parameters.file_name, 0, lastindexof(pipeline().parameters.file_name, '.csv'))\n            , '_'\n            )\n      )", "type": "Expression"}}}, {"name": "Log in ADX", "type": "AzureDataExplorerCommand", "dependsOn": [{"activity": "Copy from OUT to ADX", "dependencyConditions": ["Succeeded"]}], "policy": {"timeout": "7.00:00:00", "retry": 8, "retryIntervalInSeconds": 30, "secureOutput": false, "secureInput": false}, "userProperties": [], "typeProperties": {"command": {"value": ".ingest inline into table RimesADFLog <|\n@{utcnow()},@{pipeline().parameters.origin_run_id},false,@{pipeline().parameters.file_name},@{first(split(pipeline().parameters.file_name, '_'))},@{first(skip(split(pipeline().parameters.file_name, '_'), 1))},@{if(contains(pipeline().parameters.file_name, '_CONS_'), 'CONS', 'IDX')},R", "type": "Expression"}, "commandTimeout": "00:20:00"}, "linkedServiceName": {"referenceName": "AzureDataExplorer", "type": "LinkedServiceReference", "parameters": {"DatabaseName": "RIMES Index Data"}}}, {"name": "Wait", "type": "Wait", "dependsOn": [{"activity": "Log in ADX", "dependencyConditions": ["Succeeded"]}], "userProperties": [], "typeProperties": {"waitTimeInSeconds": 1}}], "parameters": {"origin_run_id": {"type": "string"}, "send_to_scd": {"type": "bool"}, "file_name": {"type": "string"}}, "variables": {"file_date": {"type": "String"}}, "folder": {"name": "Internal/RIMES to SCD"}, "annotations": [], "lastPublishTime": "2023-12-01T04:12:15Z"}, "type": "Microsoft.DataFactory/factories/pipelines"}