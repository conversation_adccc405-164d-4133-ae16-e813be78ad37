{"name": "MI_STAGING", "properties": {"linkedServiceName": {"referenceName": "SQLMI_RESEARCH", "type": "LinkedServiceReference", "parameters": {"DBName": "STAGING"}}, "parameters": {"TableName": {"type": "string"}, "SchemaName": {"type": "string"}}, "folder": {"name": "Outbound/MI_DWH"}, "annotations": [], "type": "AzureSqlMITable", "schema": [], "typeProperties": {"schema": {"value": "@dataset().SchemaName", "type": "Expression"}, "table": {"value": "@dataset().TableName", "type": "Expression"}}}, "type": "Microsoft.DataFactory/factories/datasets"}