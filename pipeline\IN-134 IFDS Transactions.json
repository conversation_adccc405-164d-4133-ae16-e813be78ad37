{"name": "IN-134 IFDS Transactions", "properties": {"activities": [{"name": "Archive Folder", "type": "GetMetadata", "dependsOn": [], "policy": {"timeout": "0.12:00:00", "retry": 0, "retryIntervalInSeconds": 30, "secureOutput": false, "secureInput": false}, "userProperties": [], "typeProperties": {"dataset": {"referenceName": "ADL_Binary_Dir", "type": "DatasetReference", "parameters": {"Path": {"value": "@concat(pipeline().parameters.pADLPath,'/ARCHIVE/',pipeline().parameters.pDate,'/OUT')", "type": "Expression"}}}, "fieldList": ["exists"], "storeSettings": {"type": "AzureBlobFSReadSettings", "recursive": true, "enablePartitionDiscovery": false}, "formatSettings": {"type": "BinaryReadSettings"}}}, {"name": "If file is not in ARCHIVE", "type": "IfCondition", "dependsOn": [{"activity": "Archive Folder", "dependencyConditions": ["Succeeded"]}], "userProperties": [], "typeProperties": {"expression": {"value": "@not(activity('Archive Folder').output.exists)", "type": "Expression"}, "ifTrueActivities": [{"name": "Create Output File", "type": "Copy", "state": "Inactive", "onInactiveMarkAs": "Succeeded", "dependsOn": [{"activity": "Create Transactions records", "dependencyConditions": ["Succeeded"]}], "policy": {"timeout": "7.00:00:00", "retry": 0, "retryIntervalInSeconds": 30, "secureOutput": false, "secureInput": false}, "userProperties": [], "typeProperties": {"source": {"type": "SqlMISource", "sqlReaderStoredProcedureName": "[Integration].[ifds].[sp_IFDSTransactions]", "storedProcedureParameters": {"DataDate": {"type": "String", "value": {"value": "@pipeline().parameters.pDatePlus1D", "type": "Expression"}}}, "partitionOption": "None"}, "sink": {"type": "DelimitedTextSink", "storeSettings": {"type": "AzureBlobFSWriteSettings"}, "formatSettings": {"type": "DelimitedTextWriteSettings", "quoteAllText": true, "fileExtension": ".txt"}}, "enableStaging": false, "translator": {"type": "TabularTranslator", "typeConversion": true, "typeConversionSettings": {"allowDataTruncation": true, "treatBooleanAsNumber": false}}}, "inputs": [{"referenceName": "MOW_PARAM", "type": "DatasetReference", "parameters": {"DBName": "Integration", "SchemaName": "ifds", "TableName": "sp_IFDSTransactions"}}], "outputs": [{"referenceName": "ADL_Delimited_File", "type": "DatasetReference", "parameters": {"FilePath": {"value": "@concat(pipeline().parameters.pADLPath,'/OUT')", "type": "Expression"}, "FileName": {"value": "@concat('IFDS_TRN_OUT_',pipeline().parameters.pDate,'.csv')", "type": "Expression"}, "ColDelimiter": ",", "FirstRowHeader": true}}]}, {"name": "Create Transactions records Beta", "type": "Copy", "dependsOn": [{"activity": "Create Transactions records", "dependencyConditions": ["Succeeded"]}], "policy": {"timeout": "7.00:00:00", "retry": 0, "retryIntervalInSeconds": 30, "secureOutput": false, "secureInput": false}, "userProperties": [], "typeProperties": {"source": {"type": "AzureDataExplorerSource", "query": {"value": "@concat('fn_IFDS_Transactions_All(\"',pipeline().parameters.pDatePlus1D,'\")\n| project [\"Fund Company Code\"]\n,[\"Account Number\"]\n,[\"Security ID\"]\n,[\"Fund Code\"]\n,[\"From/To Account\"]\n,[\"From/To Fund Code\"]\n,[\"External transaction number\"]\n,[\"Trade date\"]\n,[\"Payment date\"]\n,[\"Transaction Type\"]\n,[\"Current value QC\"]\n,[\"Net Amount\"]\n,[\"Nominal/Units\"]\n,[\"FndSrv Order ID\"]\n,[\"Federal Tax\"]\n,[\"Provincial Tax\"]\n,[\"Deposit Code\"]\n,[\"Redemption Code\"]\n,[\"Original Transaction No.\"]\n,[\"Trans Cost\"]\n,[\"Custody\"]\n,[\"From/To Baycom Code\"]\n,[\"Custodian\"]\n,[\"Fund Class\"]\n,[\"From/To Fund Class\"]\n,[\"Income Distribution Rate\"]\n,[\"Capital Gains Distribution Rate\"]\n,[\"Record Date Units\"]\n,[\"Record Date\"]\n,[\"Account Tax Type\"]\n,[\"From/To Account Tax Type\"]\n,[\"Portfolio\"]\n,[\"IsCancel\"]\n,[\"SCD Order ID\"]\n,[\"SCD Portfolio\"]\n,[\"From/To Account Portfolio\"]\n,[\"SCD Portfolio Group\"]\n,[\"From/To Account Portfolio Group\"]\n,[\"Adjustment Flag\"]\n,[\"CNAV Extended NAV\"]\n,[\"NAV\"]\n,[\"Fed WHold Tax Rate\"]\n,[\"Amount Type\"]\n,[\"Transaction Fund Currency\"]\n,[\"Transaction Settlement Currency\"]\n,[\"Remarks 1\"]\n,[\"Remarks 2\"]\n,[\"Remarks 3\"]\n,[\"DataDate\"]')", "type": "Expression"}, "queryTimeout": "00:10:00", "noTruncation": false}, "sink": {"type": "SqlMISink", "preCopyScript": {"value": "@{concat(\n'DELETE FROM [Integration].[ifds].[IFDSTransactionsPublished_All_Fields] \nWHERE [DataDate] = ''',pipeline().parameters.pDatePlus1D,\n'''')}", "type": "Expression"}, "writeBehavior": "insert", "sqlWriterUseTableLock": false}, "enableStaging": false, "translator": {"type": "TabularTranslator", "mappings": [{"source": {"name": "Fund Company Code", "type": "String", "physicalType": "string"}, "sink": {"name": "Fund Company Code", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}, {"source": {"name": "Account Number", "type": "String", "physicalType": "string"}, "sink": {"name": "Account Number", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}, {"source": {"name": "Security ID", "type": "String", "physicalType": "string"}, "sink": {"name": "Security ID", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}, {"source": {"name": "Fund Code", "type": "String", "physicalType": "string"}, "sink": {"name": "Fund Code", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}, {"source": {"name": "From/To Account", "type": "String", "physicalType": "string"}, "sink": {"name": "From/To Account", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}, {"source": {"name": "From/To Fund Code", "type": "String", "physicalType": "string"}, "sink": {"name": "From/To Fund Code", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}, {"source": {"name": "External transaction number", "type": "String", "physicalType": "string"}, "sink": {"name": "External transaction number", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}, {"source": {"name": "Trade date", "type": "String", "physicalType": "string"}, "sink": {"name": "Trade date", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}, {"source": {"name": "Payment date", "type": "String", "physicalType": "string"}, "sink": {"name": "Payment date", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}, {"source": {"name": "Transaction Type", "type": "String", "physicalType": "string"}, "sink": {"name": "Transaction Type", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}, {"source": {"name": "Current value QC", "type": "String", "physicalType": "string"}, "sink": {"name": "Current value QC", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}, {"source": {"name": "Net Amount", "type": "String", "physicalType": "string"}, "sink": {"name": "Net Amount", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}, {"source": {"name": "Nominal/Units", "type": "String", "physicalType": "string"}, "sink": {"name": "Nominal/Units", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}, {"source": {"name": "FndSrv Order ID", "type": "String", "physicalType": "string"}, "sink": {"name": "FndSrv Order ID", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}, {"source": {"name": "Federal Tax", "type": "String", "physicalType": "string"}, "sink": {"name": "Federal Tax", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}, {"source": {"name": "Provincial Tax", "type": "String", "physicalType": "string"}, "sink": {"name": "Provincial Tax", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}, {"source": {"name": "Deposit Code", "type": "String", "physicalType": "string"}, "sink": {"name": "Deposit Code", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}, {"source": {"name": "Redemption Code", "type": "String", "physicalType": "string"}, "sink": {"name": "Redemption Code", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}, {"source": {"name": "Original Transaction No.", "type": "String", "physicalType": "string"}, "sink": {"name": "Original Transaction No.", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}, {"source": {"name": "Trans Cost", "type": "String", "physicalType": "string"}, "sink": {"name": "Trans Cost", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}, {"source": {"name": "<PERSON><PERSON><PERSON>", "type": "String", "physicalType": "string"}, "sink": {"name": "<PERSON><PERSON><PERSON>", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}, {"source": {"name": "From/To Baycom Code", "type": "String", "physicalType": "string"}, "sink": {"name": "From/To Baycom Code", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}, {"source": {"name": "<PERSON><PERSON><PERSON><PERSON>", "type": "String", "physicalType": "string"}, "sink": {"name": "<PERSON><PERSON><PERSON><PERSON>", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}, {"source": {"name": "Fund Class", "type": "String", "physicalType": "string"}, "sink": {"name": "Fund Class", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}, {"source": {"name": "From/To Fund Class", "type": "String", "physicalType": "string"}, "sink": {"name": "From/To Fund Class", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}, {"source": {"name": "Income Distribution Rate", "type": "String", "physicalType": "string"}, "sink": {"name": "Income Distribution Rate", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}, {"source": {"name": "Capital Gains Distribution Rate", "type": "String", "physicalType": "string"}, "sink": {"name": "Capital Gains Distribution Rate", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}, {"source": {"name": "Record Date Units", "type": "String", "physicalType": "string"}, "sink": {"name": "Record Date Units", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}, {"source": {"name": "Record Date", "type": "String", "physicalType": "string"}, "sink": {"name": "Record Date", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}, {"source": {"name": "Account Tax Type", "type": "String", "physicalType": "string"}, "sink": {"name": "Account Tax Type", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}, {"source": {"name": "From/To Account Tax Type", "type": "String", "physicalType": "string"}, "sink": {"name": "From/To Account Tax Type", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}, {"source": {"name": "Portfolio", "type": "String", "physicalType": "string"}, "sink": {"name": "Portfolio", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}, {"source": {"name": "IsCancel", "type": "String", "physicalType": "string"}, "sink": {"name": "IsCancel", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}, {"source": {"name": "SCD Order ID", "type": "String", "physicalType": "string"}, "sink": {"name": "SCD Order ID", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}, {"source": {"name": "SCD Portfolio", "type": "String", "physicalType": "string"}, "sink": {"name": "SCD Portfolio", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}, {"source": {"name": "From/To Account <PERSON>lio", "type": "String", "physicalType": "string"}, "sink": {"name": "From/To Account <PERSON>lio", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}, {"source": {"name": "SCD Portfolio Group", "type": "String", "physicalType": "string"}, "sink": {"name": "SCD Portfolio Group", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}, {"source": {"name": "From/To Account Portfolio Group", "type": "String", "physicalType": "string"}, "sink": {"name": "From/To Account Portfolio Group", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}, {"source": {"name": "Adjustment Flag", "type": "String", "physicalType": "string"}, "sink": {"name": "Adjustment Flag", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}, {"source": {"name": "CNAV Extended NAV", "type": "String", "physicalType": "string"}, "sink": {"name": "CNAV Extended NAV", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}, {"source": {"name": "NAV", "type": "String", "physicalType": "string"}, "sink": {"name": "NAV", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}, {"source": {"name": "Fed WHold Tax Rate", "type": "String", "physicalType": "string"}, "sink": {"name": "Fed WHold Tax Rate", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}, {"source": {"name": "Amount Type", "type": "String", "physicalType": "string"}, "sink": {"name": "Amount Type", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}, {"source": {"name": "Transaction Fund Currency", "type": "String", "physicalType": "string"}, "sink": {"name": "Transaction Fund Currency", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}, {"source": {"name": "Transaction Settlement Currency", "type": "String", "physicalType": "string"}, "sink": {"name": "Transaction Settlement Currency", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}, {"source": {"name": "Remarks 1", "type": "String", "physicalType": "string"}, "sink": {"name": "Remarks 1", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}, {"source": {"name": "Remarks 2", "type": "String", "physicalType": "string"}, "sink": {"name": "Remarks 2", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}, {"source": {"name": "Remarks 3", "type": "String", "physicalType": "string"}, "sink": {"name": "Remarks 3", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}, {"source": {"name": "DataDate", "type": "String", "physicalType": "string"}, "sink": {"name": "DataDate", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}], "typeConversion": true, "typeConversionSettings": {"allowDataTruncation": true, "treatBooleanAsNumber": false}}}, "inputs": [{"referenceName": "ADX_IFDS", "type": "DatasetReference", "parameters": {"Table": "IFDS_Transactions"}}], "outputs": [{"referenceName": "MOW_PARAM", "type": "DatasetReference", "parameters": {"DBName": "Integration", "SchemaName": "ifds", "TableName": "IFDSTransactionsPublished_All_Fields"}}]}, {"name": "sp_IFDSTransactions", "type": "SqlServerStoredProcedure", "dependsOn": [{"activity": "Create Output File", "dependencyConditions": ["Succeeded"]}], "policy": {"timeout": "0.12:00:00", "retry": 0, "retryIntervalInSeconds": 30, "secureOutput": false, "secureInput": false}, "userProperties": [], "typeProperties": {"storedProcedureName": "[ifds].[sp_IFDSTransactions]", "storedProcedureParameters": {"DataDate": {"value": {"value": "@pipeline().parameters.pDatePlus1D", "type": "Expression"}, "type": "String"}}}, "linkedServiceName": {"referenceName": "SQLMI_MOW", "type": "LinkedServiceReference", "parameters": {"DBName": "Integration"}}}, {"name": "Create Out File", "type": "Copy", "dependsOn": [{"activity": "Create Transactions records Beta", "dependencyConditions": ["Succeeded"]}, {"activity": "sp_IFDSTransactions", "dependencyConditions": ["Succeeded"]}], "policy": {"timeout": "7.00:00:00", "retry": 0, "retryIntervalInSeconds": 30, "secureOutput": false, "secureInput": false}, "userProperties": [], "typeProperties": {"source": {"type": "SqlMISource", "sqlReaderStoredProcedureName": "[Integration].[ifds].[sp_IFDSTransactions_TRN_OUT_SCD]", "storedProcedureParameters": {"DataDate": {"type": "String", "value": {"value": "@pipeline().parameters.pDatePlus1D", "type": "Expression"}}}, "partitionOption": "None"}, "sink": {"type": "DelimitedTextSink", "storeSettings": {"type": "AzureBlobFSWriteSettings"}, "formatSettings": {"type": "DelimitedTextWriteSettings", "quoteAllText": true, "fileExtension": ".txt"}}, "enableStaging": false, "translator": {"type": "TabularTranslator", "typeConversion": true, "typeConversionSettings": {"allowDataTruncation": true, "treatBooleanAsNumber": false}}}, "inputs": [{"referenceName": "MOW_PARAM", "type": "DatasetReference", "parameters": {"DBName": "Integration", "SchemaName": "ifds", "TableName": "sp_IFDSTransactions_TRN_OUT_SCD"}}], "outputs": [{"referenceName": "ADL_Delimited_File", "type": "DatasetReference", "parameters": {"FilePath": {"value": "@concat(pipeline().parameters.pADLPath,'/OUT')", "type": "Expression"}, "FileName": {"value": "@concat('IFDS_TRN_OUT_',pipeline().parameters.pDate,'.csv')", "type": "Expression"}, "ColDelimiter": ",", "FirstRowHeader": true}}]}, {"name": "Create Transactions records", "type": "Copy", "dependsOn": [], "policy": {"timeout": "7.00:00:00", "retry": 0, "retryIntervalInSeconds": 30, "secureOutput": false, "secureInput": false}, "userProperties": [], "typeProperties": {"source": {"type": "AzureDataExplorerSource", "query": {"value": "@concat('fn_IFDS_Transactions_All(\"',pipeline().parameters.pDatePlus1D,'\")\n| project [\"Fund Company Code\"]\n,[\"Account Number\"]\n,[\"Security ID\"]\n,[\"Fund Code\"]\n,[\"From/To Account\"]\n,[\"From/To Fund Code\"]\n,[\"External transaction number\"]\n,[\"Trade date\"]\n,[\"Payment date\"]\n,[\"Transaction Type\"]\n,[\"Current value QC\"]\n,[\"Net Amount\"]\n,[\"Nominal/Units\"]\n,[\"FndSrv Order ID\"]\n,[\"Federal Tax\"]\n,[\"Provincial Tax\"]\n,[\"Deposit Code\"]\n,[\"Redemption Code\"]\n,[\"Original Transaction No.\"]\n,[\"Trans Cost\"]\n,[\"Custody\"]\n,[\"From/To Baycom Code\"]\n,[\"Custodian\"]\n,[\"Fund Class\"]\n,[\"From/To Fund Class\"]\n,[\"Income Distribution Rate\"]\n,[\"Capital Gains Distribution Rate\"]\n,[\"Record Date Units\"]\n,[\"Record Date\"]\n,[\"Account Tax Type\"]\n,[\"From/To Account Tax Type\"]\n,[\"Portfolio\"]\n,[\"IsCancel\"]\n,[\"SCD Order ID\"]\n,[\"SCD Portfolio\"]\n,[\"From/To Account Portfolio\"]\n,[\"SCD Portfolio Group\"]\n,[\"From/To Account Portfolio Group\"]\n,[\"Adjustment Flag\"]\n,[\"CNAV Extended NAV\"]\n,[\"NAV\"]\n,[\"Fed WHold Tax Rate\"]\n,[\"Amount Type\"]\n,[\"Remarks 1\"]\n,[\"Remarks 2\"]\n,[\"Remarks 3\"]\n,[\"DataDate\"]')", "type": "Expression"}, "queryTimeout": "00:10:00", "noTruncation": false}, "sink": {"type": "SqlMISink", "preCopyScript": {"value": "@{concat(\n'DELETE FROM [Integration].[ifds].[IFDSTransactionsPublished] \nWHERE [DataDate] = ''',pipeline().parameters.pDatePlus1D,\n'''')}", "type": "Expression"}, "writeBehavior": "insert", "sqlWriterUseTableLock": false}, "enableStaging": false, "translator": {"type": "TabularTranslator", "mappings": [{"source": {"name": "Fund Company Code", "type": "String", "physicalType": "string"}, "sink": {"name": "Fund Company Code", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}, {"source": {"name": "Account Number", "type": "String", "physicalType": "string"}, "sink": {"name": "Account Number", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}, {"source": {"name": "Security ID", "type": "String", "physicalType": "string"}, "sink": {"name": "Security ID", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}, {"source": {"name": "Fund Code", "type": "String", "physicalType": "string"}, "sink": {"name": "Fund Code", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}, {"source": {"name": "From/To Account", "type": "String", "physicalType": "string"}, "sink": {"name": "From/To Account", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}, {"source": {"name": "From/To Fund Code", "type": "String", "physicalType": "string"}, "sink": {"name": "From/To Fund Code", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}, {"source": {"name": "External transaction number", "type": "String", "physicalType": "string"}, "sink": {"name": "External transaction number", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}, {"source": {"name": "Trade date", "type": "String", "physicalType": "string"}, "sink": {"name": "Trade date", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}, {"source": {"name": "Payment date", "type": "String", "physicalType": "string"}, "sink": {"name": "Payment date", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}, {"source": {"name": "Transaction Type", "type": "String", "physicalType": "string"}, "sink": {"name": "Transaction Type", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}, {"source": {"name": "Current value QC", "type": "String", "physicalType": "string"}, "sink": {"name": "Current value QC", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}, {"source": {"name": "Net Amount", "type": "String", "physicalType": "string"}, "sink": {"name": "Net Amount", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}, {"source": {"name": "Nominal/Units", "type": "String", "physicalType": "string"}, "sink": {"name": "Nominal/Units", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}, {"source": {"name": "FndSrv Order ID", "type": "String", "physicalType": "string"}, "sink": {"name": "FndSrv Order ID", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}, {"source": {"name": "Federal Tax", "type": "String", "physicalType": "string"}, "sink": {"name": "Federal Tax", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}, {"source": {"name": "Provincial Tax", "type": "String", "physicalType": "string"}, "sink": {"name": "Provincial Tax", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}, {"source": {"name": "Deposit Code", "type": "String", "physicalType": "string"}, "sink": {"name": "Deposit Code", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}, {"source": {"name": "Redemption Code", "type": "String", "physicalType": "string"}, "sink": {"name": "Redemption Code", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}, {"source": {"name": "Original Transaction No.", "type": "String", "physicalType": "string"}, "sink": {"name": "Original Transaction No.", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}, {"source": {"name": "Trans Cost", "type": "String", "physicalType": "string"}, "sink": {"name": "Trans Cost", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}, {"source": {"name": "<PERSON><PERSON><PERSON>", "type": "String", "physicalType": "string"}, "sink": {"name": "<PERSON><PERSON><PERSON>", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}, {"source": {"name": "From/To Baycom Code", "type": "String", "physicalType": "string"}, "sink": {"name": "From/To Baycom Code", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}, {"source": {"name": "<PERSON><PERSON><PERSON><PERSON>", "type": "String", "physicalType": "string"}, "sink": {"name": "<PERSON><PERSON><PERSON><PERSON>", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}, {"source": {"name": "Fund Class", "type": "String", "physicalType": "string"}, "sink": {"name": "Fund Class", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}, {"source": {"name": "From/To Fund Class", "type": "String", "physicalType": "string"}, "sink": {"name": "From/To Fund Class", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}, {"source": {"name": "Income Distribution Rate", "type": "String", "physicalType": "string"}, "sink": {"name": "Income Distribution Rate", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}, {"source": {"name": "Capital Gains Distribution Rate", "type": "String", "physicalType": "string"}, "sink": {"name": "Capital Gains Distribution Rate", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}, {"source": {"name": "Record Date Units", "type": "String", "physicalType": "string"}, "sink": {"name": "Record Date Units", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}, {"source": {"name": "Record Date", "type": "String", "physicalType": "string"}, "sink": {"name": "Record Date", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}, {"source": {"name": "Account Tax Type", "type": "String", "physicalType": "string"}, "sink": {"name": "Account Tax Type", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}, {"source": {"name": "From/To Account Tax Type", "type": "String", "physicalType": "string"}, "sink": {"name": "From/To Account Tax Type", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}, {"source": {"name": "Portfolio", "type": "String", "physicalType": "string"}, "sink": {"name": "Portfolio", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}, {"source": {"name": "IsCancel", "type": "String", "physicalType": "string"}, "sink": {"name": "IsCancel", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}, {"source": {"name": "SCD Order ID", "type": "String", "physicalType": "string"}, "sink": {"name": "SCD Order ID", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}, {"source": {"name": "SCD Portfolio", "type": "String", "physicalType": "string"}, "sink": {"name": "SCD Portfolio", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}, {"source": {"name": "From/To Account <PERSON>lio", "type": "String", "physicalType": "string"}, "sink": {"name": "From/To Account <PERSON>lio", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}, {"source": {"name": "SCD Portfolio Group", "type": "String", "physicalType": "string"}, "sink": {"name": "SCD Portfolio Group", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}, {"source": {"name": "From/To Account Portfolio Group", "type": "String", "physicalType": "string"}, "sink": {"name": "From/To Account Portfolio Group", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}, {"source": {"name": "Adjustment Flag", "type": "String", "physicalType": "string"}, "sink": {"name": "Adjustment Flag", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}, {"source": {"name": "CNAV Extended NAV", "type": "String", "physicalType": "string"}, "sink": {"name": "CNAV Extended NAV", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}, {"source": {"name": "NAV", "type": "String", "physicalType": "string"}, "sink": {"name": "NAV", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}, {"source": {"name": "Fed WHold Tax Rate", "type": "String", "physicalType": "string"}, "sink": {"name": "Fed WHold Tax Rate", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}, {"source": {"name": "Amount Type", "type": "String", "physicalType": "string"}, "sink": {"name": "Amount Type", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}, {"source": {"name": "Remarks 1", "type": "String", "physicalType": "string"}, "sink": {"name": "Remarks 1", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}, {"source": {"name": "Remarks 2", "type": "String", "physicalType": "string"}, "sink": {"name": "Remarks 2", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}, {"source": {"name": "Remarks 3", "type": "String", "physicalType": "string"}, "sink": {"name": "Remarks 3", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}, {"source": {"name": "DataDate", "type": "String", "physicalType": "string"}, "sink": {"name": "DataDate", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}], "typeConversion": true, "typeConversionSettings": {"allowDataTruncation": true, "treatBooleanAsNumber": false}}}, "inputs": [{"referenceName": "ADX_IFDS", "type": "DatasetReference", "parameters": {"Table": "IFDS_Transactions"}}], "outputs": [{"referenceName": "MOW_PARAM", "type": "DatasetReference", "parameters": {"DBName": "Integration", "SchemaName": "ifds", "TableName": "IFDSTransactionsPublished"}}]}]}}, {"name": "Get Metadata", "type": "GetMetadata", "dependsOn": [{"activity": "If file is not in ARCHIVE", "dependencyConditions": ["Succeeded"]}], "policy": {"timeout": "0.12:00:00", "retry": 0, "retryIntervalInSeconds": 30, "secureOutput": false, "secureInput": false}, "userProperties": [], "typeProperties": {"dataset": {"referenceName": "ADL_Billing_Parameterized", "type": "DatasetReference", "parameters": {"FilePath": {"value": "@concat(pipeline().parameters.pADLPath,'/OUT/IFDS_TRN_OUT_',pipeline().parameters.pDate,'.csv')", "type": "Expression"}}}, "fieldList": [{"value": "Size", "type": "Expression"}], "storeSettings": {"type": "AzureBlobFSReadSettings", "recursive": true, "enablePartitionDiscovery": false}, "formatSettings": {"type": "DelimitedTextReadSettings"}}}, {"name": "If Generated File is Empty", "type": "IfCondition", "dependsOn": [{"activity": "Get Metadata", "dependencyConditions": ["Succeeded"]}], "userProperties": [], "typeProperties": {"expression": {"value": "@and(greater(activity('Get Metadata').output.size,50000),\npipeline().parameters.pSendToSCD)", "type": "Expression"}, "ifTrueActivities": [{"name": "Copy File to SCD SFTP", "type": "Copy", "dependsOn": [], "policy": {"timeout": "7.00:00:00", "retry": 0, "retryIntervalInSeconds": 30, "secureOutput": false, "secureInput": false}, "userProperties": [], "typeProperties": {"source": {"type": "BinarySource", "storeSettings": {"type": "AzureBlobFSReadSettings", "recursive": true, "deleteFilesAfterCompletion": false}, "formatSettings": {"type": "BinaryReadSettings"}}, "sink": {"type": "BinarySink", "storeSettings": {"type": "SftpWriteSettings", "operationTimeout": "01:00:00", "useTempFileRename": true}}, "enableStaging": false}, "inputs": [{"referenceName": "ADL_Binary_Dir", "type": "DatasetReference", "parameters": {"Path": {"value": "@concat(pipeline().parameters.pADLPath,'/OUT')", "type": "Expression"}}}], "outputs": [{"referenceName": "SCD_SFTP_Binary_Dir", "type": "DatasetReference", "parameters": {"Path": {"value": "@pipeline().parameters.pSCDsftpPath", "type": "Expression"}}}]}]}}, {"name": "Archive OUT", "type": "Copy", "dependsOn": [{"activity": "If Generated File is Empty", "dependencyConditions": ["Succeeded"]}], "policy": {"timeout": "7.00:00:00", "retry": 0, "retryIntervalInSeconds": 30, "secureOutput": false, "secureInput": false}, "userProperties": [], "typeProperties": {"source": {"type": "BinarySource", "storeSettings": {"type": "AzureBlobFSReadSettings", "recursive": true, "deleteFilesAfterCompletion": true}, "formatSettings": {"type": "BinaryReadSettings"}}, "sink": {"type": "BinarySink", "storeSettings": {"type": "AzureBlobFSWriteSettings"}}, "enableStaging": false}, "inputs": [{"referenceName": "ADL_Binary_Dir", "type": "DatasetReference", "parameters": {"Path": {"value": "@concat(pipeline().parameters.pADLPath,'/OUT')", "type": "Expression"}}}], "outputs": [{"referenceName": "ADL_Binary_Dir", "type": "DatasetReference", "parameters": {"Path": {"value": "@concat(pipeline().parameters.pADLPath,'/ARCHIVE/',pipeline().parameters.pDate,'/OUT')", "type": "Expression"}}}]}], "parameters": {"pDate": {"type": "string"}, "pADLPath": {"type": "string", "defaultValue": "IFDS/IFDS Transactions"}, "pADLContainer": {"type": "string", "defaultValue": "scdintegration"}, "pSendToSCD": {"type": "bool", "defaultValue": true}, "pSCDsftpPath": {"type": "string"}, "pDataType": {"type": "string"}, "pDatePlus1D": {"type": "string"}}, "folder": {"name": "IFDS/IFDS Transactions"}, "annotations": [], "lastPublishTime": "2025-01-30T23:43:13Z"}, "type": "Microsoft.DataFactory/factories/pipelines"}