{"name": "DEPRECATED_Fidelity_SR", "properties": {"folder": {"name": "Inbound"}, "type": "MappingDataFlow", "typeProperties": {"sources": [{"dataset": {"referenceName": "DEPRECATED_ADL_Fidelity_ROOT_CSV", "type": "DatasetReference"}, "name": "FidelitySRRawIn"}, {"dataset": {"referenceName": "DEPRECATED_ADL_Fidelity_ROOT_CSV", "type": "DatasetReference"}, "name": "PortfolioCodeCustodianData"}, {"dataset": {"referenceName": "DEPRECATED_ADL_Fidelity_ROOT_CSV", "type": "DatasetReference"}, "name": "IMWOpenPortfolios"}], "sinks": [{"dataset": {"referenceName": "DEPRECATED_ADL_Fidelity_DIR", "type": "DatasetReference"}, "name": "OutputColumnsToCSV"}], "transformations": [{"name": "ParseColumns"}, {"name": "SelectColumns"}, {"name": "PortfolioCodeCustodianMapping", "description": "Creates an explicit mapping for each drifted column"}, {"name": "JoinWithSFCustodianData"}, {"name": "joinWithIMWOpenPortfolios"}], "script": "parameters{\n\tFileName as string\n}\nsource(output(\n\t\tColumn_1 as string\n\t),\n\tallowSchemaDrift: true,\n\tvalidateSchema: false,\n\tignoreNoFilesFound: false,\n\twildcardPaths:[(concat('Custodian Data/Fidelity/SR_StockRecordExtract/IN/', $FileName))]) ~> FidelitySRRawIn\nsource(output(\n\t\tSSID as long,\n\t\tCustodianAccount as string\n\t),\n\tallowSchemaDrift: true,\n\tvalidateSchema: false,\n\tignoreNoFilesFound: false,\n\twildcardPaths:[('Custodian Data/Static/PortfolioCodeCustodianMapping.csv')]) ~> PortfolioCodeCustodianData\nsource(output(\n\t\tPORTFOLIO as long\n\t),\n\tallowSchemaDrift: true,\n\tvalidateSchema: false,\n\tignoreNoFilesFound: false,\n\twildcardPaths:[('Custodian Data/Fidelity/STATIC/IMWOpenPortfolios.csv')]) ~> IMWOpenPortfolios\nFidelitySRRawIn derive(dataPhileCustomerCode = substring(Column_1, 1, 3),\n\t\tcusipNum = substring(Column_1, 4, 9),\n\t\tacctNum = substring(Column_1, 13, 8),\n\t\tiaNum = substring(Column_1, 21, 4),\n\t\tsecurityNm = substring(Column_1, 25, 30),\n\t\tsecurityType = substring(Column_1, 55, 1),\n\t\tsecType = case(substring(Column_1, 55, 1) == 'E', 'Equities and Fund Certs', 'Bonds and Money Market Products'),\n\t\tcurrencyCode = substring(Column_1, 56, 3),\n\t\tlastActivityDate = substring(Column_1, 59, 8),\n\t\tcurrentQuantity = toDouble(substring(Column_1, 67, 16))/10000,\n\t\tsfkQuantity = toDouble(substring(Column_1, 83, 16)),\n\t\tpendingQuantity = toDouble(substring(Column_1, 99, 16)),\n\t\tsecurityPrice = toDouble(substring(Column_1, 115, 16))/100000,\n\t\tloanRate = toDouble(substring(Column_1, 131, 16)),\n\t\tmarketVal = toDouble(substring(Column_1, 147, 16))/100,\n\t\tloanVal = toDouble(substring(Column_1, 163, 16)),\n\t\tbookVal = toDouble(substring(Column_1, 179, 16))/100000,\n\t\tunderlyingCUSIP = substring(Column_1, 195, 9),\n\t\tsymbolShort = substring(Column_1, 204, 6),\n\t\tmarketCode = substring(Column_1, 210, 2),\n\t\tsecurityCode = substring(Column_1, 212, 6),\n\t\tavgCost = toDouble(substring(Column_1, 218, 16)),\n\t\tpriceFactor = toDouble(substring(Column_1, 234, 10)),\n\t\tsymbolLong = substring(Column_1, 244, 21),\n\t\tfileName = $FileName) ~> ParseColumns\nJoinWithSFCustodianData select(mapColumn(\n\t\tdataPhileCustomerCode,\n\t\tcusipNum,\n\t\tacctNum,\n\t\tiaNum,\n\t\tsecurityNm,\n\t\tsecurityType,\n\t\tsecType,\n\t\tcurrencyCode,\n\t\tlastActivityDate,\n\t\tcurrentQuantity,\n\t\tsfkQuantity,\n\t\tpendingQuantity,\n\t\tsecurityPrice,\n\t\tloanRate,\n\t\tmarketVal,\n\t\tloanVal,\n\t\tbookVal,\n\t\tunderlyingCUSIP,\n\t\tsymbolShort,\n\t\tmarketCode,\n\t\tsecurityCode,\n\t\tavgCost,\n\t\tpriceFactor,\n\t\tsymbolLong,\n\t\tfileName,\n\t\tSSID\n\t),\n\tskipDuplicateMapInputs: true,\n\tskipDuplicateMapOutputs: true) ~> SelectColumns\njoinWithIMWOpenPortfolios derive(SSID = toString(byName('SSID')),\n\t\tCustodianAccount = toString(byName('CustodianAccount'))) ~> PortfolioCodeCustodianMapping\nParseColumns, PortfolioCodeCustodianMapping join(trim(acctNum) == trim(CustodianAccount),\n\tjoinType:'left',\n\tmatchType:'exact',\n\tignoreSpaces: false,\n\tbroadcast: 'auto')~> JoinWithSFCustodianData\nPortfolioCodeCustodianData, IMWOpenPortfolios join(SSID == PORTFOLIO,\n\tjoinType:'inner',\n\tmatchType:'exact',\n\tignoreSpaces: false,\n\tbroadcast: 'auto')~> joinWithIMWOpenPortfolios\nSelectColumns sink(allowSchemaDrift: true,\n\tvalidateSchema: false,\n\tinput(\n\t\tColumn_1 as string,\n\t\tColumn_2 as string,\n\t\tColumn_3 as string,\n\t\tColumn_4 as string,\n\t\tColumn_5 as string\n\t),\n\tpartitionFileNames:[('SrStockRecordExtractTemp.csv')],\n\tumask: 0022,\n\tpreCommands: [],\n\tpostCommands: [],\n\tskipDuplicateMapInputs: true,\n\tskipDuplicateMapOutputs: true,\n\tmapColumn(\n\t\tdataPhileCustomerCode,\n\t\tcusipNum,\n\t\tacctNum,\n\t\tiaNum,\n\t\tsecurityNm,\n\t\tsecurityType,\n\t\tsecType,\n\t\tcurrencyCode,\n\t\tlastActivityDate,\n\t\tcurrentQuantity,\n\t\tsfkQuantity,\n\t\tpendingQuantity,\n\t\tsecurityPrice,\n\t\tloanRate,\n\t\tmarketVal,\n\t\tloanVal,\n\t\tbookVal,\n\t\tunderlyingCUSIP,\n\t\tsymbolShort,\n\t\tmarketCode,\n\t\tsecurityCode,\n\t\tavgCost,\n\t\tpriceFactor,\n\t\tsymbolLong,\n\t\tfileName,\n\t\tSSID\n\t),\n\tpartitionBy('hash', 1)) ~> OutputColumnsToCSV"}}}