{"name": "MawerP1_SCDAT", "properties": {"linkedServiceName": {"referenceName": "MAWERP1", "type": "LinkedServiceReference"}, "parameters": {"SchemaName": {"type": "string", "defaultValue": "SCDAT"}, "TableName": {"type": "string"}}, "folder": {"name": "Outbound/MI_DWH"}, "annotations": [], "type": "OracleTable", "schema": [], "typeProperties": {"schema": {"value": "@dataset().SchemaName", "type": "Expression"}, "table": {"value": "@dataset().TableName", "type": "Expression"}}}, "type": "Microsoft.DataFactory/factories/datasets"}