{"name": "DF_IFDS_Capstock_Transform", "properties": {"folder": {"name": "Inbound"}, "type": "MappingDataFlow", "typeProperties": {"sources": [{"dataset": {"referenceName": "AzurDataLakeStorage_IFDS_CapstockTransform_CSV_DIR_FILE", "type": "DatasetReference"}, "name": "IFDSMawerFundCapstockTemp"}, {"dataset": {"referenceName": "AzurDataLakeStorage_IFDS_CapstockTransform_FundDim_CSV_DIR_FILE", "type": "DatasetReference"}, "name": "FundDim"}], "sinks": [{"dataset": {"referenceName": "AzurDataLakeStorage_IFDS_CapstockTransform_DIR", "type": "DatasetReference"}, "name": "GenerateCSVFile"}], "transformations": [{"name": "LeftOuterJoin"}, {"name": "FilterTotalRow"}, {"name": "TransformColumn"}, {"name": "SelectRawData"}, {"name": "FilterOnFinalTrfReq"}, {"name": "FilterSpPurRevMinusSpRedRev"}, {"name": "UnionAll"}, {"name": "groupeByFund"}, {"name": "TRNFileTransformation"}, {"name": "FilterOnTradeAmount"}, {"name": "TRNFileData"}], "script": "parameters{\n\tOutPutFileName as string\n}\nsource(output(\n\t\t{File Name} as string,\n\t\t{Custodian Name} as string,\n\t\t{Acct Grp Name} as string,\n\t\t{Current Date} as string,\n\t\t{Client Code} as string,\n\t\t{Company Code} as string,\n\t\tCurrency as string,\n\t\t{Cycle Date} as string,\n\t\t{Fund Name} as string,\n\t\tPricingLevel as string,\n\t\tFund as string,\n\t\t{FundCustodianRef#} as string,\n\t\tPur as string,\n\t\tExchIn as string,\n\t\tTfrIn as string,\n\t\tRed as string,\n\t\tExchOut as string,\n\t\tTfrOut as string,\n\t\tCashD<PERSON> as string,\n\t\tCashMF<PERSON> as string,\n\t\tAdm<PERSON>ee as string,\n\t\tMgmtFee as string,\n\t\t<PERSON>ur<PERSON><PERSON> as string,\n\t\tDlr<PERSON>ee as string,\n\t\tNRTax as string,\n\t\tNetCash as string,\n\t\t{N$MPur} as string,\n\t\t{N$MRed} as string,\n\t\t{N$MPurRev} as string,\n\t\t{N$MRedRev} as string,\n\t\tICTXls as string,\n\t\t{N$MRej} as string,\n\t\tICTXlsRev as string,\n\t\t{N$MRejRev} as string,\n\t\tDivOut as string,\n\t\tDivIn as string,\n\t\tSpPurRev as string,\n\t\tSpRedRev as string,\n\t\tDir<PERSON>ay<PERSON>ur as string,\n\t\t<PERSON><PERSON><PERSON> as string,\n\t\t<PERSON><PERSON><PERSON><PERSON> as string,\n\t\tFinalTrfReq as string\n\t),\n\tallowSchemaDrift: true,\n\tvalidateSchema: false,\n\tignoreNoFilesFound: false) ~> IFDSMawerFundCapstockTemp\nsource(output(\n\t\tStateStreetID as string,\n\t\tApxPortfolioCd as string\n\t),\n\tallowSchemaDrift: true,\n\tvalidateSchema: false,\n\tignoreNoFilesFound: false) ~> FundDim\nIFDSMawerFundCapstockTemp, FundDim join({FundCustodianRef#} == StateStreetID,\n\tjoinType:'left',\n\tmatchType:'exact',\n\tignoreSpaces: false,\n\tbroadcast: 'auto')~> LeftOuterJoin\nLeftOuterJoin filter(notEquals({FundCustodianRef#},'PH')==true()) ~> FilterTotalRow\nFilterTotalRow derive(FinalTrfReq = toDecimal(FinalTrfReq, 20, 2),\n\t\tSpPurRev = toDecimal(SpPurRev, 20, 2),\n\t\tSpRedRev = toDecimal(SpRedRev, 20, 2)) ~> TransformColumn\nTransformColumn select(mapColumn(\n\t\tfund_StateStreetID = {FundCustodianRef#},\n\t\tApxPortfolioCd,\n\t\t{Current Date},\n\t\tFinalTrfReq,\n\t\tSpPurRev,\n\t\tSpRedRev\n\t),\n\tskipDuplicateMapInputs: true,\n\tskipDuplicateMapOutputs: true) ~> SelectRawData\nSelectRawData filter(notEquals( toInteger(FinalTrfReq) ,0)==true()) ~> FilterOnFinalTrfReq\nSelectRawData filter(abs(SpPurRev)-abs(SpRedRev)!=0) ~> FilterSpPurRevMinusSpRedRev\nFilterSpPurRevMinusSpRedRev, FilterOnFinalTrfReq union(byName: true)~> UnionAll\nUnionAll aggregate(groupBy({Current Date},\n\t\tApxPortfolioCd),\n\tFinalTrfReq = sum(FinalTrfReq)) ~> groupeByFund\ngroupeByFund derive(ApxPortfolioCd = ApxPortfolioCd,\n\t\t{Transaction Type} = case(greater(toInteger(FinalTrfReq),0),'dp','wd'),\n\t\tC = '',\n\t\t{Security Type} = 'caca',\n\t\t{Security ID} = 'cash',\n\t\t{Trade Date} = {Current Date},\n\t\t{Settle Date} = {Current Date},\n\t\tH = '',\n\t\tI = '',\n\t\tJ = '',\n\t\tK = '',\n\t\t{Dest Type} = 'awca',\n\t\t{Dest Symbol} = 'client',\n\t\tN = '',\n\t\tO = '',\n\t\tP = '',\n\t\t{Mark to Market} = 'y',\n\t\t{Trade Amount} = abs(FinalTrfReq),\n\t\tS = '',\n\t\tT = '',\n\t\tU = '',\n\t\tV = '',\n\t\tW = '',\n\t\tX = '',\n\t\tY = '',\n\t\tZ = '',\n\t\tAA = '',\n\t\tAB = '',\n\t\tAC = '',\n\t\tAD = '',\n\t\tAE = '',\n\t\tAF = '',\n\t\tAG = '',\n\t\tAH = '',\n\t\tAI = '',\n\t\t{User Defined} = '37',\n\t\tAK = '',\n\t\tAL = '',\n\t\tAM = '',\n\t\tAN = '',\n\t\tAO = '',\n\t\tAP = '1',\n\t\tAQ = '',\n\t\tAR = '',\n\t\tAS = 'n',\n\t\tAT = '',\n\t\tAU = '') ~> TRNFileTransformation\nTRNFileTransformation filter(toInteger({Trade Amount}) !=0) ~> FilterOnTradeAmount\nFilterOnTradeAmount select(mapColumn(\n\t\t{Current Date},\n\t\tApxPortfolioCd,\n\t\t{Transaction Type},\n\t\tC,\n\t\t{Security Type},\n\t\t{Security ID},\n\t\t{Trade Date},\n\t\t{Settle Date},\n\t\tH,\n\t\tI,\n\t\tJ,\n\t\tK,\n\t\t{Dest Type},\n\t\t{Dest Symbol},\n\t\tN,\n\t\tO,\n\t\tP,\n\t\t{Mark to Market},\n\t\t{Trade Amount},\n\t\tS,\n\t\tT,\n\t\tU,\n\t\tV,\n\t\tW,\n\t\tX,\n\t\tY,\n\t\tZ,\n\t\tAA,\n\t\tAB,\n\t\tAC,\n\t\tAD,\n\t\tAE,\n\t\tAF,\n\t\tAG,\n\t\tAH,\n\t\tAI,\n\t\t{User Defined},\n\t\tAK,\n\t\tAL,\n\t\tAM,\n\t\tAN,\n\t\tAO,\n\t\tAP,\n\t\tAQ,\n\t\tAR,\n\t\tAS,\n\t\tAT,\n\t\tAU\n\t),\n\tskipDuplicateMapInputs: true,\n\tskipDuplicateMapOutputs: true) ~> TRNFileData\nTRNFileData sink(allowSchemaDrift: true,\n\tvalidateSchema: false,\n\tpartitionFileNames:[($OutPutFileName)],\n\tumask: 0022,\n\tpreCommands: [],\n\tpostCommands: [],\n\tskipDuplicateMapInputs: true,\n\tskipDuplicateMapOutputs: true,\n\tpartitionBy('hash', 1)) ~> GenerateCSVFile"}}}