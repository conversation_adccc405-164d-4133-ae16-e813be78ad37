{"name": "DECOM- IN-408 Mawer Allocation Flow", "properties": {"activities": [{"name": "Set Out File Name", "type": "SetVariable", "dependsOn": [], "policy": {"secureOutput": false, "secureInput": false}, "userProperties": [], "typeProperties": {"variableName": "FileName", "value": {"value": "@concat('Mawer Allocation_',formatDateTime(convertFromUtc(adddays(utcnow(),-1),'Mountain Standard Time'),'MMddyy'),'.txt')", "type": "Expression"}}}, {"name": "Copy data Allocation", "type": "Copy", "dependsOn": [{"activity": "Set Out File Name", "dependencyConditions": ["Succeeded"]}], "policy": {"timeout": "7.00:00:00", "retry": 0, "retryIntervalInSeconds": 30, "secureOutput": false, "secureInput": false}, "userProperties": [], "typeProperties": {"source": {"type": "SqlServerSource", "sqlReaderQuery": "Declare @startdate Datetime;\nset @Startdate=CAST(CURRENT_TIMESTAMP -1 AS DATE);\n\nDeclare @MAllocation table(\norderid varchar(100),\ntradeallocid  varchar(100),\noriginalorderid  varchar(100),\naccountid  varchar(100),\nallocationexecutingbroker  varchar(100),\nallocationbrokerreason  varchar(100),\nordertargetqty  varchar(100),\norderexecqty  varchar(100),\norderaverageprice  varchar(100),\norderprinamount  varchar(100), \nordercommissionamt  varchar(100),\nordertotalfees  varchar(100),\nordernetamount  varchar(100),\nallocationdirectedbroker  varchar(100),\nallocationdirectedbrokertype  varchar(100),\nallocationstepoutbroker  varchar(100),\nallocationclearingbroker  varchar(100),\ntradedate  varchar(100),\nassetclass1  varchar(100),\nassetclass2  varchar(100)\n)\n\nINSERT INTO @MAllocation\nSELECT\n\to.ORDER_ID orderid,\n\toa.TRADE_ID tradeallocid,\n\toa.ORIG_ORDER_ID originalorderid,\n\toa.ACCT_CD accountid,\n\toa.EXEC_BROKER allocationexecutingbroker,\n    oa.BROKER_REASON allocationbrokerreason,\n\toa.TARGET_QTY ordertargetqty,\n\toa.EXEC_QTY orderexecqty,\n\toa.EXEC_PRICE orderaverageprice,\n\toa.EXEC_AMT orderprinamount,\n\toa.COMMISION_AMT ordercommissionamt,\n\toa.FEE_1_AMT+oa.FEE_2_AMT+oa.FEE_3_AMT+oa.FEE_4_AMT+oa.FEE_5_AMT+oa.FEE_6_AMT ordertotalfees,\n\toa.NET_PRIN_AMT ordernetamount,\n\tcase\n\t\t when oa.DIR_EXEC_BROKER is null then oa.DIRECTED_BROKER\n\t\t else oa.DIR_EXEC_BROKER\n\tend allocationdirectedbroker,\n\tcase when oa.DIR_EXEC_BROKER is null then oa.DIRECTED_BROKER_TYPE\n\t\t else oa.DIR_EXEC_BROKER_TYPE \n    end allocationdirectedbrokertype,\n\toa.STEP_OUT_BROKER allocationstepoutbroker,\n\toa.CLEAR_BROKER allocationclearingbroker,\n\to.TRADE_DATE tradedate,\n\n\tCASE      WHEN oa.UDF_FLOAT1 = '1'   THEN 'Cash and Cash Equivalents'\n              WHEN oa.UDF_FLOAT1 = '2'   THEN 'Canadian Large Cap Equity'\n              WHEN oa.UDF_FLOAT1 = '3'   THEN 'Canadian Small Cap Equity'\n              WHEN oa.UDF_FLOAT1 = '4'   THEN 'US Equity'\n              WHEN oa.UDF_FLOAT1 = '5'   THEN 'International Equity'\n              WHEN oa.UDF_FLOAT1 = '6'   THEN 'Global  Equity'\n              WHEN oa.UDF_FLOAT1 = '7'   THEN 'Global Small Cap Equity'\n              WHEN oa.UDF_FLOAT1 = '8'   THEN 'Canadian Bonds'\n              WHEN oa.UDF_FLOAT1 = '9'   THEN 'Global Bonds'\n\t\t\t  Else NULL         \n   END AS [assetclass1],\n   \n   CASE\n              WHEN oa.UDF_FLOAT1 = '12'  THEN 'Other'\n              WHEN oa.UDF_FLOAT1 = '13'  THEN 'Emerging Markets'\n              WHEN oa.UDF_FLOAT1 = '14'  THEN 'EAFE Large Cap Equity'\n              WHEN oa.UDF_FLOAT1 = '15'  THEN 'US Mid Cap Equity'\t  \n\t\t\t  Else NULL\n\t\n END AS [assetclass2]\n  \nFROM\n\t\t[MWR-SRV-crSQL1A].[CRDMAINMAWIM].[dbo].[TS_ORDER] o,\n\t\t[MWR-SRV-crSQL1A].[CRDMAINMAWIM].[dbo].[TS_ORDER_ALLOC] oa   \n\t\tWHERE o.ORDER_ID= oa.ORDER_ID and o.TRADE_DATE>=@startdate\n\t\tand o.TRADE_DATE<=@startdate\n\n \n\n select orderid,tradeallocid,originalorderid,accountid,\t\n\t\tallocationexecutingbroker,allocationbrokerreason,ordertargetqty,\n\t\torderexecqty,orderaverageprice,orderprinamount,\tordercommissionamt,\n\t\tordertotalfees,\tordernetamount,\tallocationdirectedbroker,\t\n\t\tallocationdirectedbrokertype,allocationstepoutbroker,\n\t\tallocationclearingbroker,tradedate,\n\t\tCOALESCE(assetclass1,assetclass2) as [assetclass] \n\t\tfrom @MAllocation", "queryTimeout": "02:00:00", "partitionOption": "None"}, "sink": {"type": "DelimitedTextSink", "storeSettings": {"type": "AzureBlobFSWriteSettings"}, "formatSettings": {"type": "DelimitedTextWriteSettings", "quoteAllText": true, "fileExtension": ".txt"}}, "enableStaging": false, "translator": {"type": "TabularTranslator", "mappings": [{"source": {"name": "orderid", "type": "String"}, "sink": {"name": "orderid", "type": "String"}}, {"source": {"name": "tradeallocid", "type": "String"}, "sink": {"name": "tradeallocid", "type": "String"}}, {"source": {"name": "originalorderid", "type": "String"}, "sink": {"name": "originalorderid", "type": "String"}}, {"source": {"name": "accountid", "type": "String"}, "sink": {"name": "accountid", "type": "String"}}, {"source": {"name": "allocationexecutingbroker", "type": "String"}, "sink": {"name": "allocationexecutingbroker", "type": "String"}}, {"source": {"name": "allocationbroker<PERSON>son", "type": "String"}, "sink": {"name": "allocationbroker<PERSON>son", "type": "String"}}, {"source": {"name": "ordertargetqty", "type": "String"}, "sink": {"name": "ordertargetqty", "type": "String"}}, {"source": {"name": "orderexecqty", "type": "String"}, "sink": {"name": "orderexecqty", "type": "String"}}, {"source": {"name": "orderaverageprice", "type": "String"}, "sink": {"name": "orderaverageprice", "type": "String"}}, {"source": {"name": "orderprinamount", "type": "String"}, "sink": {"name": "orderprinamount", "type": "String"}}, {"source": {"name": "ordercommissionamt", "type": "String"}, "sink": {"name": "ordercommissionamt", "type": "String"}}, {"source": {"name": "ordertotalfees", "type": "String"}, "sink": {"name": "ordertotalfees", "type": "String"}}, {"source": {"name": "ordernetamount", "type": "String"}, "sink": {"name": "ordernetamount", "type": "String"}}, {"source": {"name": "allocationdirectedbroker", "type": "String"}, "sink": {"name": "allocationdirectedbroker", "type": "String"}}, {"source": {"name": "allocationdirectedbrokertype", "type": "String"}, "sink": {"name": "allocationdirectedbrokertype", "type": "String"}}, {"source": {"name": "allocationstepoutbroker", "type": "String"}, "sink": {"name": "allocationstepoutbroker", "type": "String"}}, {"source": {"name": "allocationclearingbroker", "type": "String"}, "sink": {"name": "allocationclearingbroker", "type": "String"}}, {"source": {"name": "tradedate", "type": "String"}, "sink": {"name": "tradedate", "type": "String"}}, {"source": {"name": "assetclass", "type": "String"}, "sink": {"name": "assetclass", "type": "String"}}]}}, "inputs": [{"referenceName": "ODSExternalData_MawerIHSMarkit_sql", "type": "DatasetReference"}], "outputs": [{"referenceName": "AzureDataLakeStorage_MawerIHSmarkitsink_csv", "type": "DatasetReference", "parameters": {"FileName": {"value": "@variables('FileName')", "type": "Expression"}}}]}, {"name": "Archive", "type": "Copy", "dependsOn": [{"activity": "Copy data Allocation", "dependencyConditions": ["Succeeded"]}], "policy": {"timeout": "7.00:00:00", "retry": 0, "retryIntervalInSeconds": 30, "secureOutput": false, "secureInput": false}, "userProperties": [], "typeProperties": {"source": {"type": "BinarySource", "storeSettings": {"type": "AzureBlobFSReadSettings", "recursive": false, "wildcardFolderPath": "DDI Migration/Mawer IHSMarkit - workflow/Outputfiles", "wildcardFileName": {"value": "@variables('FileName')", "type": "Expression"}, "deleteFilesAfterCompletion": true}, "formatSettings": {"type": "BinaryReadSettings"}}, "sink": {"type": "BinarySink", "storeSettings": {"type": "AzureBlobFSWriteSettings"}}, "enableStaging": false}, "inputs": [{"referenceName": "AzureDataLakeStorageMawerIHSMarkit_Binary_Source", "type": "DatasetReference"}], "outputs": [{"referenceName": "AzureDataLakeStorage_MawerIHSMarkit_Binary_Sink", "type": "DatasetReference"}]}], "variables": {"FileName": {"type": "String"}}, "folder": {"name": "Internal/DDI/IN-329 DDI Migration/IN-384 Mawer IHSMarkit - TransactionCostAnalysis - Workflow"}, "annotations": [], "lastPublishTime": "2023-12-01T04:11:29Z"}, "type": "Microsoft.DataFactory/factories/pipelines"}