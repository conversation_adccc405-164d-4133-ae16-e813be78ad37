{"name": "DEPRECATED_Cash Balance Mapping", "properties": {"folder": {"name": "Inbound"}, "type": "MappingDataFlow", "typeProperties": {"sources": [{"dataset": {"referenceName": "ADL_ScotiaTrust_IN", "type": "DatasetReference"}, "name": "RawCSV"}, {"dataset": {"referenceName": "ADL_ScotiaTrust_SFPortfolioCustodian", "type": "DatasetReference"}, "name": "PortfolioCodeCustodianData"}], "sinks": [{"dataset": {"referenceName": "ADL_ScotiaTrust_TEMP", "type": "DatasetReference"}, "name": "ParsedCSV"}], "transformations": [{"name": "Surrogate<PERSON><PERSON>"}, {"name": "RankBy<PERSON>ey"}, {"name": "FilterHeadersFooters"}, {"name": "SplitColumnByValue"}, {"name": "MapFields"}, {"name": "SelectColumns"}, {"name": "PortfolioCodeCustodianMapping", "description": "Creates an explicit mapping for each drifted column"}, {"name": "JoinWithCustodianMappingData"}], "script": "parameters{\n\tfileName as string\n}\nsource(output(\n\t\tvalue as string\n\t),\n\tallowSchemaDrift: true,\n\tvalidateSchema: false,\n\tignoreNoFilesFound: false,\n\twildcardPaths:[(concat('Custodian Data/ScotiaTrust/IN/',$fileName))]) ~> RawCSV\nsource(allowSchemaDrift: true,\n\tvalidateSchema: false,\n\tignoreNoFilesFound: false,\n\twildcardPaths:[('Custodian Data/Static/PortfolioCodeCustodianMapping.csv')]) ~> PortfolioCodeCustodianData\nRawCSV keyGenerate(output(rownum as long),\n\tstartAt: 1L,\n\tstepValue: 1L) ~> SurrogateKey\nSurrogateKey rank(desc(rownum, true),\n\toutput(endRank as long),\n\tdense: true) ~> RankByKey\nRankByKey filter(and(rownum>1,endRank>1)) ~> FilterHeadersFooters\nFilterHeadersFooters derive(splitCol = split(value,',')) ~> SplitColumnByValue\nSplitColumnByValue derive(AccountID = replace(splitCol[1],'\"'),\n\t\tAccountShortName = replace(splitCol[2],'\"'),\n\t\tAccountCurrency = replace(splitCol[3],'\"'),\n\t\tBalanceCurrency = iif(replace(splitCol[3],'\"') == '0', 'CAD', 'USD'),\n\t\tPositionDate = replace(concat(substring(replace(splitCol[4],'\"'), 1, 4) , '-', substring(replace(splitCol[4],'\"'), 5, 2), '-', substring(replace(splitCol[4],'\"'), 7, 2)  ), '-', ''),\n\t\tAccountRevenueFlag = replace(splitCol[5],'\"'),\n\t\tRevenueBalance = replace(splitCol[6],'\"'),\n\t\tAccountCapitalFlag = replace(splitCol[7],'\"'),\n\t\tCapitalBalance = replace(splitCol[8],'\"'),\n\t\tCombinedFlag = replace(splitCol[9],'\"'),\n\t\tTotalCombinedBalance = replace(splitCol[10],'\"'),\n\t\tSign = iif( toDouble(replace(splitCol[10],'\"')) < 0 , 'D', 'C' ),\n\t\tCanadianToUSConversionRate = replace(splitCol[11],'\"'),\n\t\tUSToCanadianConversionRate = replace(splitCol[12],'\"'),\n\t\tAccountType = replace(splitCol[13],'\"'),\n\t\tInvestmentOfficerCode = replace(splitCol[14],'\"'),\n\t\tSpaceFiller = replace(splitCol[15],'\"'),\n\t\tEntityCode = replace(splitCol[16],'\"'),\n\t\tfileName = $fileName) ~> MapFields\nJoinWithCustodianMappingData select(mapColumn(\n\t\tAccountID,\n\t\tAccountShortName,\n\t\tAccountCurrency,\n\t\tPositionDate,\n\t\tAccountRevenueFlag,\n\t\tRevenueBalance,\n\t\tAccountCapitalFlag,\n\t\tCapitalBalance,\n\t\tCombinedFlag,\n\t\tTotalCombinedBalance,\n\t\tCanadianToUSConversionRate,\n\t\tUSToCanadianConversionRate,\n\t\tAccountType,\n\t\tInvestmentOfficerCode,\n\t\tSpaceFiller,\n\t\tEntityCode,\n\t\tfileName,\n\t\tBalanceCurrency,\n\t\tSign,\n\t\tSSID\n\t),\n\tskipDuplicateMapInputs: true,\n\tskipDuplicateMapOutputs: true) ~> SelectColumns\nPortfolioCodeCustodianData derive(SSID = toString(byName('SSID')),\n\t\tCustodianAccount = toString(byName('CustodianAccount'))) ~> PortfolioCodeCustodianMapping\nMapFields, PortfolioCodeCustodianMapping join(AccountID == CustodianAccount,\n\tjoinType:'left',\n\tmatchType:'exact',\n\tignoreSpaces: false,\n\tbroadcast: 'auto')~> JoinWithCustodianMappingData\nSelectColumns sink(allowSchemaDrift: true,\n\tvalidateSchema: false,\n\tinput(\n\t\tColumn_1 as string,\n\t\tColumn_2 as string,\n\t\tColumn_3 as string\n\t),\n\tpartitionFileNames:['CashBalance.csv'],\n\tumask: 0022,\n\tpreCommands: [],\n\tpostCommands: [],\n\tskipDuplicateMapInputs: true,\n\tskipDuplicateMapOutputs: true,\n\tpartitionBy('hash', 1)) ~> ParsedCSV"}}}