{"name": "DEPRECATED_Holdings Mapping", "properties": {"folder": {"name": "Inbound"}, "type": "MappingDataFlow", "typeProperties": {"sources": [{"dataset": {"referenceName": "ADL_ScotiaTrust_IN", "type": "DatasetReference"}, "name": "RawCSV"}, {"dataset": {"referenceName": "ADL_ScotiaTrust_SFPortfolioCustodian", "type": "DatasetReference"}, "name": "PortfolioCodeCustodianData"}], "sinks": [{"dataset": {"referenceName": "ADL_ScotiaTrust_TEMP", "type": "DatasetReference"}, "name": "ParsedCSV"}], "transformations": [{"name": "Surrogate<PERSON><PERSON>"}, {"name": "RankBy<PERSON>ey"}, {"name": "FilterHeadersFooters"}, {"name": "SplitColumnByValue"}, {"name": "MapFields"}, {"name": "SelectColumns"}, {"name": "FilterOutCashBalance"}, {"name": "PortfolioCodeCustodianMapping", "description": "Creates an explicit mapping for each drifted column"}, {"name": "JoinWithSFCustodianData"}], "script": "parameters{\n\tfileName as string ('Mmhpd31118e.taaammhd')\n}\nsource(output(\n\t\tvalue as string\n\t),\n\tallowSchemaDrift: true,\n\tvalidateSchema: false,\n\tignoreNoFilesFound: false,\n\twildcardPaths:[(concat('Custodian Data/ScotiaTrust/IN/',$fileName))]) ~> RawCSV\nsource(allowSchemaDrift: true,\n\tvalidateSchema: false,\n\tignoreNoFilesFound: false,\n\twildcardPaths:[('Custodian Data/Static/PortfolioCodeCustodianMapping.csv')]) ~> PortfolioCodeCustodianData\nRawCSV keyGenerate(output(rownum as long),\n\tstartAt: 1L,\n\tstepValue: 1L) ~> SurrogateKey\nSurrogateKey rank(desc(rownum, true),\n\toutput(endRank as long),\n\tdense: true) ~> RankByKey\nRankByKey filter(and(rownum>1,endRank>1)) ~> FilterHeadersFooters\nFilterHeadersFooters derive(splitCol = split(value,',')) ~> SplitColumnByValue\nSplitColumnByValue derive(AccountID = replace(splitCol[1],'\"'),\n\t\tAccountShortName = replace(splitCol[2],'\"'),\n\t\tAsOfDate = replace(splitCol[3],'\"'),\n\t\tSecurityNumber = replace(splitCol[4],'\"'),\n\t\tCUSIP = replace(splitCol[5],'\"'),\n\t\tISIN = replace(splitCol[6],'\"'),\n\t\tTickerSymbol = replace(splitCol[7],'\"'),\n\t\t{Sec ID} = case(length(trim(replace(splitCol[6],'\"'))) > 0 , replace(splitCol[6],'\"'), length(trim(replace(splitCol[5],'\"'))) > 0 , replace(splitCol[5],'\"'), length(trim(replace(splitCol[7],'\"'))) > 0 , replace(splitCol[7],'\"')),\n\t\tSecuritySpin = replace(splitCol[8],'\"'),\n\t\tSecType = case(replace(splitCol[8],'\"') == 'A', 'Equity',replace(splitCol[8],'\"') == 'X', 'Money Market',replace(splitCol[8],'\"') == 'S', 'Bond',replace(splitCol[8],'\"') == 'N', 'ADR',replace(splitCol[8],'\"') == 'F', 'Mutual Fund'),\n\t\tSecurityClassCode = replace(splitCol[9],'\"'),\n\t\tIDACode = replace(splitCol[10],'\"'),\n\t\tSecurityCurrency = replace(splitCol[11],'\"'),\n\t\tSecurityDescription = replace(splitCol[12],'\"'),\n\t\tSecurityMaturityDate = replace(splitCol[13],'\"'),\n\t\tInterestRate = replace(splitCol[14],'\"'),\n\t\tMarketPrice = replace(splitCol[15],'\"'),\n\t\tQuantity = replace(splitCol[16],'\"'),\n\t\tCDNBookValue = replace(splitCol[17],'\"'),\n\t\tCDNMarketValue = replace(splitCol[18],'\"'),\n\t\tMV = iif(startsWith(replace(splitCol[6],'\"'), 'US'), replace(splitCol[20],'\"'),  replace(splitCol[18],'\"')),\n\t\tUSBookValue = replace(splitCol[19],'\"'),\n\t\tUSMarketValue = replace(splitCol[20],'\"'),\n\t\tBV = case(replace(splitCol[11],'\"') == '0',replace(splitCol[17],'\"'), replace(splitCol[11],'\"') == '1', replace(splitCol[19],'\"')),\n\t\tConversionRateCDNToUS = replace(splitCol[21],'\"'),\n\t\tConversionRateUSToCDN = replace(splitCol[22],'\"'),\n\t\tAccountType = replace(splitCol[23],'\"'),\n\t\tInvestmentOfficerCode = replace(splitCol[24],'\"'),\n\t\tAssetLocation = replace(splitCol[25],'\"'),\n\t\tSecuritySpecialCharacterstics = replace(splitCol[26],'\"'),\n\t\tSpaceFiller = replace(splitCol[27],'\"'),\n\t\tEntityCode = replace(splitCol[28],'\"'),\n\t\tfileName = $fileName,\n\t\tUSDCAD = 1/toDouble(replace(splitCol[21],'\"')),\n\t\tCADUSD = replace(splitCol[21],'\"'),\n\t\tSecuritySpecialCharac = replace(splitCol[26],'\"'),\n\t\tFileName = $fileName) ~> MapFields\nJoinWithSFCustodianData select(mapColumn(\n\t\tAccountID,\n\t\tAccountShortName,\n\t\tAsOfDate,\n\t\tSecurityNumber,\n\t\tCUSIP,\n\t\tISIN,\n\t\tTickerSymbol,\n\t\tSecuritySpin,\n\t\tSecurityClassCode,\n\t\tIDACode,\n\t\tSecurityCurrency,\n\t\tSecurityDescription,\n\t\tSecurityMaturityDate,\n\t\tInterestRate,\n\t\tMarketPrice,\n\t\tQuantity,\n\t\tCDNBookValue,\n\t\tCDNMarketValue,\n\t\tUSBookValue,\n\t\tUSMarketValue,\n\t\tConversionRateCDNToUS,\n\t\tConversionRateUSToCDN,\n\t\tAccountType,\n\t\tInvestmentOfficerCode,\n\t\tAssetLocation,\n\t\tSecuritySpecialCharacterstics,\n\t\tSpaceFiller,\n\t\tEntityCode,\n\t\tfileName = MapFields@fileName,\n\t\tCADUSD,\n\t\tUSDCAD,\n\t\tSecuritySpecialCharac,\n\t\tFileName = MapFields@FileName,\n\t\tSecType,\n\t\tMV,\n\t\t{Sec ID},\n\t\tSSID,\n\t\tBV\n\t),\n\tskipDuplicateMapInputs: true,\n\tskipDuplicateMapOutputs: true) ~> SelectColumns\nMapFields filter(SecurityDescription != 'CASH BALANCE') ~> FilterOutCashBalance\nPortfolioCodeCustodianData derive(SSID = toString(byName('SSID')),\n\t\tCustodianAccount = toString(byName('CustodianAccount'))) ~> PortfolioCodeCustodianMapping\nFilterOutCashBalance, PortfolioCodeCustodianMapping join(AccountID == CustodianAccount,\n\tjoinType:'left',\n\tmatchType:'exact',\n\tignoreSpaces: false,\n\tbroadcast: 'auto')~> JoinWithSFCustodianData\nSelectColumns sink(allowSchemaDrift: true,\n\tvalidateSchema: false,\n\tinput(\n\t\tColumn_1 as string,\n\t\tColumn_2 as string,\n\t\tColumn_3 as string\n\t),\n\tpartitionFileNames:['Holdings.csv'],\n\tumask: 0022,\n\tpreCommands: [],\n\tpostCommands: [],\n\tskipDuplicateMapInputs: true,\n\tskipDuplicateMapOutputs: true,\n\tpartitionBy('hash', 1)) ~> ParsedCSV"}}}