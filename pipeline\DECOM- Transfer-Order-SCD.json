{"name": "DECOM- Transfer-Order-SCD", "properties": {"activities": [{"name": "Send Order Response To SCD", "type": "Copy", "dependsOn": [], "policy": {"timeout": "7.00:00:00", "retry": 0, "retryIntervalInSeconds": 30, "secureOutput": false, "secureInput": false}, "userProperties": [], "typeProperties": {"source": {"type": "BinarySource", "storeSettings": {"type": "AzureBlobFSReadSettings", "recursive": false, "wildcardFolderPath": {"value": "@concat('TFS/OutgoingToSCD/IN')", "type": "Expression"}, "wildcardFileName": "*", "deleteFilesAfterCompletion": false}, "formatSettings": {"type": "BinaryReadSettings"}}, "sink": {"type": "BinarySink", "storeSettings": {"type": "SftpWriteSettings", "operationTimeout": "01:00:00", "useTempFileRename": true}}, "enableStaging": false}, "inputs": [{"referenceName": "OrderTransferFile", "type": "DatasetReference", "parameters": {"DirectoryName": {"value": "@concat('TFS/OutgoingToSCD/IN')", "type": "Expression"}}}], "outputs": [{"referenceName": "SCDSFTP_CIT_CLientTrade_Binary", "type": "DatasetReference", "parameters": {"SCD_SFTP_CIT": {"value": "@concat(pipeline().globalParameters.SCD_ENV,pipeline().globalParameters.SCD_SFTP_FUNDSERV)", "type": "Expression"}}}]}, {"name": "Archive Response sent to SCD", "type": "Copy", "dependsOn": [{"activity": "Send Order Response To SCD", "dependencyConditions": ["Succeeded"]}], "policy": {"timeout": "7.00:00:00", "retry": 0, "retryIntervalInSeconds": 30, "secureOutput": false, "secureInput": false}, "userProperties": [], "typeProperties": {"source": {"type": "BinarySource", "storeSettings": {"type": "AzureBlobFSReadSettings", "recursive": false, "wildcardFolderPath": "TFS/OutgoingToSCD/IN", "wildcardFileName": "*", "deleteFilesAfterCompletion": true}, "formatSettings": {"type": "BinaryReadSettings"}}, "sink": {"type": "BinarySink", "storeSettings": {"type": "AzureBlobFSWriteSettings"}}, "enableStaging": false}, "inputs": [{"referenceName": "OrderTransferFile", "type": "DatasetReference", "parameters": {"DirectoryName": "TFS/OutgoingToSCD/IN"}}], "outputs": [{"referenceName": "OrderTransferFile", "type": "DatasetReference", "parameters": {"DirectoryName": {"value": "@concat('TFS/OutgoingToSCD/ARCHIVE/',utcnow('yyyyMMdd'))", "type": "Expression"}}}]}], "folder": {"name": "Internal/SFtoSCD"}, "annotations": [], "lastPublishTime": "2023-12-15T00:47:42Z"}, "type": "Microsoft.DataFactory/factories/pipelines"}