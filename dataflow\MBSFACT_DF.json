{"name": "MBSFACT_DF", "properties": {"folder": {"name": "Inbound"}, "type": "MappingDataFlow", "typeProperties": {"sources": [{"dataset": {"referenceName": "ODSExternalData_MBSFACTPIPE_csv", "type": "DatasetReference"}, "name": "SourceFileOfMbsFacts"}], "sinks": [{"dataset": {"referenceName": "AzureDataLakeStorage_MBSFactOutFile_csv", "type": "DatasetReference"}, "name": "GenerateOutputFile"}], "transformations": [{"name": "CalculateOriginalWAM"}, {"name": "SetOrderOfDataCols"}], "script": "parameters{\n\toutPutFileName as string,\n\tinputFileName as string\n}\nsource(output(\n\t\tCurrentDate as string,\n\t\tInstrumentSEDOL as string,\n\t\tInstrumentISIN as string,\n\t\tInstrumentCUSIP as string,\n\t\tSymbol as string,\n\t\tSecurityNm as string,\n\t\tMawerSecurityNm as string,\n\t\tBBIssuerNm as string,\n\t\tISSUER as string,\n\t\tIndustryGroup as string,\n\t\tMawerAssetClassTier1Nm as string,\n\t\tMawerAssetClassTier2Nm as string,\n\t\tMawerAssetClassTier3Nm as string,\n\t\tISSUE_DT as string,\n\t\tMATURI<PERSON> as string,\n\t\tMTG_WAM as string,\n\t\tMTG_WALA_CALC as string,\n\t\tMTG_WACPN as string,\n\t\tMTG_PAY_DELAY as string,\n\t\tMTG_WAL as string,\n\t\tRTG_DBRS as string,\n\t\tRTG_SP as string,\n\t\tRTG_MOODY as string,\n\t\tYLD_YTM_MID as string,\n\t\tYLD_CUR_MID as string,\n\t\tDUR_ADJ_MID as string,\n\t\tCPN as string,\n\t\tCNVX_MID as string,\n\t\tsecuritystatuscd as string,\n\t\tCurrency as string,\n\t\tCouponType as string,\n\t\tFactor as string,\n\t\tFactorData as string\n\t),\n\tallowSchemaDrift: true,\n\tvalidateSchema: false,\n\tignoreNoFilesFound: false,\n\tmoveFiles: [(concat('DDI Migration/MBSFactSet/Temp/' ,$inputFileName)),(concat('DDI Migration/MBSFactSet/Archive/',toString(currentUTC(),'yyyy-MM-dd'),\"/Temp/\" ,$inputFileName))]) ~> SourceFileOfMbsFacts\nSourceFileOfMbsFacts derive(OriginalWAM = toDecimal(round(toDecimal(minus(toDate(MATURITY, 'yyyyMMdd'),toDate(ISSUE_DT, 'yyyyMMdd'))/365,10,0))*12,3,0)) ~> CalculateOriginalWAM\nCalculateOriginalWAM select(mapColumn(\n\t\tCurrentDate,\n\t\tInstrumentSEDOL,\n\t\tInstrumentISIN,\n\t\tInstrumentCUSIP,\n\t\tSymbol,\n\t\tSecurityNm,\n\t\tMawerSecurityNm,\n\t\tBBIssuerNm,\n\t\tISSUER,\n\t\tIndustryGroup,\n\t\tMawerAssetClassTier1Nm,\n\t\tMawerAssetClassTier2Nm,\n\t\tMawerAssetClassTier3Nm,\n\t\tISSUE_DT,\n\t\tMATURITY,\n\t\tOriginalWAM,\n\t\tMTG_WAM,\n\t\tMTG_WALA_CALC,\n\t\tMTG_WACPN,\n\t\tMTG_PAY_DELAY,\n\t\tMTG_WAL,\n\t\tRTG_DBRS,\n\t\tRTG_SP,\n\t\tRTG_MOODY,\n\t\tYLD_YTM_MID,\n\t\tYLD_CUR_MID,\n\t\tDUR_ADJ_MID,\n\t\tCPN,\n\t\tCNVX_MID,\n\t\tsecuritystatuscd,\n\t\tCurrency,\n\t\tCouponType,\n\t\tFactor,\n\t\tFactorData\n\t),\n\tskipDuplicateMapInputs: true,\n\tskipDuplicateMapOutputs: true) ~> SetOrderOfDataCols\nSetOrderOfDataCols sink(allowSchemaDrift: true,\n\tvalidateSchema: false,\n\tinput(\n\t\tColumn_1 as string,\n\t\tColumn_2 as string,\n\t\tColumn_3 as string,\n\t\tColumn_4 as string,\n\t\tColumn_5 as string,\n\t\tColumn_6 as string,\n\t\tColumn_7 as string,\n\t\tColumn_8 as string,\n\t\tColumn_9 as string,\n\t\tColumn_10 as string,\n\t\tColumn_11 as string,\n\t\tColumn_12 as string,\n\t\tColumn_13 as string,\n\t\tColumn_14 as string,\n\t\tColumn_15 as string,\n\t\tColumn_16 as string,\n\t\tColumn_17 as string,\n\t\tColumn_18 as string,\n\t\tColumn_19 as string,\n\t\tColumn_20 as string,\n\t\tColumn_21 as string,\n\t\tColumn_22 as string,\n\t\tColumn_23 as string,\n\t\tColumn_24 as string,\n\t\tColumn_25 as string,\n\t\tColumn_26 as string,\n\t\tColumn_27 as string,\n\t\tColumn_28 as string,\n\t\tColumn_29 as string,\n\t\tColumn_30 as string,\n\t\tColumn_31 as string,\n\t\tColumn_32 as string,\n\t\tColumn_33 as string,\n\t\tColumn_34 as string,\n\t\tColumn_35 as string,\n\t\tColumn_36 as string,\n\t\tColumn_37 as string,\n\t\tColumn_38 as string,\n\t\tColumn_39 as string,\n\t\tColumn_40 as string,\n\t\tColumn_41 as string,\n\t\tColumn_42 as string,\n\t\tColumn_43 as string,\n\t\tColumn_44 as string,\n\t\tColumn_45 as string,\n\t\tColumn_46 as string,\n\t\tColumn_47 as string,\n\t\tColumn_48 as string,\n\t\tColumn_49 as string,\n\t\tColumn_50 as string,\n\t\tColumn_51 as string,\n\t\tColumn_52 as string,\n\t\tColumn_53 as string,\n\t\tColumn_54 as string,\n\t\tColumn_55 as string,\n\t\tColumn_56 as string,\n\t\tColumn_57 as string,\n\t\tColumn_58 as string,\n\t\tColumn_59 as string,\n\t\tColumn_60 as string,\n\t\tColumn_61 as string,\n\t\tColumn_62 as string,\n\t\tColumn_63 as string,\n\t\tColumn_64 as string,\n\t\tColumn_65 as string,\n\t\tColumn_66 as string,\n\t\tColumn_67 as string,\n\t\tColumn_68 as string,\n\t\tColumn_69 as string,\n\t\tColumn_70 as string,\n\t\tColumn_71 as string,\n\t\tColumn_72 as string,\n\t\tColumn_73 as string\n\t),\n\tpartitionFileNames:[($outPutFileName)],\n\tumask: 0022,\n\tpreCommands: [],\n\tpostCommands: [],\n\tskipDuplicateMapInputs: true,\n\tskipDuplicateMapOutputs: true,\n\tpartitionBy('hash', 1)) ~> GenerateOutputFile"}}}