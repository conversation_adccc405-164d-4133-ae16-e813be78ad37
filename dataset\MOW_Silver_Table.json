{"name": "MOW_Silver_Table", "properties": {"linkedServiceName": {"referenceName": "MOW_LakeHouse", "type": "LinkedServiceReference"}, "parameters": {"TableName": {"type": "string"}}, "folder": {"name": "General"}, "annotations": [], "type": "LakehouseTable", "schema": [], "typeProperties": {"table": {"value": "@dataset().TableName", "type": "Expression"}}}, "type": "Microsoft.DataFactory/factories/datasets"}