{"name": "SFTP_ScotiaTrust_Binary_File", "properties": {"linkedServiceName": {"referenceName": "ScotiaTrustFileShare", "type": "LinkedServiceReference"}, "parameters": {"Path": {"type": "string"}, "FileName": {"type": "string"}}, "folder": {"name": "SFTP"}, "annotations": [], "type": "Binary", "typeProperties": {"location": {"type": "AzureFileStorageLocation", "fileName": {"value": "@dataset().FileName", "type": "Expression"}, "folderPath": {"value": "@dataset().Path", "type": "Expression"}}}}, "type": "Microsoft.DataFactory/factories/datasets"}