{"name": "DECOM- IN-403 Real Bank Account", "properties": {"description": "This pipeline will trigger based on some column data update in financial account salesforce object through salesforce mange instance and send files to SCD ", "activities": [{"name": "Set File Name", "type": "SetVariable", "dependsOn": [], "policy": {"secureOutput": false, "secureInput": false}, "userProperties": [], "typeProperties": {"variableName": "FileName", "value": {"value": "@concat('Real_Bank_Accounts_',formatDateTime(convertFromUtc(utcnow(),'Mountain Standard Time'),'MMddyyyyhhmmss'),'.csv')", "type": "Expression"}}}, {"name": "Copy File to SCD SFTP", "type": "Copy", "dependsOn": [{"activity": "Switch Statement", "dependencyConditions": ["Succeeded"]}], "policy": {"timeout": "7.00:00:00", "retry": 0, "retryIntervalInSeconds": 30, "secureOutput": false, "secureInput": false}, "userProperties": [], "typeProperties": {"source": {"type": "BinarySource", "storeSettings": {"type": "AzureBlobFSReadSettings", "recursive": false, "deleteFilesAfterCompletion": false}, "formatSettings": {"type": "BinaryReadSettings"}}, "sink": {"type": "BinarySink", "storeSettings": {"type": "SftpWriteSettings", "operationTimeout": "01:00:00", "useTempFileRename": true}}, "enableStaging": false}, "inputs": [{"referenceName": "AzureDataLakeStorage_Portfolio_Binary", "type": "DatasetReference", "parameters": {"SourceDirectory": "Salesforce To SCD/Portfolio/OUT", "FileName": {"value": "@variables('FileName')", "type": "Expression"}}}], "outputs": [{"referenceName": "SFTP_SCD_DIR_BINARY", "type": "DatasetReference", "parameters": {"Path": {"value": "@concat(pipeline().globalParameters.SCD_ENV,pipeline().globalParameters.SCD_SFTP_Portfolio)", "type": "Expression"}}}]}, {"name": "Archive FIle", "type": "Copy", "dependsOn": [{"activity": "Copy File to SCD SFTP", "dependencyConditions": ["Succeeded"]}], "policy": {"timeout": "7.00:00:00", "retry": 0, "retryIntervalInSeconds": 30, "secureOutput": false, "secureInput": false}, "userProperties": [], "typeProperties": {"source": {"type": "BinarySource", "storeSettings": {"type": "AzureBlobFSReadSettings", "recursive": false, "deleteFilesAfterCompletion": true}, "formatSettings": {"type": "BinaryReadSettings"}}, "sink": {"type": "BinarySink", "storeSettings": {"type": "AzureBlobFSWriteSettings"}}, "enableStaging": false}, "inputs": [{"referenceName": "AzureDataLakeStorage_Portfolio_Binary", "type": "DatasetReference", "parameters": {"SourceDirectory": "Salesforce To SCD/Portfolio/OUT", "FileName": {"value": "@variables('FileName')", "type": "Expression"}}}], "outputs": [{"referenceName": "AzureDataLakeStorage_Portfolio_Binary", "type": "DatasetReference", "parameters": {"SourceDirectory": {"value": "@concat('Salesforce To SCD/Portfolio/ARCHIVE/',utcnow('yyyyMMdd'))", "type": "Expression"}, "FileName": {"value": "@variables('FileName')", "type": "Expression"}}}]}, {"name": "Switch Statement", "type": "Switch", "dependsOn": [{"activity": "Set File Name", "dependencyConditions": ["Succeeded"]}], "userProperties": [], "typeProperties": {"on": {"value": "@pipeline().parameters.ObjectName", "type": "Expression"}, "cases": [{"value": "FinancialAccount", "activities": [{"name": "Generate File for SCD", "type": "Copy", "dependsOn": [], "policy": {"timeout": "7.00:00:00", "retry": 0, "retryIntervalInSeconds": 30, "secureOutput": false, "secureInput": false}, "userProperties": [], "typeProperties": {"source": {"type": "SqlMISource", "sqlReaderQuery": {"value": "@concat('select * from [scd].[vRealBankAccountsFinancialAccountChange] where FinancialAccountId=  ', '''',pipeline().parameters.ObjectId,'''')", "type": "Expression"}, "partitionOption": "None"}, "sink": {"type": "DelimitedTextSink", "storeSettings": {"type": "AzureBlobFSWriteSettings"}, "formatSettings": {"type": "DelimitedTextWriteSettings", "quoteAllText": true, "fileExtension": ".txt"}}, "enableStaging": false, "translator": {"type": "TabularTranslator", "mappings": [{"source": {"name": "PortfolioCode", "type": "String", "physicalType": "n<PERSON><PERSON><PERSON>"}, "sink": {"name": "PortfolioCode", "type": "String", "physicalType": "String"}}, {"source": {"name": "<PERSON><PERSON><PERSON>", "type": "String", "physicalType": "n<PERSON><PERSON><PERSON>"}, "sink": {"name": "<PERSON><PERSON><PERSON>", "type": "String", "physicalType": "String"}}, {"source": {"name": "Real bank account name", "type": "String", "physicalType": "n<PERSON><PERSON><PERSON>"}, "sink": {"name": "Real bank account name", "type": "String", "physicalType": "String"}}, {"source": {"name": "IBAN", "type": "String", "physicalType": "n<PERSON><PERSON><PERSON>"}, "sink": {"name": "IBAN", "type": "String", "physicalType": "String"}}, {"source": {"name": "Bank Account Currencies", "type": "String", "physicalType": "n<PERSON><PERSON><PERSON>"}, "sink": {"name": "Bank Account Currencies", "type": "String", "physicalType": "String"}}], "typeConversion": true, "typeConversionSettings": {"allowDataTruncation": true, "treatBooleanAsNumber": false}}}, "inputs": [{"referenceName": "SalesforceViews", "type": "DatasetReference"}], "outputs": [{"referenceName": "AzureDataLakeStorage_SalesforceToScd_RealBankAccounts", "type": "DatasetReference", "parameters": {"FileName": {"value": "@variables('FileName')", "type": "Expression"}}}]}]}]}}], "parameters": {"ObjectName": {"type": "string", "defaultValue": "FinancialAccount"}, "ObjectId": {"type": "string", "defaultValue": "a095G00001EzodMQAR"}}, "variables": {"FileName": {"type": "String"}}, "folder": {"name": "Internal/SFtoSCD"}, "annotations": [], "lastPublishTime": "2023-12-01T04:12:15Z"}, "type": "Microsoft.DataFactory/factories/pipelines"}