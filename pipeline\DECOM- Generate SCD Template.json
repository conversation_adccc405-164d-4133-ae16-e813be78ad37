{"name": "DECOM- Generate SCD Template", "properties": {"activities": [{"name": "SQLMI to ADL", "type": "Copy", "dependsOn": [{"activity": "Switch for FileName", "dependencyConditions": ["Succeeded"]}], "policy": {"timeout": "7.00:00:00", "retry": 0, "retryIntervalInSeconds": 30, "secureOutput": false, "secureInput": false}, "userProperties": [], "typeProperties": {"source": {"type": "SqlMISource", "sqlReaderQuery": {"value": "@concat(pipeline().parameters.SQLQry,' ')", "type": "Expression"}, "partitionOption": "None"}, "sink": {"type": "DelimitedTextSink", "storeSettings": {"type": "AzureBlobFSWriteSettings"}, "formatSettings": {"type": "DelimitedTextWriteSettings", "quoteAllText": true, "fileExtension": ".txt"}}, "enableStaging": false, "translator": {"type": "TabularTranslator", "typeConversion": true, "typeConversionSettings": {"allowDataTruncation": true, "treatBooleanAsNumber": false}}}, "inputs": [{"referenceName": "SQLMI_SFDB", "type": "DatasetReference"}], "outputs": [{"referenceName": "AzureDataLakeStorage_SalesforceToScd_PortfolioFile", "type": "DatasetReference", "parameters": {"FileName": {"value": "@variables('FileName')", "type": "Expression"}, "FolderName": {"value": "@concat('Salesforce To SCD/Daily Loads/',formatDateTime(convertFromUtc(utcnow(),'Mountain Standard Time'),'yyyyMMdd'))", "type": "Expression"}}}]}, {"name": "Switch for FileName", "type": "Switch", "dependsOn": [{"activity": "Set DateTimeSuffix", "dependencyConditions": ["Succeeded"]}], "userProperties": [], "typeProperties": {"on": {"value": "@pipeline().parameters.TemplateName", "type": "Expression"}, "cases": [{"value": "BankAccounts", "activities": [{"name": "Set FileName", "type": "SetVariable", "dependsOn": [], "policy": {"secureOutput": false, "secureInput": false}, "userProperties": [], "typeProperties": {"variableName": "FileName", "value": {"value": "@concat('BankAccounts',variables('Suffix_DateTime'))", "type": "Expression"}}}]}, {"value": "ClientGroup", "activities": [{"name": "Set FileName_copy1", "type": "SetVariable", "dependsOn": [], "policy": {"secureOutput": false, "secureInput": false}, "userProperties": [], "typeProperties": {"variableName": "FileName", "value": {"value": "@concat('ClientGroup',variables('Suffix_DateTime'))", "type": "Expression"}}}]}, {"value": "ClientInfo", "activities": [{"name": "Set FileName_copy2", "type": "SetVariable", "dependsOn": [], "policy": {"secureOutput": false, "secureInput": false}, "userProperties": [], "typeProperties": {"variableName": "FileName", "value": {"value": "@concat('ClientInfo',variables('Suffix_DateTime'))", "type": "Expression"}}}]}, {"value": "<PERSON><PERSON><PERSON><PERSON>", "activities": [{"name": "Set FileName_copy3", "type": "SetVariable", "dependsOn": [], "policy": {"secureOutput": false, "secureInput": false}, "userProperties": [], "typeProperties": {"variableName": "FileName", "value": {"value": "@concat('Custodian',variables('Suffix_DateTime'))", "type": "Expression"}}}]}, {"value": "Custodies", "activities": [{"name": "Set FileName_copy4", "type": "SetVariable", "dependsOn": [], "policy": {"secureOutput": false, "secureInput": false}, "userProperties": [], "typeProperties": {"variableName": "FileName", "value": {"value": "@concat('Custodies',variables('Suffix_DateTime'))", "type": "Expression"}}}]}, {"value": "Portfolios", "activities": [{"name": "Set FileName_copy5", "type": "SetVariable", "dependsOn": [], "policy": {"secureOutput": false, "secureInput": false}, "userProperties": [], "typeProperties": {"variableName": "FileName", "value": {"value": "@concat('Portfolios',variables('Suffix_DateTime'))", "type": "Expression"}}}]}, {"value": "RealBankAccounts", "activities": [{"name": "Set FileName_copy6", "type": "SetVariable", "dependsOn": [], "policy": {"secureOutput": false, "secureInput": false}, "userProperties": [], "typeProperties": {"variableName": "FileName", "value": {"value": "@concat('RealBankAccounts',variables('Suffix_DateTime'))", "type": "Expression"}}}]}, {"value": "PortfolioMerge", "activities": [{"name": "Set FileName_PortfolioMerge", "type": "SetVariable", "dependsOn": [], "policy": {"secureOutput": false, "secureInput": false}, "userProperties": [], "typeProperties": {"variableName": "FileName", "value": {"value": "@concat('Portfolio_Group_Change',variables('Suffix_DateTime'))", "type": "Expression"}}}]}]}}, {"name": "Set DateTimeSuffix", "type": "SetVariable", "dependsOn": [], "policy": {"secureOutput": false, "secureInput": false}, "userProperties": [], "typeProperties": {"variableName": "Suffix_DateTime", "value": {"value": "@concat('_',formatDateTime(convertFromUtc(utcnow(),'Mountain Standard Time'),'MMddyyyy'),'_',formatDateTime(convertFromUtc(utcnow(),'Mountain Standard Time'),'HHmmss'),'.csv')", "type": "Expression"}}}, {"name": "Send to SCD After Last File Run", "type": "IfCondition", "dependsOn": [{"activity": "SQLMI to ADL", "dependencyConditions": ["Succeeded"]}], "userProperties": [], "typeProperties": {"expression": {"value": "@equals(pipeline().parameters.TemplateName,'ClientInfo')", "type": "Expression"}, "ifTrueActivities": [{"name": "Bulk Upload SF Files", "type": "ExecutePipeline", "dependsOn": [], "policy": {"secureInput": false}, "userProperties": [], "typeProperties": {"pipeline": {"referenceName": "DECOM- Bulk Upload SF Files", "type": "PipelineReference"}, "waitOnCompletion": true}}]}}], "parameters": {"SQLQry": {"type": "string", "defaultValue": "select * from scd.vCustodiesChange where 1=1 and ( FinancialAccountId in ('A','a09f4000006gCwhAAE') or MergeId in ('A') )"}, "TemplateName": {"type": "string", "defaultValue": "Custodies"}}, "variables": {"Suffix_DateTime": {"type": "String"}, "FileName": {"type": "String"}}, "folder": {"name": "Internal/SFtoSCD/SF-SCD Real Time"}, "annotations": [], "lastPublishTime": "2023-12-01T04:12:40Z"}, "type": "Microsoft.DataFactory/factories/pipelines"}