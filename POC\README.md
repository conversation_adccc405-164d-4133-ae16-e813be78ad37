# Azure Data Factory Pipeline Exclusion POC

This POC demonstrates how to exclude specific Azure Data Factory pipelines from being promoted from DEV to PROD environment during CI/CD deployment.

## Problem Statement

In your current setup:
- All pipelines are developed in DEV environment
- ARM templates are auto-generated from ADF UI changes
- CI/CD promotes all pipelines from DEV to PROD
- Need to exclude certain pipelines from PROD deployment while keeping them in DEV

## Solution Overview

This POC provides three approaches to solve the pipeline exclusion challenge:

### Approach 1: Metadata-Based Exclusion (Recommended)
Uses pipeline annotations to mark pipelines for exclusion and filters them during deployment.

### Approach 2: Environment-Specific Conditional Logic
Enhances existing conditional logic patterns in your pipelines.

### Approach 3: Trigger-Based Exclusion
Uses triggers to control which pipelines run in different environments.

## Implementation Details

### Files in this POC

1. **`sample-dev-only-pipeline.json`** - Example pipeline with exclusion annotations
2. **`Filter-ADFPipelines.ps1`** - PowerShell script to filter pipelines during deployment
3. **`azure-pipelines-adf-with-exclusion.yml`** - Azure DevOps pipeline with exclusion logic
4. **`arm-template-parameters-definition-with-exclusion.json`** - Enhanced ARM template parameters

### How It Works

#### Step 1: Mark Pipelines for Exclusion
Add annotations to pipelines that should not be promoted to PROD:

```json
{
  "name": "DEV-ONLY-Pipeline",
  "properties": {
    "annotations": [
      "DEV_ONLY",
      "EXCLUDE_FROM_PROD"
    ],
    // ... rest of pipeline definition
  }
}
```

#### Step 2: Filter During Build
The PowerShell script `Filter-ADFPipelines.ps1` processes all ADF artifacts and:
- Reads pipeline annotations
- Excludes pipelines with exclusion annotations
- Excludes triggers that reference excluded pipelines
- Copies remaining artifacts to deployment staging area

#### Step 3: Deploy Filtered Artifacts
The Azure DevOps pipeline deploys only the filtered artifacts to PROD.

## Usage Instructions

### 1. Setup in Your Environment

1. Copy the POC files to your repository
2. Modify the Azure DevOps pipeline variables to match your environment:
   ```yaml
   variables:
     - name: devDataFactoryName
       value: 'AZ-DEV-UW-DF-SCD'  # Your DEV ADF name
     - name: prodDataFactoryName
       value: 'AZ-PRD-UW-DF-SCD'  # Your PROD ADF name
   ```

### 2. Mark Pipelines for Exclusion

Add exclusion annotations to any pipeline that should stay in DEV only:

```json
"annotations": [
  "DEV_ONLY",
  "EXCLUDE_FROM_PROD",
  "TESTING_PIPELINE"
]
```

### 3. Test the Exclusion

Run the PowerShell script in "What-If" mode to see what would be excluded:

```powershell
.\Filter-ADFPipelines.ps1 -SourcePath "." -TargetPath "./filtered" -WhatIf
```

### 4. Deploy with Exclusion

Use the provided Azure DevOps pipeline or integrate the filtering script into your existing CI/CD process.

## Benefits

1. **Selective Deployment**: Only deploy pipelines intended for production
2. **Development Freedom**: Keep experimental/testing pipelines in DEV
3. **Automated Process**: No manual intervention required during deployment
4. **Audit Trail**: Generates exclusion reports for compliance
5. **Backward Compatible**: Works with existing ADF UI-based development

## Alternative Approaches

### Approach 2: Enhanced Conditional Logic

Extend your existing pattern of environment detection:

```json
{
  "name": "Environment Check",
  "type": "IfCondition",
  "typeProperties": {
    "expression": {
      "value": "@and(equals(pipeline().DataFactory,'AZ-PRD-UW-DF-SCD'), not(contains(pipeline().Pipeline.name, 'DEV-ONLY')))",
      "type": "Expression"
    }
  }
}
```

### Approach 3: Trigger-Based Control

Create environment-specific triggers:

```json
{
  "name": "PROD-ONLY-Trigger",
  "properties": {
    "annotations": ["PROD_ONLY"],
    "pipelines": [
      {
        "pipelineReference": {
          "referenceName": "Production-Pipeline",
          "type": "PipelineReference"
        }
      }
    ]
  }
}
```

## Testing Strategy

1. **Unit Testing**: Test the PowerShell filtering script with sample data
2. **Integration Testing**: Deploy to a test environment first
3. **Validation**: Verify excluded pipelines don't appear in PROD
4. **Rollback Plan**: Keep original deployment process as backup

## Monitoring and Maintenance

1. **Exclusion Reports**: Review generated exclusion reports regularly
2. **Pipeline Audits**: Ensure annotations are correctly applied
3. **Process Documentation**: Keep team informed about exclusion patterns
4. **Regular Reviews**: Periodically review excluded pipelines for relevance

## Next Steps

1. Test this POC in your development environment
2. Identify pipelines that should be excluded from PROD
3. Add appropriate annotations to those pipelines
4. Integrate the filtering script into your CI/CD pipeline
5. Monitor and refine the exclusion process

## Support and Troubleshooting

Common issues and solutions:

1. **Script Execution Policy**: Ensure PowerShell execution policy allows script execution
2. **Path Issues**: Verify source and target paths are correct
3. **JSON Parsing**: Ensure all pipeline JSON files are valid
4. **Permissions**: Verify Azure DevOps service connection has necessary permissions

For questions or issues, refer to the generated exclusion reports and logs.
