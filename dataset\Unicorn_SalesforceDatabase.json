{"name": "Unicorn_SalesforceDatabase", "properties": {"linkedServiceName": {"referenceName": "AzureSQLManagedInstance", "type": "LinkedServiceReference", "parameters": {"dbName": {"value": "@if(equals(pipeline().DataFactory,'AZ-PRD-UW-DF-SCD'),'SalesforceData','Salesforce' )\n", "type": "Expression"}}}, "folder": {"name": "Inbound/Salesforce/IN-193 DW Inbound Interface/IN-196 SF To IMW"}, "annotations": [], "type": "AzureSqlMITable", "schema": [{"name": "Id", "type": "n<PERSON><PERSON><PERSON>"}, {"name": "ValidFromUTC", "type": "datetime2", "scale": 7}, {"name": "ValidToUTC", "type": "datetime2", "scale": 7}, {"name": "Language", "type": "n<PERSON><PERSON><PERSON>"}, {"name": "OwnerDivision", "type": "n<PERSON><PERSON><PERSON>"}, {"name": "AccountNumber", "type": "n<PERSON><PERSON><PERSON>"}, {"name": "AccreditedInvestor", "type": "n<PERSON><PERSON><PERSON>"}, {"name": "AMLComplianceNotes", "type": "n<PERSON><PERSON><PERSON>"}, {"name": "AMLRiskClassification", "type": "n<PERSON><PERSON><PERSON>"}, {"name": "AnnualRevenue", "type": "float", "precision": 15}, {"name": "AccountOnBehalfOfThirdParty", "type": "n<PERSON><PERSON><PERSON>"}, {"name": "BorrowedMoneyToInvest", "type": "n<PERSON><PERSON><PERSON>"}, {"name": "AssociateId", "type": "n<PERSON><PERSON><PERSON>"}, {"name": "BackupAccountOwnerId", "type": "n<PERSON><PERSON><PERSON>"}, {"name": "BillingCity", "type": "n<PERSON><PERSON><PERSON>"}, {"name": "BillingCountry", "type": "n<PERSON><PERSON><PERSON>"}, {"name": "BillingPostalCode", "type": "n<PERSON><PERSON><PERSON>"}, {"name": "BillingState", "type": "n<PERSON><PERSON><PERSON>"}, {"name": "BillingStreet", "type": "n<PERSON><PERSON><PERSON>"}, {"name": "BusinessID_CharityNumber", "type": "n<PERSON><PERSON><PERSON>"}, {"name": "BusinessTaxID", "type": "n<PERSON><PERSON><PERSON>"}, {"name": "CapitalLossAmount", "type": "float", "precision": 15}, {"name": "CashReserveInstructions", "type": "n<PERSON><PERSON><PERSON>"}, {"name": "ClientPreviouslyIdentified", "type": "bit"}, {"name": "ClientInceptionDate", "type": "datetime2", "scale": 7}, {"name": "ClientServiceAlpha", "type": "n<PERSON><PERSON><PERSON>"}, {"name": "ConsentToIDVerification", "type": "bit"}, {"name": "ConsultantMandate", "type": "n<PERSON><PERSON><PERSON>"}, {"name": "Container", "type": "n<PERSON><PERSON><PERSON>"}, {"name": "CountryOfResidence", "type": "n<PERSON><PERSON><PERSON>"}, {"name": "DatabaseReview", "type": "n<PERSON><PERSON><PERSON>"}, {"name": "Description", "type": "n<PERSON><PERSON><PERSON>"}, {"name": "HasPOA", "type": "n<PERSON><PERSON><PERSON>"}, {"name": "Fax", "type": "n<PERSON><PERSON><PERSON>"}, {"name": "IndividualID", "type": "n<PERSON><PERSON><PERSON>"}, {"name": "IndividualType", "type": "n<PERSON><PERSON><PERSON>"}, {"name": "InvestmentExperience", "type": "n<PERSON><PERSON><PERSON>"}, {"name": "KYCDate", "type": "datetime2", "scale": 7}, {"name": "LastInteractionDate", "type": "datetime2", "scale": 7}, {"name": "LastReviewDate", "type": "datetime2", "scale": 7}, {"name": "MarketingSegment", "type": "n<PERSON><PERSON><PERSON>"}, {"name": "NetWorth", "type": "float", "precision": 15}, {"name": "NextInteractionDate", "type": "datetime2", "scale": 7}, {"name": "NextReviewDate", "type": "datetime2", "scale": 7}, {"name": "ImportantNotes", "type": "n<PERSON><PERSON><PERSON>"}, {"name": "PrimaryContactId", "type": "n<PERSON><PERSON><PERSON>"}, {"name": "ReviewFrequency", "type": "n<PERSON><PERSON><PERSON>"}, {"name": "RiskTolerance", "type": "n<PERSON><PERSON><PERSON>"}, {"name": "ServiceModel", "type": "n<PERSON><PERSON><PERSON>"}, {"name": "SSID", "type": "n<PERSON><PERSON><PERSON>"}, {"name": "Status", "type": "n<PERSON><PERSON><PERSON>"}, {"name": "TimeHorizon", "type": "n<PERSON><PERSON><PERSON>"}, {"name": "FiscalYearEnd", "type": "n<PERSON><PERSON><PERSON>"}, {"name": "FundSeries", "type": "n<PERSON><PERSON><PERSON>"}, {"name": "TaxResidentCountryNames", "type": "n<PERSON><PERSON><PERSON>"}, {"name": "InsiderCompanyNames", "type": "n<PERSON><PERSON><PERSON>"}, {"name": "Insider", "type": "n<PERSON><PERSON><PERSON>"}, {"name": "InvestingIn", "type": "n<PERSON><PERSON><PERSON>"}, {"name": "IsMailingAddressDifferent", "type": "bit"}, {"name": "IsPOASigning", "type": "n<PERSON><PERSON><PERSON>"}, {"name": "IsSpouseMawerClient", "type": "bit"}, {"name": "CustomerPortalAccount", "type": "bit"}, {"name": "LastFiscalYearEndReportSent", "type": "datetime2", "scale": 7}, {"name": "LastSuitabilityDate", "type": "datetime2", "scale": 7}, {"name": "LastOffCycleContactReviewDate", "type": "datetime2", "scale": 7}, {"name": "LeveragedDisclosureAccounts", "type": "n<PERSON><PERSON><PERSON>"}, {"name": "ClientSegment", "type": "n<PERSON><PERSON><PERSON>"}, {"name": "MeetingPackageNotes", "type": "n<PERSON><PERSON><PERSON>"}, {"name": "Account<PERSON><PERSON>", "type": "n<PERSON><PERSON><PERSON>"}, {"name": "NatureOfBusiness", "type": "n<PERSON><PERSON><PERSON>"}, {"name": "NetWorthRange", "type": "n<PERSON><PERSON><PERSON>"}, {"name": "NoAnnualIPSSignatureObligation", "type": "bit"}, {"name": "NonModelPositions", "type": "n<PERSON><PERSON><PERSON>"}, {"name": "Employees", "type": "int", "precision": 10}, {"name": "OffModelPositions", "type": "n<PERSON><PERSON><PERSON>"}, {"name": "OtherCountryTaxpayerId", "type": "n<PERSON><PERSON><PERSON>"}, {"name": "OwnerId", "type": "n<PERSON><PERSON><PERSON>"}, {"name": "Ownership", "type": "n<PERSON><PERSON><PERSON>"}, {"name": "ParentAccountId", "type": "n<PERSON><PERSON><PERSON>"}, {"name": "Phone", "type": "n<PERSON><PERSON><PERSON>"}, {"name": "PlaceOfDocumentExecution", "type": "n<PERSON><PERSON><PERSON>"}, {"name": "PlaceOfIncorporation", "type": "n<PERSON><PERSON><PERSON>"}, {"name": "PoliticallyExposed", "type": "n<PERSON><PERSON><PERSON>"}, {"name": "PrimaryPlatformRecordKeeper", "type": "n<PERSON><PERSON><PERSON>"}, {"name": "RecordTypeId", "type": "n<PERSON><PERSON><PERSON>"}, {"name": "Relationship3minDrill", "type": "n<PERSON><PERSON><PERSON>"}, {"name": "ReportingNotes", "type": "n<PERSON><PERSON><PERSON>"}, {"name": "SecondaryPlatformRecordKeeper", "type": "n<PERSON><PERSON><PERSON>"}, {"name": "SecuritiesAndTimeFrameToSell", "type": "n<PERSON><PERSON><PERSON>"}, {"name": "SetupPortalAccess", "type": "bit"}, {"name": "ShippingCity", "type": "n<PERSON><PERSON><PERSON>"}, {"name": "ShippingCountry", "type": "n<PERSON><PERSON><PERSON>"}, {"name": "ShippingPostalCode", "type": "n<PERSON><PERSON><PERSON>"}, {"name": "ShippingState", "type": "n<PERSON><PERSON><PERSON>"}, {"name": "ShippingStreet", "type": "n<PERSON><PERSON><PERSON>"}, {"name": "SpouseProvideInstructions", "type": "bit"}, {"name": "StatementDelivery", "type": "n<PERSON><PERSON><PERSON>"}, {"name": "TaxResidentOtherThanCanadaOrUS", "type": "n<PERSON><PERSON><PERSON>"}, {"name": "ThirdPartyCity", "type": "n<PERSON><PERSON><PERSON>"}, {"name": "ThirdPartyCountry", "type": "n<PERSON><PERSON><PERSON>"}, {"name": "ThirdPartyDateOfBirth", "type": "datetime2", "scale": 7}, {"name": "ThirdPartyName", "type": "n<PERSON><PERSON><PERSON>"}, {"name": "ThirdPartyPostalCode", "type": "n<PERSON><PERSON><PERSON>"}, {"name": "ThirdPartyRelationshipToClient", "type": "n<PERSON><PERSON><PERSON>"}, {"name": "ThirdPartyState", "type": "n<PERSON><PERSON><PERSON>"}, {"name": "ThirdPartyStreet", "type": "n<PERSON><PERSON><PERSON>"}, {"name": "AccountType", "type": "n<PERSON><PERSON><PERSON>"}, {"name": "UniqueCircumstances", "type": "n<PERSON><PERSON><PERSON>"}, {"name": "UnsupervisedPositions", "type": "n<PERSON><PERSON><PERSON>"}, {"name": "USTaxResidentOrCitizen", "type": "n<PERSON><PERSON><PERSON>"}, {"name": "USTaxpayerId", "type": "n<PERSON><PERSON><PERSON>"}, {"name": "IsVulnerableClient", "type": "bit"}, {"name": "CreatedById", "type": "n<PERSON><PERSON><PERSON>"}, {"name": "CreatedDate", "type": "datetime2", "scale": 7}, {"name": "LastModifiedById", "type": "n<PERSON><PERSON><PERSON>"}, {"name": "LastModifiedDate", "type": "datetime2", "scale": 7}, {"name": "TaxJurisdiction", "type": "n<PERSON><PERSON><PERSON>"}, {"name": "NoTINReason", "type": "n<PERSON><PERSON><PERSON>"}, {"name": "FATCAClassification", "type": "n<PERSON><PERSON><PERSON>"}, {"name": "CRSClassification", "type": "n<PERSON><PERSON><PERSON>"}], "typeProperties": {"schema": "dbo", "table": "Account"}}, "type": "Microsoft.DataFactory/factories/datasets"}