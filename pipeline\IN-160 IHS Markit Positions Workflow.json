{"name": "IN-160 IHS Markit Positions Workflow", "properties": {"activities": [{"name": "Run Only In PROD", "type": "IfCondition", "dependsOn": [], "userProperties": [], "typeProperties": {"expression": {"value": "@equals(pipeline().DataFactory,'AZ-PRD-UW-DF-SCD')", "type": "Expression"}, "ifTrueActivities": [{"name": "IHSMarkitPositionUpload", "type": "ExecutePipeline", "dependsOn": [{"activity": "Set Output FileName", "dependencyConditions": ["Succeeded"]}], "policy": {"secureInput": false}, "userProperties": [], "typeProperties": {"pipeline": {"referenceName": "IN - 161 IHS Markit Position Upload - PARENT", "type": "PipelineReference"}, "waitOnCompletion": true, "parameters": {"pOutFileName": {"value": "@variables('OutFileName')", "type": "Expression"}}}}, {"name": "Set Output FileName", "type": "SetVariable", "dependsOn": [], "policy": {"secureOutput": false, "secureInput": false}, "userProperties": [], "typeProperties": {"variableName": "OutFileName", "value": {"value": "@concat('caPosUpload_',formatDateTime(convertFromUtc(utcnow(),'Mountain Standard Time'),'yyyyMMddHHmmss'),'.csv')", "type": "Expression"}}}, {"name": "Completion alert after Position Load completion", "type": "WebActivity", "dependsOn": [{"activity": "Completion Time", "dependencyConditions": ["Succeeded"]}], "policy": {"timeout": "7.00:00:00", "retry": 0, "retryIntervalInSeconds": 30, "secureOutput": false, "secureInput": false}, "userProperties": [], "typeProperties": {"method": "POST", "headers": {"Content-Type": "application/json"}, "url": {"value": "@pipeline().globalParameters.SendEmailLogicFunctionUrl", "type": "Expression"}, "body": {"value": "@json(concat(\n    '{',\n    '\"BodyContents\":\"',\n    variables('OutFileName'),\n    ' file loaded into SFTP at ',\n    variables('CompletionTime'),\n    '\",',\n    '\"SubjectContents\":\"CASAAS Position Upload completed successfully at ',\n    variables('CompletionTime'),\n    '\",',\n    '\"ToContents\":\"',\n    pipeline().globalParameters.CorporateActions_Recipients,\n    '\"',\n    '}'\n))\n", "type": "Expression"}}}, {"name": "Completion Time", "type": "SetVariable", "dependsOn": [{"activity": "IHSMarkitPositionUpload", "dependencyConditions": ["Succeeded"]}], "policy": {"secureOutput": false, "secureInput": false}, "userProperties": [], "typeProperties": {"variableName": "CompletionTime", "value": {"value": "@formatDateTime(convertFromUtc(utcnow(),'Mountain Standard Time'),'hh:mm:ss tt')", "type": "Expression"}}}, {"name": "Failed Time", "type": "SetVariable", "dependsOn": [{"activity": "IHSMarkitPositionUpload", "dependencyConditions": ["Failed"]}], "policy": {"secureOutput": false, "secureInput": false}, "userProperties": [], "typeProperties": {"variableName": "FailedTime", "value": {"value": "@formatDateTime(convertFromUtc(utcnow(),'Mountain Standard Time'),'hh:mm:ss tt')", "type": "Expression"}}}, {"name": "Failure alert after Position Load completion_copy1", "type": "WebActivity", "dependsOn": [{"activity": "Failed Time", "dependencyConditions": ["Succeeded"]}], "policy": {"timeout": "7.00:00:00", "retry": 0, "retryIntervalInSeconds": 30, "secureOutput": false, "secureInput": false}, "userProperties": [], "typeProperties": {"method": "POST", "headers": {"Content-Type": "application/json"}, "url": {"value": "@pipeline().globalParameters.SendEmailLogicFunctionUrl", "type": "Expression"}, "body": {"value": "@json(concat(\n    '{',\n    '\"BodyContents\":\"CASAAS Position Upload failed at ',\n    variables('FailedTime'),\n    '. Please do not act on pending actions until next successful position upload run. \n\t  If the agent deadline is approaching you may reach out to SCD Support for position confirmation or CASAAS support for assistance on manual position overrides\",',\n    '\"SubjectContents\":\"CASAAS Position Upload failed at ',\n    variables('FailedTime'),\n    '\",',\n    '\"ToContents\":\"',\n    pipeline().globalParameters.CorporateActions_Recipients,\n    '\"',\n    '}'\n))\n", "type": "Expression"}}}]}}], "variables": {"CompletionTime": {"type": "String"}, "FailedTime": {"type": "String"}, "OutFileName": {"type": "String"}}, "folder": {"name": "IHS Markit/IHS Markit CA Workflow"}, "annotations": [], "lastPublishTime": "2025-02-03T20:46:48Z"}, "type": "Microsoft.DataFactory/factories/pipelines"}