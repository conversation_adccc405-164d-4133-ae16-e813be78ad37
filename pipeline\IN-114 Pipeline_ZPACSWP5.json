{"name": "IN-114 Pipeline_ZPACSWP5", "properties": {"activities": [{"name": "ZPACSWP5", "type": "GetMetadata", "dependsOn": [], "policy": {"timeout": "7.00:00:00", "retry": 0, "retryIntervalInSeconds": 30, "secureOutput": false, "secureInput": false}, "userProperties": [], "typeProperties": {"dataset": {"referenceName": "AzureDataLakeStorage_IFDS_RAWFiles_IN_ZPACSWP5", "type": "DatasetReference", "parameters": {"ADLRawFileDirectory": {"value": "@pipeline().parameters.ADLRawFileDirectory", "type": "Expression"}}}, "fieldList": ["exists"], "storeSettings": {"type": "AzureBlobFSReadSettings", "recursive": true, "enablePartitionDiscovery": false}, "formatSettings": {"type": "DelimitedTextReadSettings"}}}, {"name": "If Condition_ZPACSWP5", "type": "IfCondition", "dependsOn": [{"activity": "ZPACSWP5", "dependencyConditions": ["Succeeded"]}], "userProperties": [], "typeProperties": {"expression": {"value": "@activity('ZPACSWP5').output.exists", "type": "Expression"}, "ifTrueActivities": [{"name": "Execute Pipeline_ZPACSWP5", "type": "ExecutePipeline", "dependsOn": [], "policy": {"secureInput": false}, "userProperties": [], "typeProperties": {"waitOnCompletion": true, "parameters": {"FunctionBody": "{\"FileName\":\"ZPACSWP5.D\"}"}}}, {"name": "Copy data from External Files", "type": "Copy", "dependsOn": [{"activity": "Set Date", "dependencyConditions": ["Succeeded"]}], "policy": {"timeout": "7.00:00:00", "retry": 0, "retryIntervalInSeconds": 30, "secureOutput": false, "secureInput": false}, "userProperties": [], "typeProperties": {"source": {"type": "DelimitedTextSource", "storeSettings": {"type": "AzureBlobFSReadSettings", "recursive": true, "enablePartitionDiscovery": false}, "formatSettings": {"type": "DelimitedTextReadSettings"}}, "sink": {"type": "AzureDataExplorerSink"}, "enableStaging": false, "translator": {"type": "TabularTranslator", "mappings": [{"source": {"name": "Type", "type": "String"}, "sink": {"name": "Type", "type": "String"}}, {"source": {"name": "Account", "type": "String"}, "sink": {"name": "Account", "type": "String"}}, {"source": {"name": "Name", "type": "String"}, "sink": {"name": "Name", "type": "String"}}, {"source": {"name": "Broker", "type": "String"}, "sink": {"name": "Broker", "type": "String"}}, {"source": {"name": "Branch", "type": "String"}, "sink": {"name": "Branch", "type": "String"}}, {"source": {"name": "SlsRep", "type": "String"}, "sink": {"name": "SlsRep", "type": "String"}}, {"source": {"name": "Fund", "type": "String"}, "sink": {"name": "Fund", "type": "String"}}, {"source": {"name": "C", "type": "String"}, "sink": {"name": "c", "type": "String"}}, {"source": {"name": "StartDate", "type": "String"}, "sink": {"name": "StartDate", "type": "String"}}, {"source": {"name": "LastProc", "type": "String"}, "sink": {"name": "LastProc", "type": "String"}}, {"source": {"name": "Amount", "type": "String"}, "sink": {"name": "Amount", "type": "String"}}, {"source": {"name": "Loaded", "type": "String"}, "sink": {"name": "Loaded", "type": "DateTime"}}, {"source": {"name": "RowId", "type": "String"}, "sink": {"name": "RowId", "type": "String"}}]}}, "inputs": [{"referenceName": "AzureDataLakeStorage_IFDS_ExternalFiles_ZPACSWP5", "type": "DatasetReference", "parameters": {"fileName": {"value": "@concat('ZPACSWP5.D-',variables('vDate'),'.csv')", "type": "Expression"}}}], "outputs": [{"referenceName": "ADX_ExternalIFDS", "type": "DatasetReference", "parameters": {"Table": "ZPACSWP5"}}]}, {"name": "Set Date", "type": "SetVariable", "dependsOn": [{"activity": "Execute Pipeline_ZPACSWP5", "dependencyConditions": ["Succeeded"]}], "policy": {"secureOutput": false, "secureInput": false}, "userProperties": [], "typeProperties": {"variableName": "vDate", "value": {"value": "@concat(substring(pipeline().parameters.Date,6,2),'-',\nsubstring(pipeline().parameters.Date,4,2),'-',\nsubstring(pipeline().parameters.Date,0,4) )\n", "type": "Expression"}}}]}}], "parameters": {"ADLRawFileDirectory": {"type": "string"}, "Date": {"type": "string"}}, "variables": {"vDate": {"type": "String"}}, "folder": {"name": "IFDS/IFDS Integration/IN-101-ExternalFIles"}, "annotations": [], "lastPublishTime": "2023-12-01T04:11:27Z"}, "type": "Microsoft.DataFactory/factories/pipelines"}