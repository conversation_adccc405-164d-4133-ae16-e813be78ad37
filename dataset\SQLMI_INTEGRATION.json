{"name": "SQLMI_INTEGRATION", "properties": {"linkedServiceName": {"referenceName": "SQLMI_RESEARCH_PRD", "type": "LinkedServiceReference", "parameters": {"DBName": "Integration"}}, "parameters": {"SchemaName": {"type": "string"}, "TableName": {"type": "string"}}, "folder": {"name": "General"}, "annotations": [], "type": "AzureSqlMITable", "schema": [], "typeProperties": {"schema": {"value": "@dataset().SchemaName", "type": "Expression"}, "table": {"value": "@dataset().TableName", "type": "Expression"}}}, "type": "Microsoft.DataFactory/factories/datasets"}