{"name": "SQLMI_SFIntegration", "properties": {"linkedServiceName": {"referenceName": "SQLMI_RESEARCH_PRD", "type": "LinkedServiceReference", "parameters": {"DBName": {"value": "@dataset().Database", "type": "Expression"}}}, "parameters": {"Database": {"type": "string"}, "Schema": {"type": "string"}, "Table": {"type": "string"}}, "folder": {"name": "SCD"}, "annotations": [], "type": "AzureSqlMITable", "schema": [], "typeProperties": {"schema": {"value": "@dataset().Schema", "type": "Expression"}, "table": {"value": "@dataset().Table", "type": "Expression"}}}, "type": "Microsoft.DataFactory/factories/datasets"}