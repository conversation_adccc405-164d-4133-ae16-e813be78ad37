{"name": "IN-130 IFDS Workflow_Beta", "properties": {"activities": [{"name": "Run Only In PROD", "type": "IfCondition", "dependsOn": [], "userProperties": [], "typeProperties": {"expression": {"value": "@equals(pipeline().DataFactory,'AZ-PRD-UW-DF-SCD')", "type": "Expression"}, "ifTrueActivities": [{"name": "IFDS_ADL_SQLMI", "type": "ExecutePipeline", "dependsOn": [{"activity": "IFDS Daily Files Download", "dependencyConditions": ["Succeeded"]}], "policy": {"secureInput": false}, "userProperties": [], "typeProperties": {"pipeline": {"referenceName": "IFDS_ADL_SQLMI", "type": "PipelineReference"}, "waitOnCompletion": true, "parameters": {"RunDate": {"value": "@variables('Date')", "type": "Expression"}}}}, {"name": "IFDS Daily Files Download", "type": "ExecutePipeline", "dependsOn": [{"activity": "Set ADLRawFilesDirectory", "dependencyConditions": ["Succeeded"]}], "policy": {"secureInput": false}, "userProperties": [], "typeProperties": {"pipeline": {"referenceName": "IN-131 IFDS Download Raw Files_Beta", "type": "PipelineReference"}, "waitOnCompletion": true, "parameters": {"ADLRawFileDirectory": {"value": "@variables('ADLSourceDirectory')", "type": "Expression"}, "IFDS_SFTP_PATH": "/home/<USER>/"}}}, {"name": "Set Date", "type": "SetVariable", "dependsOn": [], "policy": {"secureOutput": false, "secureInput": false}, "userProperties": [], "typeProperties": {"variableName": "Date", "value": {"value": "@formatDateTime(\nconvertFromUtc(utcnow(), 'Mountain Standard Time')\n,'yyyyMMdd')", "type": "Expression"}}}, {"name": "Set ADLRawFilesDirectory", "type": "SetVariable", "dependsOn": [{"activity": "Set vDatePlus1D", "dependencyConditions": ["Succeeded"]}], "policy": {"secureOutput": false, "secureInput": false}, "userProperties": [], "typeProperties": {"variableName": "ADLSourceDirectory", "value": {"value": "@concat('Custodian Data/IFDS/Raw Files/',variables('Date'))", "type": "Expression"}}}, {"name": "IFDS Transactions", "type": "ExecutePipeline", "dependsOn": [{"activity": "IFDS Load to ADX", "dependencyConditions": ["Succeeded"]}], "policy": {"secureInput": false}, "userProperties": [], "typeProperties": {"pipeline": {"referenceName": "IN-134 IFDS Transactions", "type": "PipelineReference"}, "waitOnCompletion": true, "parameters": {"pDate": {"value": "@variables('Date')", "type": "Expression"}, "pADLPath": {"value": "@pipeline().parameters.pADLPath", "type": "Expression"}, "pADLContainer": {"value": "@pipeline().parameters.pADLContainer", "type": "Expression"}, "pSendToSCD": {"value": "@pipeline().parameters.pSendToSCD", "type": "Expression"}, "pSCDsftpPath": {"value": "@concat(pipeline().globalParameters.SCD_ENV, '/ActiveData/Interfaces/In/SFTP/IFDS/Working')", "type": "Expression"}, "pDataType": {"value": "@pipeline().parameters.pDataType", "type": "Expression"}, "pDatePlus1D": {"value": "@variables('vDatePlus1D')", "type": "Expression"}}}}, {"name": "Send mail alert after completion", "type": "WebActivity", "dependsOn": [{"activity": "IFDS Transactions", "dependencyConditions": ["Succeeded"]}], "policy": {"timeout": "7.00:00:00", "retry": 0, "retryIntervalInSeconds": 30, "secureOutput": false, "secureInput": false}, "userProperties": [], "typeProperties": {"method": "POST", "headers": {"Content-Type": "application/json"}, "url": {"value": "@pipeline().globalParameters.SendEmailLogicFunctionUrl", "type": "Expression"}, "body": {"value": "@concat(\n    '{\n        \"BodyContents\":\"\",\n        \"SubjectContents\":\"IFDS TRN Load Completed\",\n        \"ToContents\"',':\"',pipeline().globalParameters.DDIMailAlerts,'\"',' \n    }'\n    )", "type": "Expression"}}}, {"name": "Send mail alert if it fails", "type": "WebActivity", "dependsOn": [{"activity": "IFDS Transactions", "dependencyConditions": ["Failed"]}], "policy": {"timeout": "7.00:00:00", "retry": 0, "retryIntervalInSeconds": 30, "secureOutput": false, "secureInput": false}, "userProperties": [], "typeProperties": {"method": "POST", "headers": {"Content-Type": "application/json"}, "url": {"value": "@pipeline().globalParameters.SendEmailLogicFunctionUrl", "type": "Expression"}, "body": {"value": "@concat(\n    '{\n        \"BodyContents\":\"\",\n        \"SubjectContents\":\"IFDS TRN Load Failed!\",\n        \"ToContents\"',':\"',pipeline().globalParameters.DDIMailAlerts,'\"',' \n    }'\n    )", "type": "Expression"}}}, {"name": "Load FS Orders to SQL MI", "type": "ExecutePipeline", "dependsOn": [{"activity": "IFDS_ADL_SQLMI", "dependencyConditions": ["Succeeded"]}], "policy": {"secureInput": false}, "userProperties": [], "typeProperties": {"pipeline": {"referenceName": "Load FS Orders to SQL MI", "type": "PipelineReference"}, "waitOnCompletion": true, "parameters": {"FileDate": {"value": "@variables('Date')", "type": "Expression"}}}}, {"name": "Execute IFDS Cash Flow Reports", "type": "ExecutePipeline", "dependsOn": [{"activity": "IFDS Daily Files Download", "dependencyConditions": ["Succeeded"]}], "policy": {"secureInput": false}, "userProperties": [], "typeProperties": {"pipeline": {"referenceName": "IN-751 IFDS CashFlow Reports", "type": "PipelineReference"}, "waitOnCompletion": true, "parameters": {"FileDate": {"value": "@variables('Date')", "type": "Expression"}}}}, {"name": "IN-85 IFDS Production Report WorkFlow", "type": "ExecutePipeline", "dependsOn": [{"activity": "IN-105 Pipeline_MAWSHORTTRD", "dependencyConditions": ["Succeeded"]}], "policy": {"secureInput": false}, "userProperties": [], "typeProperties": {"pipeline": {"referenceName": "DECOM- IN-85 IFDS Production Report WorkFlow", "type": "PipelineReference"}, "waitOnCompletion": true, "parameters": {"ADLRawFileDirectory": {"value": "@variables('ADLSourceDirectory')", "type": "Expression"}, "Date": {"value": "@variables('Date')", "type": "Expression"}}}}, {"name": "IN-91 IFDS Account", "type": "ExecutePipeline", "dependsOn": [{"activity": "IN-105 Pipeline_MAWSHORTTRD", "dependencyConditions": ["Succeeded"]}], "policy": {"secureInput": false}, "userProperties": [], "typeProperties": {"pipeline": {"referenceName": "IN-91 IFDS Account", "type": "PipelineReference"}, "waitOnCompletion": true, "parameters": {"ADLRawFileDirectory": {"value": "@variables('ADLSourceDirectory')", "type": "Expression"}, "Date": {"value": "@variables('Date')", "type": "Expression"}}}}, {"name": "IN-92 IFDS DEALER", "type": "ExecutePipeline", "dependsOn": [{"activity": "IN-105 Pipeline_MAWSHORTTRD", "dependencyConditions": ["Succeeded"]}], "policy": {"secureInput": false}, "userProperties": [], "typeProperties": {"pipeline": {"referenceName": "IN-92 IFDS DEALER", "type": "PipelineReference"}, "waitOnCompletion": true, "parameters": {"ADLRawFileDirectory": {"value": "@variables('ADLSourceDirectory')", "type": "Expression"}, "Date": {"value": "@variables('Date')", "type": "Expression"}}}}, {"name": "IN-101 External Files", "type": "ExecutePipeline", "dependsOn": [{"activity": "IN-105 Pipeline_MAWSHORTTRD", "dependencyConditions": ["Succeeded"]}], "policy": {"secureInput": false}, "userProperties": [], "typeProperties": {"pipeline": {"referenceName": "IN-101 External Files", "type": "PipelineReference"}, "waitOnCompletion": true, "parameters": {"ADLRawFileDirectory": {"value": "@variables('ADLSourceDirectory')", "type": "Expression"}, "Date": {"value": "@variables('Date')", "type": "Expression"}}}}, {"name": "IN-105 Pipeline_MAWSHORTTRD", "type": "ExecutePipeline", "dependsOn": [{"activity": "IFDS Daily Files Download", "dependencyConditions": ["Succeeded"]}], "policy": {"secureInput": false}, "userProperties": [], "typeProperties": {"pipeline": {"referenceName": "IN-105 Pipeline_MAWSHORTTRD", "type": "PipelineReference"}, "waitOnCompletion": true, "parameters": {"ADLRawFileDirectory": {"value": "@variables('ADLSourceDirectory')", "type": "Expression"}, "Date": {"value": "@variables('Date')", "type": "Expression"}}}}, {"name": "IFDS Load to ADX", "type": "ExecutePipeline", "dependsOn": [{"activity": "IFDS Daily Files Download", "dependencyConditions": ["Succeeded"]}], "policy": {"secureInput": false}, "userProperties": [], "typeProperties": {"pipeline": {"referenceName": "IN-132 IFDS Stage Files in ADX_Beta", "type": "PipelineReference"}, "waitOnCompletion": true, "parameters": {"pDate": {"value": "@variables('Date')", "type": "Expression"}, "pDatePlus1D": {"value": "@variables('vDatePlus1D')", "type": "Expression"}}}}, {"name": "Set vDatePlus1D", "type": "SetVariable", "dependsOn": [{"activity": "Set Date", "dependencyConditions": ["Succeeded"]}], "policy": {"secureOutput": false, "secureInput": false}, "userProperties": [], "typeProperties": {"variableName": "vDatePlus1D", "value": {"value": "@addDays(variables('Date'), 1,'yyyyMMdd')", "type": "Expression"}}}, {"name": "IFDS Positions", "type": "ExecutePipeline", "dependsOn": [{"activity": "IFDS Load to ADX", "dependencyConditions": ["Succeeded"]}], "policy": {"secureInput": false}, "userProperties": [], "typeProperties": {"pipeline": {"referenceName": "IN-133 IFDS Positions_Beta", "type": "PipelineReference"}, "waitOnCompletion": true, "parameters": {"pDate": {"value": "@variables('Date')", "type": "Expression"}, "pADLPath": {"value": "@pipeline().parameters.pADLPath", "type": "Expression"}, "pADLContainer": {"value": "@pipeline().parameters.pADLContainer", "type": "Expression"}, "pSendToSCD": false, "pSCDsftpPath": {"value": "@concat(pipeline().globalParameters.SCD_ENV, '/ActiveData/Reconciliation/IFDS/Working')", "type": "Expression"}, "pDataType": {"value": "@pipeline().parameters.pDataType", "type": "Expression"}, "pDatePlus1D": {"value": "@variables('vDatePlus1D')", "type": "Expression"}}}}]}}], "parameters": {"pDate": {"type": "string"}, "pADLPath": {"type": "string", "defaultValue": "IFDS"}, "pADLContainer": {"type": "string", "defaultValue": "scdintegration"}, "pSendToSCD": {"type": "bool", "defaultValue": true}, "pSCDsftpPath": {"type": "string"}, "pDataType": {"type": "string"}}, "variables": {"Date": {"type": "String"}, "ADLSourceDirectory": {"type": "String"}, "vDatePlus1D": {"type": "String"}}, "folder": {"name": "IFDS/IFDS Workflow"}, "annotations": [], "lastPublishTime": "2024-03-22T21:54:37Z"}, "type": "Microsoft.DataFactory/factories/pipelines"}