{"name": "IMW MI SCDStaging Load", "properties": {"activities": [{"name": "CompleteLoad", "type": "ExecutePipeline", "dependsOn": [], "policy": {"secureInput": false}, "userProperties": [], "typeProperties": {"pipeline": {"referenceName": "DECOM- CompleteLoad", "type": "PipelineReference"}, "waitOnCompletion": true}}, {"name": "IncrementalLoad_Positions", "type": "ExecutePipeline", "dependsOn": [], "policy": {"secureInput": false}, "userProperties": [], "typeProperties": {"pipeline": {"referenceName": "IncrementalLoad_Positions", "type": "PipelineReference"}, "waitOnCompletion": true}}, {"name": "IncrementalLoad_Transactions", "type": "ExecutePipeline", "dependsOn": [], "policy": {"secureInput": false}, "userProperties": [], "typeProperties": {"pipeline": {"referenceName": "IncrementalLoad_Transactions", "type": "PipelineReference"}, "waitOnCompletion": true}}, {"name": "CompleteLoad_Part2", "type": "ExecutePipeline", "dependsOn": [], "policy": {"secureInput": false}, "userProperties": [], "typeProperties": {"pipeline": {"referenceName": "DECOM- CompleteLoad_Part2", "type": "PipelineReference"}, "waitOnCompletion": true}}, {"name": "Portfolio_Custodian_Complete_Load", "type": "ExecutePipeline", "dependsOn": [], "policy": {"secureInput": false}, "userProperties": [], "typeProperties": {"pipeline": {"referenceName": "Portfolio_Custodian_Complete_Load", "type": "PipelineReference"}, "waitOnCompletion": true}}, {"name": "IncrementalLoad_TRANSACTIONS_SUBLEDGER", "type": "ExecutePipeline", "dependsOn": [], "policy": {"secureInput": false}, "userProperties": [], "typeProperties": {"pipeline": {"referenceName": "IncrementalLoad_DM_TRANSACTIONS_SUBLEDGER", "type": "PipelineReference"}, "waitOnCompletion": true}}, {"name": "Incremental Open Payments", "type": "ExecutePipeline", "dependsOn": [], "policy": {"secureInput": false}, "userProperties": [], "typeProperties": {"pipeline": {"referenceName": "IncrementalLoad_Open_Payments", "type": "PipelineReference"}, "waitOnCompletion": true}}, {"name": "Incremental DM_CIT_LLC_FUND_PERFORMANCE", "type": "ExecutePipeline", "dependsOn": [], "policy": {"secureInput": false}, "userProperties": [], "typeProperties": {"pipeline": {"referenceName": "IncrementalLoad_CIT_LLC_FUND_PERFORMANCE", "type": "PipelineReference"}, "waitOnCompletion": true}}], "folder": {"name": "Internal/IMW/IWM-MI CS CP"}, "annotations": [], "lastPublishTime": "2023-12-01T04:12:39Z"}, "type": "Microsoft.DataFactory/factories/pipelines"}