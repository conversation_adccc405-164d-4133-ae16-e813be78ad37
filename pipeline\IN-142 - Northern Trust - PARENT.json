{"name": "IN-142 - Northern Trust - PARENT", "properties": {"activities": [{"name": "Execute ETL", "type": "ExecutePipeline", "dependsOn": [{"activity": "Convert TXT to CSV", "dependencyConditions": ["Succeeded"]}], "policy": {"secureInput": false}, "userProperties": [], "typeProperties": {"pipeline": {"referenceName": "IN-142 - Northern Trust - CHILD", "type": "PipelineReference"}, "waitOnCompletion": true, "parameters": {"P_DATE": {"value": "@variables('Date')", "type": "Expression"}}}}, {"name": "Set Date", "type": "SetVariable", "dependsOn": [], "policy": {"secureOutput": false, "secureInput": false}, "userProperties": [], "typeProperties": {"variableName": "Date", "value": {"value": "@formatDatetime(adddays(formatDatetime(convertFromUtc(utcnow(), 'Mountain Standard Time'), 'yyyy-MM-dd'), -1), 'yyyyMMdd')", "type": "Expression"}}}, {"name": "Convert TXT to CSV", "type": "Copy", "dependsOn": [{"activity": "Set Date", "dependencyConditions": ["Succeeded"]}], "policy": {"timeout": "7.00:00:00", "retry": 0, "retryIntervalInSeconds": 30, "secureOutput": false, "secureInput": false}, "userProperties": [], "typeProperties": {"source": {"type": "DelimitedTextSource", "storeSettings": {"type": "AzureBlobFSReadSettings", "recursive": true, "enablePartitionDiscovery": false}, "formatSettings": {"type": "DelimitedTextReadSettings"}}, "sink": {"type": "DelimitedTextSink", "storeSettings": {"type": "AzureBlobFSWriteSettings"}, "formatSettings": {"type": "DelimitedTextWriteSettings", "quoteAllText": true, "fileExtension": ".txt"}}, "enableStaging": false}, "inputs": [{"referenceName": "ADL_Delimited_File", "type": "DatasetReference", "parameters": {"FilePath": {"value": "@concat('Custodian Data/Northern Trust/Northern Trust Recon/ARCHIVE/',variables('Date'),'/IN')", "type": "Expression"}, "FileName": {"value": "@concat(variables('Date'),'_Pending_FX_Detail_by_Currency.TXT')", "type": "Expression"}, "ColDelimiter": {"value": "@concat('\t')", "type": "Expression"}, "FirstRowHeader": true}}], "outputs": [{"referenceName": "ADL_Delimited_File", "type": "DatasetReference", "parameters": {"FilePath": "FX/Northern Trust/TEMP", "FileName": "PendingFXDetailByCurrencyTemp.csv", "ColDelimiter": ",", "FirstRowHeader": true}}]}, {"name": "Success Email", "type": "WebActivity", "dependsOn": [{"activity": "Execute ETL", "dependencyConditions": ["Succeeded"]}], "policy": {"timeout": "7.00:00:00", "retry": 0, "retryIntervalInSeconds": 30, "secureOutput": false, "secureInput": false}, "userProperties": [], "typeProperties": {"method": "POST", "headers": {"Content-Type": "application/json"}, "url": {"value": "@pipeline().globalParameters.SendEmailLogicFunctionUrl", "type": "Expression"}, "body": {"value": "@concat(\n    '{\n        \"BodyContents\":\"\",\n        \"SubjectContents\":\"Northern Trust FX is completed\",\n        \"ToContents\"',':\"',pipeline().globalParameters.DDIMailAlerts,'\"',' \n    }'\n    )", "type": "Expression"}}}, {"name": "Failure E-mail", "type": "WebActivity", "dependsOn": [{"activity": "Convert TXT to CSV", "dependencyConditions": ["Failed"]}], "policy": {"timeout": "7.00:00:00", "retry": 0, "retryIntervalInSeconds": 30, "secureOutput": false, "secureInput": false}, "userProperties": [], "typeProperties": {"method": "POST", "headers": {"Content-Type": "application/json"}, "url": {"value": "@pipeline().globalParameters.SendEmailLogicFunctionUrl", "type": "Expression"}, "body": {"value": "@concat(\n    '{\n        \"BodyContents\":\"\",\n        \"SubjectContents\":\"Northern Trust FX filter criteria data not received\",\n        \"ToContents\"',':\"',pipeline().globalParameters.DDIMailAlerts,'\"',' \n    }'\n    )", "type": "Expression"}}}], "variables": {"Date": {"type": "String"}}, "folder": {"name": "Northern Trust/Northern Trust FX"}, "annotations": [], "lastPublishTime": "2024-05-29T21:01:01Z"}, "type": "Microsoft.DataFactory/factories/pipelines"}