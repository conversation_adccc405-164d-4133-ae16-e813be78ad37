{"name": "MBSFactOutFile", "properties": {"linkedServiceName": {"referenceName": "AzureDataLakeStorage", "type": "LinkedServiceReference"}, "parameters": {"Root": {"type": "string", "defaultValue": "scdintegration"}, "FolderPath": {"type": "string", "defaultValue": "DDI Migration/MBSFactSet/OutPutFile"}}, "folder": {"name": "Inbound/DDI/IN-329 DDI Migration/IN-330 MBSFact Sets"}, "annotations": [], "type": "DelimitedText", "typeProperties": {"location": {"type": "AzureBlobFSLocation", "folderPath": {"value": "@dataset().FolderPath", "type": "Expression"}, "fileSystem": {"value": "@dataset().Root", "type": "Expression"}}, "columnDelimiter": "|", "escapeChar": "\\", "quoteChar": ""}, "schema": [{"type": "String"}, {"type": "String"}, {"type": "String"}, {"type": "String"}, {"type": "String"}, {"type": "String"}, {"type": "String"}, {"type": "String"}, {"type": "String"}, {"type": "String"}, {"type": "String"}, {"type": "String"}, {"type": "String"}, {"type": "String"}, {"type": "String"}, {"type": "String"}, {"type": "String"}, {"type": "String"}, {"type": "String"}, {"type": "String"}, {"type": "String"}, {"type": "String"}, {"type": "String"}, {"type": "String"}, {"type": "String"}, {"type": "String"}, {"type": "String"}, {"type": "String"}, {"type": "String"}, {"type": "String"}, {"type": "String"}, {"type": "String"}, {"type": "String"}, {"type": "String"}, {"type": "String"}, {"type": "String"}, {"type": "String"}, {"type": "String"}, {"type": "String"}, {"type": "String"}, {"type": "String"}, {"type": "String"}, {"type": "String"}, {"type": "String"}, {"type": "String"}, {"type": "String"}, {"type": "String"}, {"type": "String"}, {"type": "String"}, {"type": "String"}, {"type": "String"}, {"type": "String"}, {"type": "String"}, {"type": "String"}, {"type": "String"}, {"type": "String"}, {"type": "String"}, {"type": "String"}, {"type": "String"}, {"type": "String"}, {"type": "String"}, {"type": "String"}, {"type": "String"}, {"type": "String"}, {"type": "String"}, {"type": "String"}, {"type": "String"}, {"type": "String"}, {"type": "String"}, {"type": "String"}, {"type": "String"}, {"type": "String"}, {"type": "String"}]}, "type": "Microsoft.DataFactory/factories/datasets"}