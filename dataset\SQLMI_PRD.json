{"name": "SQLMI_PRD", "properties": {"linkedServiceName": {"referenceName": "SQLMI_RESEARCH_PRD", "type": "LinkedServiceReference", "parameters": {"DBName": {"value": "@dataset().DBName", "type": "Expression"}}}, "parameters": {"DBName": {"type": "string"}, "Schema": {"type": "string"}, "Table": {"type": "string"}}, "folder": {"name": "Inbound/Transactions and Orders/IFDS"}, "annotations": [], "type": "AzureSqlMITable", "schema": [], "typeProperties": {"schema": {"value": "@dataset().Schema", "type": "Expression"}, "table": {"value": "@dataset().Table", "type": "Expression"}}}, "type": "Microsoft.DataFactory/factories/datasets"}