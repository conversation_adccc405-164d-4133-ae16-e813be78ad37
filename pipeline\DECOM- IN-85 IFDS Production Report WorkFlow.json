{"name": "DECOM- IN-85 IFDS Production Report WorkFlow", "properties": {"activities": [{"name": "Send email web app", "type": "WebActivity", "dependsOn": [{"activity": "Report5_Data_Flow", "dependencyConditions": ["Succeeded"]}], "policy": {"timeout": "7.00:00:00", "retry": 0, "retryIntervalInSeconds": 30, "secureOutput": false, "secureInput": false}, "userProperties": [], "typeProperties": {"method": "POST", "headers": {"Content-Type": "application/json"}, "url": {"value": "@pipeline().globalParameters.SendEmailLogicFunctionUrl", "type": "Expression"}, "body": {"value": "@concat('{\"BodyContents\":\"All, please find attached Production Reports.\",\"FolderLocation\":\"/scdintegration/IFDS/Production Report/OUT\",',\n'\"SubjectContents\":\"Production Reports\",\"ToContents\"',\n':\"',pipeline().globalParameters.ProdReportEmails,'\"',' }')", "type": "Expression"}}}, {"name": "Report5_Data_Flow", "type": "ExecuteDataFlow", "dependsOn": [{"activity": "ZPACSWP5_Data_Flow", "dependencyConditions": ["Succeeded"]}], "policy": {"timeout": "7.00:00:00", "retry": 0, "retryIntervalInSeconds": 30, "secureOutput": false, "secureInput": false}, "userProperties": [], "typeProperties": {"dataflow": {"referenceName": "IN85_IFDS_Report5_DF", "type": "DataFlowReference", "parameters": {"ADLFileDirectory": {"value": "'@{pipeline().parameters.ADLRawFileDirectory}'", "type": "Expression"}}}, "compute": {"coreCount": 8, "computeType": "General"}, "traceLevel": "Fine"}}, {"name": "ZPACSWP5_Data_Flow", "type": "ExecuteDataFlow", "dependsOn": [], "policy": {"timeout": "7.00:00:00", "retry": 0, "retryIntervalInSeconds": 30, "secureOutput": false, "secureInput": false}, "userProperties": [], "typeProperties": {"dataflow": {"referenceName": "IN85_IFDS_ZPACSWP5_DF", "type": "DataFlowReference", "parameters": {"ADLFileDirectory": {"value": "'@{pipeline().parameters.ADLRawFileDIrectory}'", "type": "Expression"}}}, "compute": {"coreCount": 8, "computeType": "General"}, "traceLevel": "Fine"}}, {"name": "Cleanup - ARCHIVE OUT", "type": "Copy", "dependsOn": [{"activity": "Send email web app", "dependencyConditions": ["Succeeded"]}], "policy": {"timeout": "7.00:00:00", "retry": 0, "retryIntervalInSeconds": 30, "secureOutput": false, "secureInput": false}, "userProperties": [], "typeProperties": {"source": {"type": "BinarySource", "storeSettings": {"type": "AzureBlobFSReadSettings", "recursive": true, "deleteFilesAfterCompletion": true}, "formatSettings": {"type": "BinaryReadSettings"}}, "sink": {"type": "BinarySink", "storeSettings": {"type": "AzureBlobFSWriteSettings"}}, "enableStaging": false}, "inputs": [{"referenceName": "AzureDataLakeStorage_IFDS_RawFiles_IN_01", "type": "DatasetReference", "parameters": {"DIrectory": "IFDS/Production Report/OUT"}}], "outputs": [{"referenceName": "AzureDataLakeStorage_IFDS_RawFiles_IN_01", "type": "DatasetReference", "parameters": {"DIrectory": {"value": "@concat('IFDS/Production Report/ARCHIVE/',pipeline().parameters.Date,'/OUT')", "type": "Expression"}}}]}], "parameters": {"ADLRawFileDirectory": {"type": "string"}, "Date": {"type": "string"}}, "folder": {"name": "IFDS/IFDS Integration"}, "annotations": [], "lastPublishTime": "2023-12-01T04:11:17Z"}, "type": "Microsoft.DataFactory/factories/pipelines"}