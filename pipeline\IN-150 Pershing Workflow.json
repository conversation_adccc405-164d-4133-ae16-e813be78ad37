{"name": "IN-150 Pershing Workflow", "properties": {"activities": [{"name": "Run Only In PROD", "type": "IfCondition", "dependsOn": [], "userProperties": [], "typeProperties": {"expression": {"value": "@equals(pipeline().DataFactory,'AZ-PRD-UW-DF-SCD')", "type": "Expression"}, "ifTrueActivities": [{"name": "Execute Pershing FX", "type": "ExecutePipeline", "dependsOn": [{"activity": "Stage All Files", "dependencyConditions": ["Succeeded"]}], "policy": {"secureInput": false}, "userProperties": [], "typeProperties": {"pipeline": {"referenceName": "IN-152 Pershing FX", "type": "PipelineReference"}, "waitOnCompletion": true, "parameters": {"pDate": {"value": "@variables('vDate')", "type": "Expression"}, "pADLPath": "Custodian Data/Pershing/Pershing FX", "pADLContainer": {"value": "@pipeline().parameters.pADLContainer", "type": "Expression"}, "pSendToSCD": {"value": "@pipeline().parameters.pSendToSCD", "type": "Expression"}, "pSCDsftpPath": {"value": "@concat(pipeline().globalParameters.SCD_ENV, \npipeline().globalParameters.SCD_SFTP_FX)", "type": "Expression"}}}}, {"name": "Stage All Files", "type": "ExecutePipeline", "dependsOn": [{"activity": "Set vDate Variable", "dependencyConditions": ["Succeeded"]}], "policy": {"secureInput": false}, "userProperties": [], "typeProperties": {"pipeline": {"referenceName": "IN-150 Pershing Workflow Stage Files", "type": "PipelineReference"}, "waitOnCompletion": true, "parameters": {"pRecipientEmail": "<EMAIL>", "pDate": {"value": "@variables('vDate')", "type": "Expression"}, "pADLPath": {"value": "@concat('Custodian Data/Pershing/Zipped/',variables('vDate'),'/')", "type": "Expression"}}}}, {"name": "Set vDate Variable", "type": "SetVariable", "dependsOn": [{"activity": "Find the last Biz Date", "dependencyConditions": ["Succeeded"]}], "policy": {"secureOutput": false, "secureInput": false}, "userProperties": [], "typeProperties": {"variableName": "vDate", "value": {"value": "@formatDateTime( addDays(convertFromUtc(utcnow(), 'Mountain Standard Time'),-1),'yyyyMMdd')", "type": "Expression"}}}, {"name": "Find the last Biz Date", "type": "Lookup", "state": "Inactive", "onInactiveMarkAs": "Succeeded", "dependsOn": [], "policy": {"timeout": "7.00:00:00", "retry": 0, "retryIntervalInSeconds": 30, "secureOutput": false, "secureInput": false}, "userProperties": [], "typeProperties": {"source": {"type": "SqlMISource", "sqlReaderQuery": {"value": "@concat('SELECT format([PREV_BD_TSE],''yyyyMMdd'') TD_1Date\n  FROM [DataCore].[dbo].[vw_Calendar]\n  where date_iso = ''',formatDateTime(convertFromUtc(utcnow(),'Mountain Standard Time'),'yyyy-MM-dd'),'''')", "type": "Expression"}, "partitionOption": "None"}, "dataset": {"referenceName": "SQLMI_MOW_PARAM", "type": "DatasetReference", "parameters": {"DBName": "DataCore", "SchemaName": "dbo", "TableName": "vw_Calendar"}}}}, {"name": "Execute Pershing Recon", "type": "ExecutePipeline", "dependsOn": [{"activity": "Stage Transaction", "dependencyConditions": ["Succeeded"]}], "policy": {"secureInput": false}, "userProperties": [], "typeProperties": {"pipeline": {"referenceName": "IN-151 Pershing Recon", "type": "PipelineReference"}, "waitOnCompletion": true, "parameters": {"pDate": {"value": "@variables('vDate')", "type": "Expression"}, "pADLPath": "Custodian Data/Pershing/Pershing Recon", "pADLContainer": {"value": "@pipeline().parameters.pADLContainer", "type": "Expression"}, "pSendToSCD": {"value": "@pipeline().parameters.pSendToSCD", "type": "Expression"}, "pSCDsftpPath": {"value": "@concat(pipeline().globalParameters.SCD_ENV, \n'/ActiveData/Reconciliation/BNYM/Working'\n)", "type": "Expression"}}}}, {"name": "Stage Transaction", "type": "Copy", "dependsOn": [{"activity": "Stage All Files", "dependencyConditions": ["Succeeded"]}], "policy": {"timeout": "0.12:00:00", "retry": 0, "retryIntervalInSeconds": 30, "secureOutput": false, "secureInput": false}, "userProperties": [], "typeProperties": {"source": {"type": "DelimitedTextSource", "additionalColumns": [{"name": "DataDate", "value": {"value": "@variables('vDate')", "type": "Expression"}}], "storeSettings": {"type": "AzureBlobFSReadSettings", "recursive": true, "enablePartitionDiscovery": false}, "formatSettings": {"type": "DelimitedTextReadSettings"}}, "sink": {"type": "SqlMISink", "preCopyScript": {"value": "@{concat('DELETE FROM [Integration].[Recon].[PershingTransactionsStaging] \nWHERE [DataDate] = ''',variables('vDate'),'''')}", "type": "Expression"}, "writeBehavior": "insert", "sqlWriterUseTableLock": false}, "enableStaging": false, "translator": {"type": "TabularTranslator", "mappings": [{"source": {"name": "Activity Date", "type": "String", "physicalType": "String"}, "sink": {"name": "Activity Date", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}, {"source": {"name": "Process Date", "type": "String", "physicalType": "String"}, "sink": {"name": "Process Date", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}, {"source": {"name": "Trade Date", "type": "String", "physicalType": "String"}, "sink": {"name": "Trade Date", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}, {"source": {"name": "Settlement Date", "type": "String", "physicalType": "String"}, "sink": {"name": "Settlement Date", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}, {"source": {"name": "Pay date", "type": "String", "physicalType": "String"}, "sink": {"name": "Pay date", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}, {"source": {"name": "Account Number", "type": "String", "physicalType": "String"}, "sink": {"name": "Account Number", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}, {"source": {"name": "Account Type", "type": "String", "physicalType": "String"}, "sink": {"name": "Account Type", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}, {"source": {"name": "CUSIP", "type": "String", "physicalType": "String"}, "sink": {"name": "CUSIP", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}, {"source": {"name": "ISIN", "type": "String", "physicalType": "String"}, "sink": {"name": "ISIN", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}, {"source": {"name": "SYMBOL", "type": "String", "physicalType": "String"}, "sink": {"name": "SYMBOL", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}, {"source": {"name": "Security Identifier", "type": "String", "physicalType": "String"}, "sink": {"name": "Security Identifier", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}, {"source": {"name": "Security Description", "type": "String", "physicalType": "String"}, "sink": {"name": "Security Description", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}, {"source": {"name": "Security Name", "type": "String", "physicalType": "String"}, "sink": {"name": "Security Name", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}, {"source": {"name": "Transaction code", "type": "String", "physicalType": "String"}, "sink": {"name": "Transaction code", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}, {"source": {"name": "Transaction Description", "type": "String", "physicalType": "String"}, "sink": {"name": "Transaction Description", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}, {"source": {"name": "Activity Type Description", "type": "String", "physicalType": "String"}, "sink": {"name": "Activity Type Description", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}, {"source": {"name": "Transaction Type Code", "type": "String", "physicalType": "String"}, "sink": {"name": "Transaction Type Code", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}, {"source": {"name": "Transaction Type Description", "type": "String", "physicalType": "String"}, "sink": {"name": "Transaction Type Description", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}, {"source": {"name": "Activity Type Code", "type": "String", "physicalType": "String"}, "sink": {"name": "Activity Type", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}, {"source": {"name": "Activity Description", "type": "String", "physicalType": "String"}, "sink": {"name": "Activity Description", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}, {"source": {"name": " Currency Description", "type": "String", "physicalType": "String"}, "sink": {"name": "Currency Description", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}, {"source": {"name": "Activity Amount", "type": "String", "physicalType": "String"}, "sink": {"name": "Activity Amount", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}, {"source": {"name": "Net Amount (Transaction Currency)", "type": "String", "physicalType": "String"}, "sink": {"name": "Net Amount (Transaction Currency)", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}, {"source": {"name": "Net Amount (Base Currency)", "type": "String", "physicalType": "String"}, "sink": {"name": "Net Amount (Base Currency)", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}, {"source": {"name": "Total Amount", "type": "String", "physicalType": "String"}, "sink": {"name": "Total Amount", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}, {"source": {"name": "Quantity", "type": "String", "physicalType": "String"}, "sink": {"name": "Quantity", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}, {"source": {"name": "Order Quantity", "type": "String", "physicalType": "String"}, "sink": {"name": "Order Quantity", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}, {"source": {"name": "Price (Transaction Currency)", "type": "String", "physicalType": "String"}, "sink": {"name": "Price (Transaction Currency)", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}, {"source": {"name": "Asset Sub Sub Type Code", "type": "String", "physicalType": "String"}, "sink": {"name": "Asset Sub Sub Type Code", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}, {"source": {"name": "Asset Sub Sub Type Description", "type": "String", "physicalType": "String"}, "sink": {"name": "Asset Sub Sub Type Description", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}, {"source": {"name": "Asset Sub Type Code", "type": "String", "physicalType": "String"}, "sink": {"name": "Asset Sub Type Code", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}, {"source": {"name": "Asset Sub Type Description", "type": "String", "physicalType": "String"}, "sink": {"name": "Asset Sub Type Description", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}, {"source": {"name": "Asset Type Code", "type": "String", "physicalType": "String"}, "sink": {"name": "Asset Type Code", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}, {"source": {"name": "Asset Type Description", "type": "String", "physicalType": "String"}, "sink": {"name": "Asset Type Description", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}, {"source": {"name": "Buy/Sell", "type": "String", "physicalType": "String"}, "sink": {"name": "Buy/Sell", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}, {"source": {"name": "Commission", "type": "String", "physicalType": "String"}, "sink": {"name": "Commission", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}, {"source": {"name": "Credit", "type": "String", "physicalType": "String"}, "sink": {"name": "Credit", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}, {"source": {"name": "Currency Code", "type": "String", "physicalType": "String"}, "sink": {"name": "Currency Code", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}, {"source": {"name": "Debit", "type": "String", "physicalType": "String"}, "sink": {"name": "Debit", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}, {"source": {"name": "Dividend Rate", "type": "String", "physicalType": "String"}, "sink": {"name": "Dividend Rate", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}, {"source": {"name": "Ex Date", "type": "String", "physicalType": "String"}, "sink": {"name": "Ex Date", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}, {"source": {"name": "Ex Rate", "type": "String", "physicalType": "String"}, "sink": {"name": "Ex Rate", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}, {"source": {"name": "Exchange Code", "type": "String", "physicalType": "String"}, "sink": {"name": "Exchange Code", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}, {"source": {"name": "Exchange Description", "type": "String", "physicalType": "String"}, "sink": {"name": "Exchange Description", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}, {"source": {"name": "FX Rate (To Base)", "type": "String", "physicalType": "String"}, "sink": {"name": "FX Rate (To Base)", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}, {"source": {"name": "FX Reference Identifier", "type": "String", "physicalType": "String"}, "sink": {"name": "FX Reference Identifier", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}, {"source": {"name": "Gross", "type": "String", "physicalType": "String"}, "sink": {"name": "Gross", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}, {"source": {"name": "Income", "type": "String", "physicalType": "String"}, "sink": {"name": "Income", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}, {"source": {"name": "Activity Type", "type": "String", "physicalType": "String"}, "sink": {"name": "Income Type", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}, {"source": {"name": "Transaction Currency", "type": "String", "physicalType": "String"}, "sink": {"name": "Issue Currency code", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}, {"source": {"name": "Fees", "type": "String", "physicalType": "String"}, "sink": {"name": "Miscellaneous Fee Amount", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}, {"source": {"name": "Other Fee Amount", "type": "String", "physicalType": "String"}, "sink": {"name": "Other Fee Amount", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}, {"source": {"name": "Principal", "type": "String", "physicalType": "String"}, "sink": {"name": "Principal", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}, {"source": {"name": "SEC Fee Amount", "type": "String", "physicalType": "String"}, "sink": {"name": "SEC Fee Amount", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}, {"source": {"name": "Tax Amount", "type": "String", "physicalType": "String"}, "sink": {"name": "Tax Amount", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}, {"source": {"name": "Taxable", "type": "String", "physicalType": "String"}, "sink": {"name": "Taxable", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}, {"source": {"name": "Taxable Code Description", "type": "String", "physicalType": "String"}, "sink": {"name": "Taxable Code Description", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}, {"source": {"name": "Trade Activity Type Code", "type": "String", "physicalType": "String"}, "sink": {"name": "Trade Activity Type Code", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}, {"source": {"name": "Trade Activity Type Description", "type": "String", "physicalType": "String"}, "sink": {"name": "Trade Activity Type Description", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}, {"source": {"name": "Trade Reference Number", "type": "String", "physicalType": "String"}, "sink": {"name": "Trade Reference Number", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}, {"source": {"name": "Tram ID", "type": "String", "physicalType": "String"}, "sink": {"name": "Tram ID", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}, {"source": {"name": "DataDate", "type": "String"}, "sink": {"name": "DataDate", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}], "typeConversion": true, "typeConversionSettings": {"allowDataTruncation": true, "treatBooleanAsNumber": false}}}, "inputs": [{"referenceName": "ADL_Delimited_File", "type": "DatasetReference", "parameters": {"FilePath": {"value": "@concat(pipeline().parameters.pADLPath,'/',pipeline().parameters.pDate)", "type": "Expression"}, "FileName": {"value": "@concat(pipeline().parameters.pDate,'BNYMTransactions.csv')", "type": "Expression"}, "ColDelimiter": ",", "FirstRowHeader": true}}], "outputs": [{"referenceName": "SQLMI_MOW_PARAM", "type": "DatasetReference", "parameters": {"DBName": "Integration", "SchemaName": "Recon", "TableName": "PershingTransactionsStaging"}}]}, {"name": "Check for Holiday run", "type": "ExecutePipeline", "dependsOn": [{"activity": "Set vDate Variable", "dependencyConditions": ["Succeeded"]}], "policy": {"secureInput": false}, "userProperties": [], "typeProperties": {"pipeline": {"referenceName": "HOLIDAY_RUN_ALERT", "type": "PipelineReference"}, "waitOnCompletion": true, "parameters": {"RunDate": {"value": "@variables('vDate')", "type": "Expression"}, "PipelineName": {"value": "@pipeline().Pipeline", "type": "Expression"}}}}]}}], "parameters": {"pDate": {"type": "string"}, "pADLPath": {"type": "string", "defaultValue": "Custodian Data/Pershing/Netx360Payments"}, "pADLContainer": {"type": "string", "defaultValue": "scdintegration"}, "pSendToSCD": {"type": "bool"}, "pSCDsftpPath": {"type": "string"}, "pDataType": {"type": "string"}}, "variables": {"vDate": {"type": "String"}, "Today": {"type": "String"}}, "folder": {"name": "Custodians and Vendors/Pershing/Pershing Workflow"}, "annotations": [], "lastPublishTime": "2024-10-13T08:30:34Z"}, "type": "Microsoft.DataFactory/factories/pipelines"}