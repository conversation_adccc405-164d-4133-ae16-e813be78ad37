{"name": "DECOM- Portfolios CSV Template - SF to SCD", "properties": {"description": "Integrate Client data from Salesforce to SCD\nhttps://beboringmakedata.atlassian.net/browse/IN-383\n", "activities": [{"name": "Portfolios", "type": "Switch", "dependsOn": [{"activity": "Set File Name", "dependencyConditions": ["Succeeded"]}], "userProperties": [], "typeProperties": {"on": {"value": "@pipeline().parameters.ObjectName", "type": "Expression"}, "cases": [{"value": "Account", "activities": [{"name": "Copy Portfolio Account Data", "type": "Copy", "dependsOn": [], "policy": {"timeout": "7.00:00:00", "retry": 0, "retryIntervalInSeconds": 30, "secureOutput": false, "secureInput": false}, "userProperties": [], "typeProperties": {"source": {"type": "SqlMISource", "sqlReaderQuery": {"value": "@concat(\n'SELECT [Portfolio Group] ,[Portfolio Group Name] ,[Portfolio Group Manager] ,[Portfolio Group Currency] ,[Portfolio ID] ,[Portfolio name] ,[Long reporting portfolio name] ,[Portfolio Manager] ,[Portfolio Currency] ,[Taxable Status (Portfolio free code 77)] ,[Mawer Portfolio Type (Portfolio free code 78)] ,[Management type (Portfolio free code 79)] ,[Ownership type (Portfolio free code 80)] ,[Leveraged account (Portfolio free code 82)] ,[Investment Objective (Portfolio free code 90)] ,[Anti-money Laundering Risk (Portfolio free code 85)] ,[Cross Trade Approval (Portfolio free code 84)] ,[Holdings type (Portfolio free code 81)] ,[Collateral account (Portfolio free code 83)] ,[Business Segment (Portfolio free code 91)] ,[Permitted Client (Portfolio free code 86)] ,[Proxy Voting (Portfolio free code 62)] ,[Risk tolerance (Portfolio free code 88)] ,[Time Horizon (Portfolio free code 89)] ,[Portfolio Active Status (Portfolio free code 24)] ,[Insider Status (Portfolio free code 60)] ,[Politically Exposed Person (Portfolio free code 61)] ,[Portfolio Client Type (Portfolio Free Code 25)] ,[Portfolio Country] ,[Tax Jurisdiction (Portfolio free code 63)] ,[Accredited Investor (Portfolio free code 87)] ,[Opening date] ,[Closing date] ,[Inception date] ,[Primary Owner Contact Code (Portfolio Free code 1)] ,[Portfolio manager 1] ,[Portfolio manager 2] ,[Portfolio free code 71 (Marketing Segment)] ,[CTM Account ID] ,[Investment Discretion 13F US (Portfolio Free Code 66)] ,[SAAM Weights From Mix Committee (Portfolio Free Code 6)] ,[Bank Name #1 (Portfolio free code 30)] ,[Bank Info #1 (Portfolio free code 31)] ,[Bank Name #2 (Portfolio free code 32)] ,[Bank Info #2 (Portfolio free code 33)] ,[Bank Name #3 (Portfolio free code 34)] ,[Bank Info #3 (Portfolio free code 34)] ,[Bank Name #4 (Portfolio free code 35)] ,[Bank Info #4 (Portfolio free code 36)] ,[IFDS Account ID (Portfolio free code 44)] ,[Equity Upper Compliance Range (Portfolio group free code 15)] ,[Equity Lower Compliance Range (Portfolio group free code 16)] ,[From Portfolio (Portfolio free code 41)] ,[To Portfolio (Portfolio free code 42)] ,[Reason for Closure (Portfolio free code 43)] ,[IPO Eligible (Portfolio free code 27)] ,[Private Placement Eligible (Portfolio free code 28)] ,[Country of Residency (Portfolio free code 72)] ,[Country of Business (Portfolio free code 73)] ,[Portfolio Sub-Type (Portfolio free code 45)] ,[Qualified Instutional Buyer] ,[Withholding Tax] ,[Merge and Composite ID] ,[Reporting Currency (3 ISO code)] ,[Custodian currencies] FROM [scd].[vPortfolioPortfolioGroupChange] WHERE 1=1 AND [AccountId] in (',pipeline().parameters.ObjectId,')')", "type": "Expression"}, "partitionOption": "None"}, "sink": {"type": "DelimitedTextSink", "storeSettings": {"type": "AzureBlobFSWriteSettings"}, "formatSettings": {"type": "DelimitedTextWriteSettings", "quoteAllText": true, "fileExtension": ".csv"}}, "enableStaging": false}, "inputs": [{"referenceName": "SalesforceViews", "type": "DatasetReference"}], "outputs": [{"referenceName": "AzureDataLakeStorage_SalesforceToScd_PortfolioFile", "type": "DatasetReference", "parameters": {"FileName": {"value": "@variables('FileName')", "type": "Expression"}, "FolderName": {"value": "@concat('Salesforce To SCD/Daily Loads/',formatDateTime(convertFromUtc(utcnow(),'Mountain Standard Time'),'yyyyMMdd'))", "type": "Expression"}}}]}]}, {"value": "FinancialAccount", "activities": [{"name": "Copy FinAcct Data", "type": "Copy", "dependsOn": [], "policy": {"timeout": "7.00:00:00", "retry": 0, "retryIntervalInSeconds": 30, "secureOutput": false, "secureInput": false}, "userProperties": [], "typeProperties": {"source": {"type": "SqlMISource", "sqlReaderQuery": {"value": "@concat(\n'SELECT [Portfolio Group] ,[Portfolio Group Name] ,[Portfolio Group Manager] ,[Portfolio Group Currency] ,[Portfolio ID] ,[Portfolio name] ,[Long reporting portfolio name] ,[Portfolio Manager] ,[Portfolio Currency] ,[Taxable Status (Portfolio free code 77)] ,[Mawer Portfolio Type (Portfolio free code 78)] ,[Management type (Portfolio free code 79)] ,[Ownership type (Portfolio free code 80)] ,[Leveraged account (Portfolio free code 82)] ,[Investment Objective (Portfolio free code 90)] ,[Anti-money Laundering Risk (Portfolio free code 85)] ,[Cross Trade Approval (Portfolio free code 84)] ,[Holdings type (Portfolio free code 81)] ,[Collateral account (Portfolio free code 83)] ,[Business Segment (Portfolio free code 91)] ,[Permitted Client (Portfolio free code 86)] ,[Proxy Voting (Portfolio free code 62)] ,[Risk tolerance (Portfolio free code 88)] ,[Time Horizon (Portfolio free code 89)] ,[Portfolio Active Status (Portfolio free code 24)] ,[Insider Status (Portfolio free code 60)] ,[Politically Exposed Person (Portfolio free code 61)] ,[Portfolio Client Type (Portfolio Free Code 25)] ,[Portfolio Country] ,[Tax Jurisdiction (Portfolio free code 63)] ,[Accredited Investor (Portfolio free code 87)] ,[Opening date] ,[Closing date] ,[Inception date] ,[Primary Owner Contact Code (Portfolio Free code 1)] ,[Portfolio manager 1] ,[Portfolio manager 2] ,[Portfolio free code 71 (Marketing Segment)] ,[CTM Account ID] ,[Investment Discretion 13F US (Portfolio Free Code 66)] ,[SAAM Weights From Mix Committee (Portfolio Free Code 6)] ,[Bank Name #1 (Portfolio free code 30)] ,[Bank Info #1 (Portfolio free code 31)] ,[Bank Name #2 (Portfolio free code 32)] ,[Bank Info #2 (Portfolio free code 33)] ,[Bank Name #3 (Portfolio free code 34)] ,[Bank Info #3 (Portfolio free code 34)] ,[Bank Name #4 (Portfolio free code 35)] ,[Bank Info #4 (Portfolio free code 36)] ,[IFDS Account ID (Portfolio free code 44)] ,[Equity Upper Compliance Range (Portfolio group free code 15)] ,[Equity Lower Compliance Range (Portfolio group free code 16)] ,[From Portfolio (Portfolio free code 41)] ,[To Portfolio (Portfolio free code 42)] ,[Reason for Closure (Portfolio free code 43)] ,[IPO Eligible (Portfolio free code 27)] ,[Private Placement Eligible (Portfolio free code 28)] ,[Country of Residency (Portfolio free code 72)] ,[Country of Business (Portfolio free code 73)] ,[Portfolio Sub-Type (Portfolio free code 45)] ,[Qualified Instutional Buyer] ,[Withholding Tax] ,[Merge and Composite ID] ,[Reporting Currency (3 ISO code)] ,[Custodian currencies] FROM [scd].[vPortfolioPortfolioGroupChange] WHERE 1=1 AND [FinancialAccountId] in (',pipeline().parameters.ObjectId,')')", "type": "Expression"}, "partitionOption": "None"}, "sink": {"type": "DelimitedTextSink", "storeSettings": {"type": "AzureBlobFSWriteSettings"}, "formatSettings": {"type": "DelimitedTextWriteSettings", "quoteAllText": true, "fileExtension": ".csv"}}, "enableStaging": false}, "inputs": [{"referenceName": "SalesforceViews", "type": "DatasetReference"}], "outputs": [{"referenceName": "AzureDataLakeStorage_SalesforceToScd_PortfolioFile", "type": "DatasetReference", "parameters": {"FileName": {"value": "@variables('FileName')", "type": "Expression"}, "FolderName": {"value": "@concat('Salesforce To SCD/Daily Loads/',formatDateTime(convertFromUtc(utcnow(),'Mountain Standard Time'),'yyyyMMdd'))", "type": "Expression"}}}]}]}, {"value": "User", "activities": [{"name": "Copy User Data_copy1", "type": "Copy", "dependsOn": [], "policy": {"timeout": "7.00:00:00", "retry": 0, "retryIntervalInSeconds": 30, "secureOutput": false, "secureInput": false}, "userProperties": [], "typeProperties": {"source": {"type": "SqlMISource", "sqlReaderQuery": {"value": "@concat(\n'SELECT [Portfolio Group] ,[Portfolio Group Name] ,[Portfolio Group Manager] ,[Portfolio Group Currency] ,[Portfolio ID] ,[Portfolio name] ,[Long reporting portfolio name] ,[Portfolio Manager] ,[Portfolio Currency] ,[Taxable Status (Portfolio free code 77)] ,[Mawer Portfolio Type (Portfolio free code 78)] ,[Management type (Portfolio free code 79)] ,[Ownership type (Portfolio free code 80)] ,[Leveraged account (Portfolio free code 82)] ,[Investment Objective (Portfolio free code 90)] ,[Anti-money Laundering Risk (Portfolio free code 85)] ,[Cross Trade Approval (Portfolio free code 84)] ,[Holdings type (Portfolio free code 81)] ,[Collateral account (Portfolio free code 83)] ,[Business Segment (Portfolio free code 91)] ,[Permitted Client (Portfolio free code 86)] ,[Proxy Voting (Portfolio free code 62)] ,[Risk tolerance (Portfolio free code 88)] ,[Time Horizon (Portfolio free code 89)] ,[Portfolio Active Status (Portfolio free code 24)] ,[Insider Status (Portfolio free code 60)] ,[Politically Exposed Person (Portfolio free code 61)] ,[Portfolio Client Type (Portfolio Free Code 25)] ,[Portfolio Country] ,[Tax Jurisdiction (Portfolio free code 63)] ,[Accredited Investor (Portfolio free code 87)] ,[Opening date] ,[Closing date] ,[Inception date] ,[Primary Owner Contact Code (Portfolio Free code 1)] ,[Portfolio manager 1] ,[Portfolio manager 2] ,[Portfolio free code 71 (Marketing Segment)] ,[CTM Account ID] ,[Investment Discretion 13F US (Portfolio Free Code 66)] ,[SAAM Weights From Mix Committee (Portfolio Free Code 6)] ,[Bank Name #1 (Portfolio free code 30)] ,[Bank Info #1 (Portfolio free code 31)] ,[Bank Name #2 (Portfolio free code 32)] ,[Bank Info #2 (Portfolio free code 33)] ,[Bank Name #3 (Portfolio free code 34)] ,[Bank Info #3 (Portfolio free code 34)] ,[Bank Name #4 (Portfolio free code 35)] ,[Bank Info #4 (Portfolio free code 36)] ,[IFDS Account ID (Portfolio free code 44)] ,[Equity Upper Compliance Range (Portfolio group free code 15)] ,[Equity Lower Compliance Range (Portfolio group free code 16)] ,[From Portfolio (Portfolio free code 41)] ,[To Portfolio (Portfolio free code 42)] ,[Reason for Closure (Portfolio free code 43)] ,[IPO Eligible (Portfolio free code 27)] ,[Private Placement Eligible (Portfolio free code 28)] ,[Country of Residency (Portfolio free code 72)] ,[Country of Business (Portfolio free code 73)] ,[Portfolio Sub-Type (Portfolio free code 45)] ,[Qualified Instutional Buyer] ,[Withholding Tax] ,[Merge and Composite ID] ,[Reporting Currency (3 ISO code)] ,[Custodian currencies] FROM [scd].[vPortfolioPortfolioGroupChange] WHERE 1=1 AND [UserId] in (',pipeline().parameters.ObjectId,')')", "type": "Expression"}, "partitionOption": "None"}, "sink": {"type": "DelimitedTextSink", "storeSettings": {"type": "AzureBlobFSWriteSettings"}, "formatSettings": {"type": "DelimitedTextWriteSettings", "quoteAllText": true, "fileExtension": ".csv"}}, "enableStaging": false}, "inputs": [{"referenceName": "SalesforceViews", "type": "DatasetReference"}], "outputs": [{"referenceName": "AzureDataLakeStorage_SalesforceToScd_PortfolioFile", "type": "DatasetReference", "parameters": {"FileName": {"value": "@variables('FileName')", "type": "Expression"}, "FolderName": {"value": "@concat('Salesforce To SCD/Daily Loads/',formatDateTime(convertFromUtc(utcnow(),'Mountain Standard Time'),'yyyyMMdd'))", "type": "Expression"}}}]}]}, {"value": "<PERSON><PERSON>", "activities": [{"name": "Copy Merge Data", "type": "Copy", "dependsOn": [], "policy": {"timeout": "7.00:00:00", "retry": 0, "retryIntervalInSeconds": 30, "secureOutput": false, "secureInput": false}, "userProperties": [], "typeProperties": {"source": {"type": "SqlMISource", "sqlReaderQuery": {"value": "@concat(\n'SELECT [Portfolio Group] ,[Portfolio Group Name] ,[Portfolio Group Manager] ,[Portfolio Group Currency] ,[Portfolio ID] ,[Portfolio name] ,[Long reporting portfolio name] ,[Portfolio Manager] ,[Portfolio Currency] ,[Taxable Status (Portfolio free code 77)] ,[Mawer Portfolio Type (Portfolio free code 78)] ,[Management type (Portfolio free code 79)] ,[Ownership type (Portfolio free code 80)] ,[Leveraged account (Portfolio free code 82)] ,[Investment Objective (Portfolio free code 90)] ,[Anti-money Laundering Risk (Portfolio free code 85)] ,[Cross Trade Approval (Portfolio free code 84)] ,[Holdings type (Portfolio free code 81)] ,[Collateral account (Portfolio free code 83)] ,[Business Segment (Portfolio free code 91)] ,[Permitted Client (Portfolio free code 86)] ,[Proxy Voting (Portfolio free code 62)] ,[Risk tolerance (Portfolio free code 88)] ,[Time Horizon (Portfolio free code 89)] ,[Portfolio Active Status (Portfolio free code 24)] ,[Insider Status (Portfolio free code 60)] ,[Politically Exposed Person (Portfolio free code 61)] ,[Portfolio Client Type (Portfolio Free Code 25)] ,[Portfolio Country] ,[Tax Jurisdiction (Portfolio free code 63)] ,[Accredited Investor (Portfolio free code 87)] ,[Opening date] ,[Closing date] ,[Inception date] ,[Primary Owner Contact Code (Portfolio Free code 1)] ,[Portfolio manager 1] ,[Portfolio manager 2] ,[Portfolio free code 71 (Marketing Segment)] ,[CTM Account ID] ,[Investment Discretion 13F US (Portfolio Free Code 66)] ,[SAAM Weights From Mix Committee (Portfolio Free Code 6)] ,[Bank Name #1 (Portfolio free code 30)] ,[Bank Info #1 (Portfolio free code 31)] ,[Bank Name #2 (Portfolio free code 32)] ,[Bank Info #2 (Portfolio free code 33)] ,[Bank Name #3 (Portfolio free code 34)] ,[Bank Info #3 (Portfolio free code 34)] ,[Bank Name #4 (Portfolio free code 35)] ,[Bank Info #4 (Portfolio free code 36)] ,[IFDS Account ID (Portfolio free code 44)] ,[Equity Upper Compliance Range (Portfolio group free code 15)] ,[Equity Lower Compliance Range (Portfolio group free code 16)] ,[From Portfolio (Portfolio free code 41)] ,[To Portfolio (Portfolio free code 42)] ,[Reason for Closure (Portfolio free code 43)] ,[IPO Eligible (Portfolio free code 27)] ,[Private Placement Eligible (Portfolio free code 28)] ,[Country of Residency (Portfolio free code 72)] ,[Country of Business (Portfolio free code 73)] ,[Portfolio Sub-Type (Portfolio free code 45)] ,[Qualified Instutional Buyer] ,[Withholding Tax] ,[Merge and Composite ID] ,[Reporting Currency (3 ISO code)] ,[Custodian currencies] FROM [scd].[vPortfolioPortfolioGroupChange] WHERE 1=1 AND [MergeId] in (',pipeline().parameters.ObjectId,')')", "type": "Expression"}, "partitionOption": "None"}, "sink": {"type": "DelimitedTextSink", "storeSettings": {"type": "AzureBlobFSWriteSettings"}, "formatSettings": {"type": "DelimitedTextWriteSettings", "quoteAllText": true, "fileExtension": ".csv"}}, "enableStaging": false}, "inputs": [{"referenceName": "SalesforceViews", "type": "DatasetReference"}], "outputs": [{"referenceName": "AzureDataLakeStorage_SalesforceToScd_PortfolioFile", "type": "DatasetReference", "parameters": {"FileName": {"value": "@variables('FileName')", "type": "Expression"}, "FolderName": {"value": "@concat('Salesforce To SCD/Daily Loads/',formatDateTime(convertFromUtc(utcnow(),'Mountain Standard Time'),'yyyyMMdd'))", "type": "Expression"}}}]}]}]}}, {"name": "Set File Name", "type": "SetVariable", "dependsOn": [], "policy": {"secureOutput": false, "secureInput": false}, "userProperties": [], "typeProperties": {"variableName": "FileName", "value": {"value": "@concat('Portfolios_',formatDateTime(convertFromUtc(utcnow(),'Mountain Standard Time'),'MMddyyyy'),'_',formatDateTime(convertFromUtc(utcnow(),'Mountain Standard Time'),'hhmmss'),'.csv')", "type": "Expression"}}}, {"name": "Copy File to SCD SFTP", "type": "Copy", "dependsOn": [{"activity": "Portfolios", "dependencyConditions": ["Succeeded"]}], "policy": {"timeout": "7.00:00:00", "retry": 0, "retryIntervalInSeconds": 30, "secureOutput": false, "secureInput": false}, "userProperties": [], "typeProperties": {"source": {"type": "BinarySource", "storeSettings": {"type": "AzureBlobFSReadSettings", "recursive": true}, "formatSettings": {"type": "BinaryReadSettings"}}, "sink": {"type": "BinarySink", "storeSettings": {"type": "SftpWriteSettings", "operationTimeout": "01:00:00", "useTempFileRename": true}}, "enableStaging": false}, "inputs": [{"referenceName": "AzureDataLakeStorage_Portfolio_Binary", "type": "DatasetReference", "parameters": {"SourceDirectory": {"value": "@concat('Salesforce To SCD/Daily Loads/',formatDateTime(convertFromUtc(utcnow(),'Mountain Standard Time'),'yyyyMMdd'))", "type": "Expression"}, "FileName": {"value": "@variables('FileName')", "type": "Expression"}}}], "outputs": [{"referenceName": "SFTP_SCD_DIR_BINARY", "type": "DatasetReference", "parameters": {"Path": {"value": "@concat(pipeline().globalParameters.SCD_ENV,pipeline().globalParameters.SCD_SFTP_Portfolio)", "type": "Expression"}}}]}], "parameters": {"ObjectId": {"type": "string", "defaultValue": "'0015G00001dLuPHQA0'"}, "ObjectName": {"type": "string", "defaultValue": "Account"}}, "variables": {"FileName": {"type": "String"}}, "folder": {"name": "Internal/SFtoSCD/SF-SCD Real Time"}, "annotations": [], "lastPublishTime": "2023-12-01T04:12:26Z"}, "type": "Microsoft.DataFactory/factories/pipelines"}