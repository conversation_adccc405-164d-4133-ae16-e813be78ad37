{"name": "IN-104 Pipeline_TBAVFTS0", "properties": {"activities": [{"name": "TrialBalanceByFundTypeSummary", "type": "GetMetadata", "dependsOn": [], "policy": {"timeout": "7.00:00:00", "retry": 0, "retryIntervalInSeconds": 30, "secureOutput": false, "secureInput": false}, "userProperties": [], "typeProperties": {"dataset": {"referenceName": "AzureDataLakeStorage_IFDS_RawFiles_IN_TBAVFTS0", "type": "DatasetReference", "parameters": {"ADLRawFileDirectory": {"value": "@pipeline().parameters.ADLRawFileDirectory", "type": "Expression"}}}, "fieldList": ["exists"], "storeSettings": {"type": "AzureBlobFSReadSettings", "recursive": true, "enablePartitionDiscovery": false}, "formatSettings": {"type": "DelimitedTextReadSettings"}}}, {"name": "Check file Exists", "type": "IfCondition", "dependsOn": [{"activity": "TrialBalanceByFundTypeSummary", "dependencyConditions": ["Succeeded"]}], "userProperties": [], "typeProperties": {"expression": {"value": "@activity('TrialBalanceByFundTypeSummary').output.exists", "type": "Expression"}, "ifTrueActivities": [{"name": "Execute Pipeline TBAVFTS0", "type": "ExecutePipeline", "dependsOn": [], "policy": {"secureInput": false}, "userProperties": [], "typeProperties": {"waitOnCompletion": true, "parameters": {"FunctionBody": {"value": "{\"FileName\":\"TBAVFTS0\"}", "type": "Expression"}}}}, {"name": "Copy data from External Files", "type": "Copy", "dependsOn": [{"activity": "Set Date", "dependencyConditions": ["Succeeded"]}], "policy": {"timeout": "7.00:00:00", "retry": 0, "retryIntervalInSeconds": 30, "secureOutput": false, "secureInput": false}, "userProperties": [], "typeProperties": {"source": {"type": "DelimitedTextSource", "storeSettings": {"type": "AzureBlobFSReadSettings", "recursive": false, "enablePartitionDiscovery": false}, "formatSettings": {"type": "DelimitedTextReadSettings"}}, "sink": {"type": "AzureDataExplorerSink"}, "enableStaging": false, "translator": {"type": "TabularTranslator", "mappings": [{"source": {"name": "tax_type", "type": "String"}, "sink": {"name": "tax_type", "type": "String"}}, {"source": {"name": "settled_units", "type": "String"}, "sink": {"name": "settled_units", "type": "String"}}, {"source": {"name": "unsettled_units", "type": "String"}, "sink": {"name": "unsettled_units", "type": "String"}}, {"source": {"name": "total_units", "type": "String"}, "sink": {"name": "total_units", "type": "String"}}, {"source": {"name": "total_value", "type": "String"}, "sink": {"name": "total_value", "type": "String"}}, {"source": {"name": "fund", "type": "String"}, "sink": {"name": "fund", "type": "String"}}, {"source": {"name": "Report_date", "type": "String"}, "sink": {"name": "Report_date", "type": "String"}}, {"source": {"name": "Loaded", "type": "String"}, "sink": {"name": "Loaded", "type": "DateTime"}}, {"source": {"name": "RowId", "type": "String"}, "sink": {"name": "RowId", "type": "String"}}]}}, "inputs": [{"referenceName": "AzureDataLakeStorage_IFDS_ExternalFiles_TBAVFTS0", "type": "DatasetReference", "parameters": {"fileName": {"value": "@concat('TBAVFTS0-',variables('vDate'),'.csv')", "type": "Expression"}}}], "outputs": [{"referenceName": "ADX_ExternalIFDS", "type": "DatasetReference", "parameters": {"Table": "TBAVFTS0"}}]}, {"name": "Set Date", "type": "SetVariable", "dependsOn": [{"activity": "Execute Pipeline TBAVFTS0", "dependencyConditions": ["Succeeded"]}], "policy": {"secureOutput": false, "secureInput": false}, "userProperties": [], "typeProperties": {"variableName": "vDate", "value": {"value": "@concat(substring(pipeline().parameters.Date,6,2),'-',\nsubstring(pipeline().parameters.Date,4,2),'-',\nsubstring(pipeline().parameters.Date,0,4) )\n", "type": "Expression"}}}]}}], "parameters": {"ADLRawFileDirectory": {"type": "string"}, "Date": {"type": "string"}}, "variables": {"vDate": {"type": "String"}}, "folder": {"name": "IFDS/IFDS Integration/IN-101-ExternalFIles"}, "annotations": [], "lastPublishTime": "2023-12-01T04:10:23Z"}, "type": "Microsoft.DataFactory/factories/pipelines"}