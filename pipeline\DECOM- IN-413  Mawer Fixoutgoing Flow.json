{"name": "DECOM- IN-413  Mawer Fixoutgoing Flow", "properties": {"activities": [{"name": "Generate Mawer Fixoutgoing source file", "type": "Copy", "dependsOn": [{"activity": "Set variable1", "dependencyConditions": ["Succeeded"]}], "policy": {"timeout": "7.00:00:00", "retry": 0, "retryIntervalInSeconds": 30, "secureOutput": false, "secureInput": false}, "userProperties": [], "typeProperties": {"source": {"type": "SqlServerSource", "sqlReaderQuery": "Declare @startdate Datetime;\nset @Startdate=CAST(CURRENT_TIMESTAMP -1 AS DATE);\n\nSELECT        o.ORDER_ID AS orderid, foh.FIX_OUTGOING_ID AS fixoutgoingid, foh.QUEUE_TIME AS fixqueuetime, foh.LAST_UPD_DATE AS fixlastupdatedate, foh.FIX_MSG_TYPE AS fixmessagetype,\n                         foh.STATUS AS fixmessagestatus, foh.SEND_TIME AS fixsendtime, foh.FIX_ORIG_ID AS fixorigid, foh.FIX_ORIG_BROKER_ID AS fixorigbrokerid, foh.FIX_CHAR_ID AS fixcharid, foh.TARGET_ID AS fixtargetid,\n                         foh.TARGET_SUB_ID AS fixtargetsubid, foh.SENDER_SUB_ID AS fixsendersubid, foh.ORDERQTY AS fixorderqty, foh.PRICE AS fixlimitprice, foh.STOPPX AS fixstopprice, foh.ORDTYPE AS fixordertype,\n                         foh.EXECINST AS fixexecutioninstruction, foh.HANDINST AS fixhandlinginstruction, foh.TIMEINFORCE AS fixtimeinforce, foh.EXPIRETIME AS fixexpiretime, foh.MINQTY AS fixminquantity,\n                         foh.MAXFLOOR AS fixmaxfloor, foh.SIDE AS fixorderside, foh.FIX_ORD_STATUS AS fixorderstatus, foh.SYMBOL AS fixsymbol, foh.FIX_SETTLMNTTYP AS fixsettlementtype,\n                         foh.FIX_EXDESTINATION AS fixexchangedestination, foh.SECURITYID AS fixsecurityid, foh.IDSOURCE AS fixsecurityidtype, foh.CURRENCY AS fixcurrency, foh.PLACE_ID AS placementid,\n                         foh.USER_DEFINED AS fixuserdefinedtags, foh.LAST_CUM_QTY AS fixlastcumulativeqty, o.TRADE_DATE AS tradedate\nFROM            [MWR-SRV-crSQL1A].[CRDMAINMAWIM].[dbo].[TS_ORDER] AS o INNER JOIN\n                         [MWR-SRV-crSQL1A].[CRDMAINMAWIM].[dbo].[FIX_OUTGOING_HIST] AS foh ON o.ORDER_ID = foh.ORDER_ID\nWHERE        (o.TRADE_DATE >=@startdate) AND (o.TRADE_DATE <=@startdate)\n", "queryTimeout": "02:00:00", "partitionOption": "None"}, "sink": {"type": "DelimitedTextSink", "storeSettings": {"type": "AzureBlobFSWriteSettings"}, "formatSettings": {"type": "DelimitedTextWriteSettings", "quoteAllText": true, "fileExtension": ".txt"}}, "enableStaging": false, "translator": {"type": "TabularTranslator", "typeConversion": true, "typeConversionSettings": {"allowDataTruncation": true, "treatBooleanAsNumber": false}}}, "inputs": [{"referenceName": "ODSExternalData_MawerIHSMarkit_sql", "type": "DatasetReference"}], "outputs": [{"referenceName": "AzureDataLakeStorage_MawerIHSmarkitsink_csv", "type": "DatasetReference", "parameters": {"FileName": {"value": "@variables('FileName')", "type": "Expression"}}}]}, {"name": "Set variable1", "type": "SetVariable", "dependsOn": [], "policy": {"secureOutput": false, "secureInput": false}, "userProperties": [], "typeProperties": {"variableName": "FileName", "value": {"value": "@concat('Mawer Fixoutgoing_',formatDateTime(convertFromUtc(adddays(utcnow(),-1),'Mountain Standard Time'),'MMddyy'),'.txt')", "type": "Expression"}}}, {"name": "Archive", "type": "Copy", "dependsOn": [{"activity": "Generate Mawer Fixoutgoing source file", "dependencyConditions": ["Succeeded"]}], "policy": {"timeout": "7.00:00:00", "retry": 0, "retryIntervalInSeconds": 30, "secureOutput": false, "secureInput": false}, "userProperties": [], "typeProperties": {"source": {"type": "BinarySource", "storeSettings": {"type": "AzureBlobFSReadSettings", "recursive": false, "wildcardFolderPath": "DDI Migration/Mawer IHSMarkit - workflow/Outputfiles", "wildcardFileName": {"value": "@variables('FileName')", "type": "Expression"}, "deleteFilesAfterCompletion": true}, "formatSettings": {"type": "BinaryReadSettings"}}, "sink": {"type": "BinarySink", "storeSettings": {"type": "AzureBlobFSWriteSettings"}}, "enableStaging": false}, "inputs": [{"referenceName": "AzureDataLakeStorageMawerIHSMarkit_Binary_Source", "type": "DatasetReference"}], "outputs": [{"referenceName": "AzureDataLakeStorage_MawerIHSMarkit_Binary_Sink", "type": "DatasetReference"}]}], "variables": {"FileName": {"type": "String"}}, "folder": {"name": "Internal/DDI/IN-329 DDI Migration/IN-384 Mawer IHSMarkit - TransactionCostAnalysis - Workflow"}, "annotations": [], "lastPublishTime": "2023-12-01T04:11:14Z"}, "type": "Microsoft.DataFactory/factories/pipelines"}