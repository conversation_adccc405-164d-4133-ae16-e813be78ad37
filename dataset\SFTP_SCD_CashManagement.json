{"name": "SFTP_SCD_CashManagement", "properties": {"linkedServiceName": {"referenceName": "SCD_SFTP_Prod", "type": "LinkedServiceReference"}, "parameters": {"FilePath": {"type": "string"}}, "folder": {"name": "Outbound/Cash Management Report Workflow"}, "annotations": [], "type": "DelimitedText", "typeProperties": {"location": {"type": "SftpLocation", "folderPath": {"value": "@dataset().FilePath", "type": "Expression"}}, "columnDelimiter": ",", "escapeChar": "\\", "firstRowAsHeader": true, "quoteChar": "\""}, "schema": [{"name": "Cash management setup\tReport date\tPortfolio\tPortfolio name\tPortfolio currency\tBank account ID\tBank currency\tMoney Market Positions QC\tTD\tTD+1BD\tTD+2BD\tTD+3BD\tPending money market orders\tPortfolio manager\tPortfolio manager associate", "type": "String"}]}, "type": "Microsoft.DataFactory/factories/datasets"}