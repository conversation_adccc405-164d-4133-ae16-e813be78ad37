{"name": "DEPRECATED_Posted Transactions Mapping", "properties": {"folder": {"name": "Inbound"}, "type": "MappingDataFlow", "typeProperties": {"sources": [{"dataset": {"referenceName": "ADL_ScotiaTrust_IN", "type": "DatasetReference"}, "name": "RawCSV"}, {"dataset": {"referenceName": "ADL_ScotiaTrust_EntryCodeMapping", "type": "DatasetReference"}, "name": "EntryCodeData"}, {"dataset": {"referenceName": "ADL_ScotiaTrust_SFPortfolioCustodian", "type": "DatasetReference"}, "name": "PortfolioCodeCustodianData"}], "sinks": [{"dataset": {"referenceName": "ADL_ScotiaTrust_TEMP", "type": "DatasetReference"}, "name": "ParsedCSV"}], "transformations": [{"name": "SplitColumnByValue"}, {"name": "MapFields"}, {"name": "SelectColumns"}, {"name": "JoinWithEntryCodeMappingData"}, {"name": "EntryCodeMapping", "description": "Creates an explicit mapping for each drifted column"}, {"name": "AddColumns"}, {"name": "JoinWithCustodianMappingData"}, {"name": "PortfolioCodeCustodianMapping", "description": "Creates an explicit mapping for each drifted column"}, {"name": "FilterZerosInEntryCodeAndTransactionCashAmount"}], "script": "parameters{\n\tfileName as string\n}\nsource(output(\n\t\tvalue as string\n\t),\n\tallowSchemaDrift: true,\n\tvalidateSchema: false,\n\tignoreNoFilesFound: false,\n\tskipLines: 1,\n\twildcardPaths:[(concat('Custodian Data/ScotiaTrust/IN/',$fileName))]) ~> RawCSV\nsource(allowSchemaDrift: true,\n\tvalidateSchema: false,\n\tignoreNoFilesFound: false) ~> EntryCodeData\nsource(allowSchemaDrift: true,\n\tvalidateSchema: false,\n\tignoreNoFilesFound: false,\n\twildcardPaths:[('Custodian Data/Static/PortfolioCodeCustodianMapping.csv')]) ~> PortfolioCodeCustodianData\nRawCSV derive(splitCol = split(value,',')) ~> SplitColumnByValue\nSplitColumnByValue derive(AccountID = replace(splitCol[1],'\"'),\n\t\tAccountShortName = replace(splitCol[2],'\"'),\n\t\tAccountCurrency = replace(splitCol[3],'\"'),\n\t\tPostingDate = concat(substring(replace(splitCol[4],'\"'),1, 4), substring(replace(splitCol[4],'\"'),5, 2),substring(replace(splitCol[4],'\"'),7, 2)),\n\t\tSecurityNo = replace(splitCol[5],'\"'),\n\t\tCUSIP = replace(splitCol[6],'\"'),\n\t\tISIN = replace(splitCol[7],'\"'),\n\t\t{Security Identifiers} = case(length(trim(replace(splitCol[7],'\"'))) != 0, replace(splitCol[7],'\"'), \nlength(trim(replace(splitCol[6],'\"'))) != 0, replace(splitCol[6],'\"'),\n length(trim(replace(splitCol[8],'\"'))) != 0, replace(splitCol[8],'\"')),\n\t\tTickerSymbol = replace(splitCol[8],'\"'),\n\t\tSecurityDescription = replace(splitCol[9],'\"'),\n\t\tSecurityType = replace(splitCol[10],'\"'),\n\t\tSecurityClassCode = replace(splitCol[11],'\"'),\n\t\tSecurityMaturityDate = replace(splitCol[12],'\"'),\n\t\tInterestRate = replace(splitCol[13],'\"'),\n\t\tTransactionBranchNumber = replace(splitCol[14],'\"'),\n\t\tTransactionNumber = replace(splitCol[15],'\"'),\n\t\tEntryCode = replace(splitCol[16],'\"'),\n\t\tEntryCodeClean = right(replace(splitCol[16],'\"'), 3),\n\t\tEntryFam = replace(splitCol[17],'\"'),\n\t\tSign = case(\r\n    and(replace(splitCol[17],'\"')== 'G', toInteger(replace(splitCol[20],'\"')) >= 0), 'D', \r\n    and(replace(splitCol[17],'\"')== 'G', toInteger(replace(splitCol[20],'\"')) < 0), 'C', \r\n    and(replace(splitCol[17],'\"')== 'B', toInteger(replace(splitCol[22],'\"')) >= 0), 'D', \r\n    and(replace(splitCol[17],'\"')== 'B', toInteger(replace(splitCol[22],'\"')) < 0), 'C', \r\n    replace(splitCol[17],'\"')== 'C', 'C', \r\n    replace(splitCol[17],'\"')== 'D', 'C', \r\n    replace(splitCol[17],'\"')== 'E', 'D'),\n\t\t{Transaction Type} = case(\n    and(replace(splitCol[17],'\"')== 'G', toInteger(replace(splitCol[20],'\"')) > 0), 'Buy', \n    and(trim(replace(splitCol[17],'\"'))== 'G', toInteger(replace(splitCol[20],'\"')) < 0), 'Sell', \n    trim(replace(splitCol[17],'\"')) == 'C', 'Dividend', \n    trim(replace(splitCol[17],'\"')) == 'D', 'Interest', \n    trim(replace(splitCol[17],'\"')) == 'E', 'Overdraft Interest', \n    and(trim(replace(splitCol[17],'\"'))== 'B', trim(replace(splitCol[16],'\"')) == ''), 'Other'),\n\t\tTRSCurrency = replace(splitCol[18],'\"'),\n\t\tAccruedDescription = replace(splitCol[19],'\"'),\n\t\tNewQTY = replace(splitCol[20],'\"'),\n\t\tTransactionUnitPrice = replace(splitCol[21],'\"'),\n\t\tTransactionCashAmt = replace(splitCol[22],'\"'),\n\t\tCDNBookValue = replace(splitCol[23],'\"'),\n\t\tUSBookValue = replace(splitCol[24],'\"'),\n\t\tFillerSpace = replace(splitCol[26],'\"'),\n\t\tFINSCUID = replace(splitCol[27],'\"'),\n\t\tFINSCUIDDescription = replace(splitCol[28],'\"'),\n\t\tTransDesc1 = replace(splitCol[29],'\"'),\n\t\tTransDesc2 = replace(splitCol[30],'\"'),\n\t\tTransDesc3 = replace(splitCol[31],'\"'),\n\t\tTransDesc4 = replace(splitCol[32],'\"'),\n\t\tBrokerCommission = replace(splitCol[33],'\"'),\n\t\tReversalFlag = replace(splitCol[34],'\"'),\n\t\tTradeDate = concat(substring(replace(splitCol[35],'\"'),1, 4),substring(replace(splitCol[35],'\"'),5, 2),substring(replace(splitCol[35],'\"'),7, 2)),\n\t\tAccountType = replace(splitCol[36],'\"'),\n\t\tPortfolioManagerCode = replace(splitCol[37],'\"'),\n\t\tAssetLocation = replace(splitCol[38],'\"'),\n\t\tSecuritySpecialCharacterstics = replace(splitCol[39],'\"'),\n\t\tFiller = replace(splitCol[40],'\"'),\n\t\tEntityCode = replace(splitCol[41],'\"'),\n\t\tfileName = $fileName) ~> MapFields\nFilterZerosInEntryCodeAndTransactionCashAmount select(mapColumn(\n\t\tAccountID,\n\t\tAccountShortName,\n\t\tAccountCurrency,\n\t\tPostingDate,\n\t\tSecurityNo,\n\t\tCUSIP,\n\t\tISIN,\n\t\t{Security Identifiers},\n\t\tTickerSymbol,\n\t\tSecurityDescription,\n\t\tSecurityType,\n\t\tSecurityClassCode,\n\t\tSecurityMaturityDate,\n\t\tInterestRate,\n\t\tTransactionBranchNumber,\n\t\tTransactionNumber,\n\t\tEntryCode,\n\t\tEntryFam,\n\t\tSign,\n\t\t{Transaction Type},\n\t\tTRSCurrency,\n\t\tAccruedDescription,\n\t\tNewQTY,\n\t\tTransactionUnitPrice,\n\t\tTransactionCashAmt,\n\t\tCDNBookValue,\n\t\tUSBookValue,\n\t\tFillerSpace,\n\t\tFINSCUID,\n\t\tFINSCUIDDescription,\n\t\tTransDesc1,\n\t\tTransDesc2,\n\t\tTransDesc3,\n\t\tTransDesc4,\n\t\tBrokerCommission,\n\t\tReversalFlag,\n\t\tTradeDate,\n\t\tAccountType,\n\t\tPortfolioManagerCode,\n\t\tAssetLocation,\n\t\tSecuritySpecialCharacterstics,\n\t\tFiller,\n\t\tEntityCode,\n\t\tfileName,\n\t\tSSID,\n\t\t{Payment amount}\n\t),\n\tskipDuplicateMapInputs: true,\n\tskipDuplicateMapOutputs: true) ~> SelectColumns\nMapFields, EntryCodeMapping join(EntryCodeClean == {Entry Code},\n\tjoinType:'left',\n\tmatchType:'exact',\n\tignoreSpaces: false,\n\tbroadcast: 'auto')~> JoinWithEntryCodeMappingData\nEntryCodeData derive(Description = toString(byName('Description')),\n\t\t{Entry Code} = toString(byName('Entry Code')),\n\t\tLanguage = toString(byName('Language'))) ~> EntryCodeMapping\nJoinWithCustodianMappingData derive({Transaction Type} = case(and(replace(splitCol[17],'\"')== 'G', toInteger(replace(splitCol[20],'\"')) > 0), 'Buy', \nand(trim(replace(splitCol[17],'\"'))== 'G', toInteger(replace(splitCol[20],'\"')) < 0), 'Sell', \ntrim(replace(splitCol[17],'\"')) == 'C', 'Dividend',\n trim(replace(splitCol[17],'\"')) == 'D', 'Interest',\n  trim(replace(splitCol[17],'\"')) == 'E', 'Overdraft Interest', \n  and(trim(replace(splitCol[17],'\"'))== 'B', trim(replace(splitCol[16],'\"')) == ''), 'Other',\n    and(trim(replace(splitCol[17],'\"'))== 'B', trim(replace(splitCol[16],'\"')) != ''), Description\n  ),\n\t\t{Payment amount} = case(\r\n    trim(Sign) == \"C\",  toString(abs(toDecimal(TransactionCashAmt,20,2))), \r\n    trim(Sign) == \"D\",  toString(-1*abs(toDecimal(TransactionCashAmt,20,2))), \r\n    or(isNull(trim(Sign)), trim(Sign) == \"\"), toString(-1*toDecimal(TransactionCashAmt,20,2)), \r\n    TransactionCashAmt)) ~> AddColumns\nJoinWithEntryCodeMappingData, PortfolioCodeCustodianMapping join(AccountID == CustodianAccount,\n\tjoinType:'left',\n\tmatchType:'exact',\n\tignoreSpaces: false,\n\tbroadcast: 'auto')~> JoinWithCustodianMappingData\nPortfolioCodeCustodianData derive(SSID = toString(byName('SSID')),\n\t\tCustodianAccount = toString(byName('CustodianAccount'))) ~> PortfolioCodeCustodianMapping\nAddColumns filter(and(toDecimal(TransactionCashAmt,20,2) != 0, trim(EntryCode) != '0INT')) ~> FilterZerosInEntryCodeAndTransactionCashAmount\nSelectColumns sink(allowSchemaDrift: true,\n\tvalidateSchema: false,\n\tinput(\n\t\tColumn_1 as string,\n\t\tColumn_2 as string,\n\t\tColumn_3 as string\n\t),\n\tpartitionFileNames:['PostedTransactions.csv'],\n\tumask: 0022,\n\tpreCommands: [],\n\tpostCommands: [],\n\tskipDuplicateMapInputs: true,\n\tskipDuplicateMapOutputs: true,\n\tpartitionBy('hash', 1)) ~> ParsedCSV"}}}