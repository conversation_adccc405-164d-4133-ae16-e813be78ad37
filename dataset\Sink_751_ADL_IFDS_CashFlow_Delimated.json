{"name": "Sink_751_ADL_IFDS_CashFlow_Delimated", "properties": {"linkedServiceName": {"referenceName": "AzureDataLakeStorage", "type": "LinkedServiceReference"}, "folder": {"name": "Inbound"}, "annotations": [], "type": "DelimitedText", "typeProperties": {"location": {"type": "AzureBlobFSLocation", "folderPath": "IFDS_Outbond_CashFlow/IFDS_CashFlow/Out/", "fileSystem": "scdintegration"}, "columnDelimiter": ",", "escapeChar": "\\", "firstRowAsHeader": true, "quoteChar": "\""}, "schema": []}, "type": "Microsoft.DataFactory/factories/datasets"}