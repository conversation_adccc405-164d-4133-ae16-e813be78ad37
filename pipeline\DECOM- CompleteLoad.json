{"name": "DECOM- CompleteLoad", "properties": {"activities": [{"name": "1 DM_FUND to MI", "type": "IfCondition", "dependsOn": [], "userProperties": [], "typeProperties": {"expression": {"value": "@equals(pipeline().parameters.Load_DM_FUND,'1')", "type": "Expression"}, "ifTrueActivities": [{"name": "DM_FUND to MI", "type": "Copy", "dependsOn": [], "policy": {"timeout": "7.00:00:00", "retry": 0, "retryIntervalInSeconds": 30, "secureOutput": false, "secureInput": false}, "userProperties": [], "typeProperties": {"source": {"type": "OracleSource", "partitionOption": "None", "queryTimeout": "02:00:00"}, "sink": {"type": "SqlMISink", "preCopyScript": "delete from dbo.DM_FUND"}, "enableStaging": false, "translator": {"type": "TabularTranslator", "mappings": [{"source": {"name": "DWH_EXTRACT_PROCESS_IK", "type": "Decimal"}, "sink": {"name": "DWH_EXTRACT_PROCESS_IK", "type": "Decimal", "physicalType": "decimal", "scale": 0, "precision": 10}}, {"source": {"name": "DWH_LOAD_TS", "type": "DateTime"}, "sink": {"name": "DWH_LOAD_TS", "type": "DateTime", "physicalType": "datetime"}}, {"source": {"name": "LAST_CHANGE_TS", "type": "DateTime"}, "sink": {"name": "LAST_CHANGE_TS", "type": "DateTime", "physicalType": "datetime"}}, {"source": {"name": "FUND_KEY", "type": "Decimal"}, "sink": {"name": "FUND_KEY", "type": "Decimal", "physicalType": "decimal", "scale": 0, "precision": 14}}, {"source": {"name": "FUND_IK", "type": "Decimal"}, "sink": {"name": "FUND_IK", "type": "Decimal", "physicalType": "decimal", "scale": 0, "precision": 10}}, {"source": {"name": "FUND", "type": "String"}, "sink": {"name": "FUND", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}, {"source": {"name": "FUND_NAME", "type": "String"}, "sink": {"name": "FUND_NAME", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}, {"source": {"name": "FUND_TYPE", "type": "String"}, "sink": {"name": "FUND_TYPE", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}, {"source": {"name": "FUND_TYPE_NAME", "type": "String"}, "sink": {"name": "FUND_TYPE_NAME", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}, {"source": {"name": "FUND_INVESTMENT_TYPE", "type": "String"}, "sink": {"name": "FUND_INVESTMENT_TYPE", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}, {"source": {"name": "FUND_PORTFOLIO", "type": "String"}, "sink": {"name": "FUND_PORTFOLIO", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}, {"source": {"name": "FUND_ISSUE_DATE", "type": "DateTime"}, "sink": {"name": "FUND_ISSUE_DATE", "type": "DateTime", "physicalType": "date"}}, {"source": {"name": "FUND_CODE", "type": "String"}, "sink": {"name": "FUND_CODE", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}, {"source": {"name": "FUND_NUMERIC_CODE", "type": "String"}, "sink": {"name": "FUND_NUMERIC_CODE", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}, {"source": {"name": "MUTUAL_FUND_TYPE_CODE", "type": "String"}, "sink": {"name": "MUTUAL_FUND_TYPE_CODE", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}, {"source": {"name": "MUTUAL_FUND_TYPE_NAME", "type": "String"}, "sink": {"name": "MUTUAL_FUND_TYPE_NAME", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}, {"source": {"name": "MUTUAL_FUND_VEHICLE", "type": "String"}, "sink": {"name": "MUTUAL_FUND_VEHICLE", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}, {"source": {"name": "MUTUAL_FUND_VEHICLE_NAME", "type": "String"}, "sink": {"name": "MUTUAL_FUND_VEHICLE_NAME", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}, {"source": {"name": "SECURITY_ID", "type": "String"}, "sink": {"name": "SECURITY_ID", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}, {"source": {"name": "SECURITY_TYPE", "type": "String"}, "sink": {"name": "SECURITY_TYPE", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}, {"source": {"name": "SECURITY_GROUP", "type": "String"}, "sink": {"name": "SECURITY_GROUP", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}, {"source": {"name": "INSTRUMENT_TYPE", "type": "String"}, "sink": {"name": "INSTRUMENT_TYPE", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}, {"source": {"name": "ORIGINATING_SYSTEM_NO", "type": "Decimal"}, "sink": {"name": "ORIGINATING_SYSTEM_NO", "type": "Decimal", "physicalType": "decimal", "scale": 0, "precision": 14}}, {"source": {"name": "CURRENT_VERSION", "type": "String"}, "sink": {"name": "CURRENT_VERSION", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}, {"source": {"name": "VALID_FROM_DATE", "type": "DateTime"}, "sink": {"name": "VALID_FROM_DATE", "type": "DateTime", "physicalType": "date"}}, {"source": {"name": "VALID_TO_DATE", "type": "DateTime"}, "sink": {"name": "VALID_TO_DATE", "type": "DateTime", "physicalType": "date"}}, {"source": {"name": "MASTER_FUND_CODE", "type": "String"}, "sink": {"name": "MASTER_FUND_CODE", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}], "typeConversion": true, "typeConversionSettings": {"allowDataTruncation": true, "treatBooleanAsNumber": false}}}, "inputs": [{"referenceName": "IMW_SCD_Oracle_UAT", "type": "DatasetReference", "parameters": {"Prefix": "SCDWH", "Table": "DM_FUND"}}], "outputs": [{"referenceName": "DM_FUND", "type": "DatasetReference"}]}, {"name": "DM_FUND Row Count", "type": "Copy", "dependsOn": [{"activity": "DM_FUND to MI", "dependencyConditions": ["Succeeded"]}], "policy": {"timeout": "7.00:00:00", "retry": 0, "retryIntervalInSeconds": 30, "secureOutput": false, "secureInput": false}, "userProperties": [], "typeProperties": {"source": {"type": "OracleSource", "oracleReaderQuery": "select NULL AS ProcessTS,'DM_FUND' AS TableName,\nCOUNT(DWH_EXTRACT_PROCESS_IK) AS SourceCount,\nNULL AS TargetCount,\nNULL AS SourceSUM ,\nNULL as TargetSUM,\nNULL AS Status\nfrom SCDWH.DM_FUND", "partitionOption": "None", "queryTimeout": "02:00:00"}, "sink": {"type": "SqlMISink", "writeBehavior": "insert", "sqlWriterUseTableLock": false}, "enableStaging": false, "translator": {"type": "TabularTranslator", "mappings": [{"source": {"name": "PROCESSTS", "type": "String"}, "sink": {"name": "ProcessTS", "type": "DateTime", "physicalType": "datetime"}}, {"source": {"name": "TABLENAME", "type": "String"}, "sink": {"name": "TableName", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}, {"source": {"name": "SOURCECOUNT", "type": "Double"}, "sink": {"name": "SourceCount", "type": "Int32", "physicalType": "int"}}, {"source": {"name": "TARGETCOUNT", "type": "String"}, "sink": {"name": "TargetCount", "type": "Int32", "physicalType": "int"}}, {"source": {"name": "SOURCESUM", "type": "String"}, "sink": {"name": "SourceSUM", "type": "Double", "physicalType": "float"}}, {"source": {"name": "TARGETSUM", "type": "String"}, "sink": {"name": "TargetSUM", "type": "Double", "physicalType": "float"}}, {"source": {"name": "STATUS", "type": "String"}, "sink": {"name": "Status", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}], "typeConversion": true, "typeConversionSettings": {"allowDataTruncation": true, "treatBooleanAsNumber": false}}}, "inputs": [{"referenceName": "IMW_SCD_Oracle_UAT", "type": "DatasetReference", "parameters": {"Prefix": "SCDWH", "Table": "DM_FUND"}}], "outputs": [{"referenceName": "ReconLog", "type": "DatasetReference"}]}, {"name": "Log Final Status DM_FUND", "type": "Lookup", "dependsOn": [{"activity": "DM_FUND Row Count", "dependencyConditions": ["Succeeded"]}], "policy": {"timeout": "7.00:00:00", "retry": 0, "retryIntervalInSeconds": 30, "secureOutput": false, "secureInput": false}, "userProperties": [], "typeProperties": {"source": {"type": "SqlMISource", "sqlReaderQuery": "update dbo.ReconLog\nSET ProcessTS = getdate(),\nTargetCount = (SELECT COUNT(*) FROM dbo.DM_FUND ),\nTargetSUM = NULL\nwhere TableName = 'DM_FUND'\nand Status IS NULL\n\nupdate dbo.ReconLog\nset Status = (case when SourceCount = TargetCount  then 'Success' Else 'Failed' END)\nwhere Status IS NULL and TableName = 'DM_FUND'\n\nSELECT top 1 * FROM dbo.ReconLog ", "partitionOption": "None"}, "dataset": {"referenceName": "DM_FUND", "type": "DatasetReference"}}}]}}, {"name": "2 DM_FUND_CERTIFICATE to MI", "type": "IfCondition", "dependsOn": [], "userProperties": [], "typeProperties": {"expression": {"value": "@equals(pipeline().parameters.Load_DM_FUND_CERTIFICATE,'1')", "type": "Expression"}, "ifTrueActivities": [{"name": "DM_FUND_CERTIFICATE to MI", "type": "Copy", "dependsOn": [], "policy": {"timeout": "7.00:00:00", "retry": 0, "retryIntervalInSeconds": 30, "secureOutput": false, "secureInput": false}, "userProperties": [], "typeProperties": {"source": {"type": "OracleSource", "partitionOption": "None", "queryTimeout": "02:00:00"}, "sink": {"type": "SqlMISink", "preCopyScript": "delete from dbo.DM_FUND_CERTIFICATE"}, "enableStaging": false, "translator": {"type": "TabularTranslator", "mappings": [{"source": {"name": "DWH_EXTRACT_PROCESS_IK", "type": "Decimal"}, "sink": {"name": "DWH_EXTRACT_PROCESS_IK", "type": "Decimal", "physicalType": "decimal", "scale": 0, "precision": 10}}, {"source": {"name": "DWH_LOAD_TS", "type": "DateTime"}, "sink": {"name": "DWH_LOAD_TS", "type": "DateTime", "physicalType": "datetime"}}, {"source": {"name": "LAST_CHANGE_TS", "type": "DateTime"}, "sink": {"name": "LAST_CHANGE_TS", "type": "DateTime", "physicalType": "datetime"}}, {"source": {"name": "FUND_CERTIFICATE_KEY", "type": "Decimal"}, "sink": {"name": "FUND_CERTIFICATE_KEY", "type": "Decimal", "physicalType": "decimal", "scale": 0, "precision": 14}}, {"source": {"name": "FUND_CERTIFICATE_IK", "type": "Decimal"}, "sink": {"name": "FUND_CERTIFICATE_IK", "type": "Decimal", "physicalType": "decimal", "scale": 0, "precision": 10}}, {"source": {"name": "FUND_IK", "type": "Decimal"}, "sink": {"name": "FUND_IK", "type": "Decimal", "physicalType": "decimal", "scale": 0, "precision": 10}}, {"source": {"name": "FUND", "type": "String"}, "sink": {"name": "FUND", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}, {"source": {"name": "FUND_CODE", "type": "String"}, "sink": {"name": "FUND_CODE", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}, {"source": {"name": "FUND_NAME", "type": "String"}, "sink": {"name": "FUND_NAME", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}, {"source": {"name": "FUND_NAME_LONG", "type": "String"}, "sink": {"name": "FUND_NAME_LONG", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}, {"source": {"name": "FUND_CLASS", "type": "String"}, "sink": {"name": "FUND_CLASS", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}, {"source": {"name": "FUND_CLASS_NAME", "type": "String"}, "sink": {"name": "FUND_CLASS_NAME", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}, {"source": {"name": "FUND_CLASS_NAME_LONG", "type": "String"}, "sink": {"name": "FUND_CLASS_NAME_LONG", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}, {"source": {"name": "FUND_STRUCTURE", "type": "String"}, "sink": {"name": "FUND_STRUCTURE", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}, {"source": {"name": "SECURITY_ID", "type": "String"}, "sink": {"name": "SECURITY_ID", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}, {"source": {"name": "SECURITY_TYPE", "type": "String"}, "sink": {"name": "SECURITY_TYPE", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}, {"source": {"name": "SECURITY_GROUP", "type": "String"}, "sink": {"name": "SECURITY_GROUP", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}, {"source": {"name": "INSTRUMENT_TYPE", "type": "String"}, "sink": {"name": "INSTRUMENT_TYPE", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}, {"source": {"name": "ORIGINATING_SYSTEM_NO", "type": "Decimal"}, "sink": {"name": "ORIGINATING_SYSTEM_NO", "type": "Decimal", "physicalType": "decimal", "scale": 0, "precision": 14}}, {"source": {"name": "CURRENT_VERSION", "type": "String"}, "sink": {"name": "CURRENT_VERSION", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}, {"source": {"name": "VALID_FROM_DATE", "type": "DateTime"}, "sink": {"name": "VALID_FROM_DATE", "type": "DateTime", "physicalType": "date"}}, {"source": {"name": "VALID_TO_DATE", "type": "DateTime"}, "sink": {"name": "VALID_TO_DATE", "type": "DateTime", "physicalType": "date"}}, {"source": {"name": "SECURITY_NAME", "type": "String"}, "sink": {"name": "SECURITY_NAME", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}], "typeConversion": true, "typeConversionSettings": {"allowDataTruncation": true, "treatBooleanAsNumber": false}}}, "inputs": [{"referenceName": "IMW_SCD_Oracle_UAT", "type": "DatasetReference", "parameters": {"Prefix": "SCDWH", "Table": "DM_FUND_CERTIFICATE"}}], "outputs": [{"referenceName": "DM_FUND_CERTIFICATE", "type": "DatasetReference"}]}, {"name": "DM_FUND_CERTIFICATE Row Count", "type": "Copy", "dependsOn": [{"activity": "DM_FUND_CERTIFICATE to MI", "dependencyConditions": ["Succeeded"]}], "policy": {"timeout": "7.00:00:00", "retry": 0, "retryIntervalInSeconds": 30, "secureOutput": false, "secureInput": false}, "userProperties": [], "typeProperties": {"source": {"type": "OracleSource", "oracleReaderQuery": "select NULL AS ProcessTS,'DM_FUND_CERTIFICATE' AS TableName,\nCOUNT(DWH_EXTRACT_PROCESS_IK) AS SourceCount,\nNULL AS TargetCount,\nNULL AS SourceSUM ,\nNULL as TargetSUM,\nNULL AS Status\nfrom SCDWH.DM_FUND_CERTIFICATE", "partitionOption": "None", "queryTimeout": "02:00:00"}, "sink": {"type": "SqlMISink", "writeBehavior": "insert", "sqlWriterUseTableLock": false}, "enableStaging": false, "translator": {"type": "TabularTranslator", "mappings": [{"source": {"name": "PROCESSTS", "type": "String"}, "sink": {"name": "ProcessTS", "type": "DateTime", "physicalType": "datetime"}}, {"source": {"name": "TABLENAME", "type": "String"}, "sink": {"name": "TableName", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}, {"source": {"name": "SOURCECOUNT", "type": "Double"}, "sink": {"name": "SourceCount", "type": "Int32", "physicalType": "int"}}, {"source": {"name": "TARGETCOUNT", "type": "String"}, "sink": {"name": "TargetCount", "type": "Int32", "physicalType": "int"}}, {"source": {"name": "SOURCESUM", "type": "String"}, "sink": {"name": "SourceSUM", "type": "Double", "physicalType": "float"}}, {"source": {"name": "TARGETSUM", "type": "String"}, "sink": {"name": "TargetSUM", "type": "Double", "physicalType": "float"}}, {"source": {"name": "STATUS", "type": "String"}, "sink": {"name": "Status", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}], "typeConversion": true, "typeConversionSettings": {"allowDataTruncation": true, "treatBooleanAsNumber": false}}}, "inputs": [{"referenceName": "IMW_SCD_Oracle_UAT", "type": "DatasetReference", "parameters": {"Prefix": "SCDWH", "Table": "DM_FUND_CERTIFICATE"}}], "outputs": [{"referenceName": "ReconLog", "type": "DatasetReference"}]}, {"name": "Log Final Status DM_FUND_CERTIFICATE", "type": "Lookup", "dependsOn": [{"activity": "DM_FUND_CERTIFICATE Row Count", "dependencyConditions": ["Succeeded"]}], "policy": {"timeout": "7.00:00:00", "retry": 0, "retryIntervalInSeconds": 30, "secureOutput": false, "secureInput": false}, "userProperties": [], "typeProperties": {"source": {"type": "SqlMISource", "sqlReaderQuery": "update dbo.ReconLog\nSET ProcessTS = getdate(),\nTargetCount = (SELECT COUNT(*) FROM dbo.DM_FUND_CERTIFICATE ),\nTargetSUM = NULL\nwhere TableName = 'DM_FUND_CERTIFICATE'\nand Status IS NULL\n\nupdate dbo.ReconLog\nset Status = (case when SourceCount = TargetCount  then 'Success' Else 'Failed' END)\nwhere Status IS NULL and TableName = 'DM_FUND_CERTIFICATE'\n\nSELECT top 1 * FROM dbo.ReconLog ", "partitionOption": "None"}, "dataset": {"referenceName": "DM_FUND_CERTIFICATE", "type": "DatasetReference"}}}]}}, {"name": "3 DM_PORTFOLIO to MI", "type": "IfCondition", "dependsOn": [], "userProperties": [], "typeProperties": {"expression": {"value": "@equals(pipeline().parameters.Load_DM_PORTFOLIO,'1')", "type": "Expression"}, "ifTrueActivities": [{"name": "DM_PORTFOLIO to MI", "type": "Copy", "dependsOn": [], "policy": {"timeout": "7.00:00:00", "retry": 0, "retryIntervalInSeconds": 30, "secureOutput": false, "secureInput": false}, "userProperties": [], "typeProperties": {"source": {"type": "OracleSource", "partitionOption": "None", "queryTimeout": "02:00:00"}, "sink": {"type": "SqlMISink", "preCopyScript": "delete from dbo.DM_PORTFOLIO"}, "enableStaging": false, "translator": {"type": "TabularTranslator", "mappings": [{"source": {"name": "DWH_EXTRACT_PROCESS_IK", "type": "Decimal"}, "sink": {"name": "DWH_EXTRACT_PROCESS_IK", "type": "Decimal", "physicalType": "decimal", "scale": 0, "precision": 10}}, {"source": {"name": "DWH_LOAD_TS", "type": "DateTime"}, "sink": {"name": "DWH_LOAD_TS", "type": "DateTime", "physicalType": "datetime"}}, {"source": {"name": "LAST_CHANGE_TS", "type": "DateTime"}, "sink": {"name": "LAST_CHANGE_TS", "type": "DateTime", "physicalType": "datetime"}}, {"source": {"name": "PORTFOLIO_KEY", "type": "Decimal"}, "sink": {"name": "PORTFOLIO_KEY", "type": "Decimal", "physicalType": "decimal", "scale": 0, "precision": 14}}, {"source": {"name": "PORTFOLIO_IK", "type": "Decimal"}, "sink": {"name": "PORTFOLIO_IK", "type": "Decimal", "physicalType": "decimal", "scale": 0, "precision": 10}}, {"source": {"name": "PORTFOLIO", "type": "String"}, "sink": {"name": "PORTFOLIO", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}, {"source": {"name": "PORTFOLIO_NAME", "type": "String"}, "sink": {"name": "PORTFOLIO_NAME", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}, {"source": {"name": "PRIMARY_OWNER_CONTACT_CODE", "type": "String"}, "sink": {"name": "PRIMARY_OWNER_CONTACT_CODE", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}, {"source": {"name": "PRIMARY_OWNER_CLIENT_KEY", "type": "Decimal"}, "sink": {"name": "PRIMARY_OWNER_CLIENT_KEY", "type": "Decimal", "physicalType": "decimal", "scale": 0, "precision": 14}}, {"source": {"name": "PORTFOLIO_GROUP", "type": "String"}, "sink": {"name": "PORTFOLIO_GROUP", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}, {"source": {"name": "GROUP_NAME", "type": "String"}, "sink": {"name": "GROUP_NAME", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}, {"source": {"name": "GROUP_TYPE", "type": "String"}, "sink": {"name": "GROUP_TYPE", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}, {"source": {"name": "GROUP_TYPE_NAME", "type": "String"}, "sink": {"name": "GROUP_TYPE_NAME", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}, {"source": {"name": "BUSINESS_SEGMENT", "type": "String"}, "sink": {"name": "BUSINESS_SEGMENT", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}, {"source": {"name": "PORTFOLIO_STATUS", "type": "String"}, "sink": {"name": "PORTFOLIO_STATUS", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}, {"source": {"name": "FUND_PORTFOLIO_TYPE", "type": "String"}, "sink": {"name": "FUND_PORTFOLIO_TYPE", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}, {"source": {"name": "COLLATERAL_ACCOUNT", "type": "String"}, "sink": {"name": "COLLATERAL_ACCOUNT", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}, {"source": {"name": "ACCREDITED_INVESTOR", "type": "String"}, "sink": {"name": "ACCREDITED_INVESTOR", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}, {"source": {"name": "ACCREDITED_INVESTOR_NAME", "type": "String"}, "sink": {"name": "ACCREDITED_INVESTOR_NAME", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}, {"source": {"name": "ANTI_MONEY_LAUNDERING_RISK", "type": "String"}, "sink": {"name": "ANTI_MONEY_LAUNDERING_RISK", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}, {"source": {"name": "CROSS_TRADE_APPROVAL", "type": "String"}, "sink": {"name": "CROSS_TRADE_APPROVAL", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}, {"source": {"name": "MAWER_PORTFOLIO_TYPE", "type": "String"}, "sink": {"name": "MAWER_PORTFOLIO_TYPE", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}, {"source": {"name": "CURRENCY", "type": "String"}, "sink": {"name": "CURRENCY", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}, {"source": {"name": "CURRENCY_NAME", "type": "String"}, "sink": {"name": "CURRENCY_NAME", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}, {"source": {"name": "HOLDINGS_TYPE", "type": "String"}, "sink": {"name": "HOLDINGS_TYPE", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}, {"source": {"name": "INSIDER_STATUS", "type": "String"}, "sink": {"name": "INSIDER_STATUS", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}, {"source": {"name": "INVESTMENT_AUTHORITY_13F", "type": "String"}, "sink": {"name": "INVESTMENT_AUTHORITY_13F", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}, {"source": {"name": "INVESTMENT_DISCRETION_13F", "type": "String"}, "sink": {"name": "INVESTMENT_DISCRETION_13F", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}, {"source": {"name": "INVESTMENT_OBJECTIVE", "type": "String"}, "sink": {"name": "INVESTMENT_OBJECTIVE", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}, {"source": {"name": "MODEL_PORTFOLIO", "type": "String"}, "sink": {"name": "MODEL_PORTFOLIO", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}, {"source": {"name": "MODEL_PORTFOLIO_TYPE", "type": "String"}, "sink": {"name": "MODEL_PORTFOLIO_TYPE", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}, {"source": {"name": "LEVERAGED_ACCOUNT", "type": "String"}, "sink": {"name": "LEVERAGED_ACCOUNT", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}, {"source": {"name": "MANAGEMENT_TYPE", "type": "String"}, "sink": {"name": "MANAGEMENT_TYPE", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}, {"source": {"name": "RISK_TOLERANCE", "type": "String"}, "sink": {"name": "RISK_TOLERANCE", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}, {"source": {"name": "TAXABLE_STATUS", "type": "String"}, "sink": {"name": "TAXABLE_STATUS", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}, {"source": {"name": "TAX_JURISDICTION", "type": "String"}, "sink": {"name": "TAX_JURISDICTION", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}, {"source": {"name": "TIME_HORIZON", "type": "String"}, "sink": {"name": "TIME_HORIZON", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}, {"source": {"name": "OPEN_DATE", "type": "DateTime"}, "sink": {"name": "OPEN_DATE", "type": "DateTime", "physicalType": "date"}}, {"source": {"name": "CLOSING_DATE", "type": "DateTime"}, "sink": {"name": "CLOSING_DATE", "type": "DateTime", "physicalType": "date"}}, {"source": {"name": "INCEPTION_DATE", "type": "DateTime"}, "sink": {"name": "INCEPTION_DATE", "type": "DateTime", "physicalType": "date"}}, {"source": {"name": "TERMINATION_DATE", "type": "DateTime"}, "sink": {"name": "TERMINATION_DATE", "type": "DateTime", "physicalType": "date"}}, {"source": {"name": "PORTFOLIO_TYPE", "type": "String"}, "sink": {"name": "PORTFOLIO_TYPE", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}, {"source": {"name": "PORTFOLIO_SUB_TYPE", "type": "String"}, "sink": {"name": "PORTFOLIO_SUB_TYPE", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}, {"source": {"name": "MANAGER_1", "type": "Decimal"}, "sink": {"name": "MANAGER_1", "type": "Decimal", "physicalType": "decimal", "scale": 0, "precision": 10}}, {"source": {"name": "TEXT_FOR_MANAGER_1", "type": "String"}, "sink": {"name": "TEXT_FOR_MANAGER_1", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}, {"source": {"name": "MANAGER_2", "type": "Decimal"}, "sink": {"name": "MANAGER_2", "type": "Decimal", "physicalType": "decimal", "scale": 0, "precision": 10}}, {"source": {"name": "TEXT_FOR_MANAGER_2", "type": "String"}, "sink": {"name": "TEXT_FOR_MANAGER_2", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}, {"source": {"name": "PRE_TRADE_COMPLIANCE", "type": "String"}, "sink": {"name": "PRE_TRADE_COMPLIANCE", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}, {"source": {"name": "PROXY_VOTING", "type": "String"}, "sink": {"name": "PROXY_VOTING", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}, {"source": {"name": "MULTI_STRATEGY_REBALANCING", "type": "String"}, "sink": {"name": "MULTI_STRATEGY_REBALANCING", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}, {"source": {"name": "OWNERSHIP_TYPE", "type": "String"}, "sink": {"name": "OWNERSHIP_TYPE", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}, {"source": {"name": "OWNERSHIP_TYPE_NAME", "type": "String"}, "sink": {"name": "OWNERSHIP_TYPE_NAME", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}, {"source": {"name": "PERMITTED_CLIENT", "type": "String"}, "sink": {"name": "PERMITTED_CLIENT", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}, {"source": {"name": "PERMITTED_CLIENT_NAME", "type": "String"}, "sink": {"name": "PERMITTED_CLIENT_NAME", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}, {"source": {"name": "POLITICALLY_EXPOSED_PERSON", "type": "String"}, "sink": {"name": "POLITICALLY_EXPOSED_PERSON", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}, {"source": {"name": "MARKETING_SEGMENT", "type": "String"}, "sink": {"name": "MARKETING_SEGMENT", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}, {"source": {"name": "TAX_REFERENCE_NO_IK", "type": "Decimal"}, "sink": {"name": "TAX_REFERENCE_NO_IK", "type": "Decimal", "physicalType": "decimal", "scale": 0, "precision": 5}}, {"source": {"name": "CUSTOMER_NUMBER", "type": "Decimal"}, "sink": {"name": "CUSTOMER_NUMBER", "type": "Decimal", "physicalType": "decimal", "scale": 0, "precision": 10}}, {"source": {"name": "CUSTOMER_REFERENCE", "type": "Decimal"}, "sink": {"name": "CUSTOMER_REFERENCE", "type": "Decimal", "physicalType": "decimal", "scale": 0, "precision": 5}}, {"source": {"name": "PORTFOLIO_BELONGS_TO_BANK", "type": "Decimal"}, "sink": {"name": "PORTFOLIO_BELONGS_TO_BANK", "type": "Decimal", "physicalType": "decimal", "scale": 0, "precision": 5}}, {"source": {"name": "REASON_FOR_CLOSURE", "type": "String"}, "sink": {"name": "REASON_FOR_CLOSURE", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}, {"source": {"name": "XFER_FROM_PORTFOLIO", "type": "String"}, "sink": {"name": "XFER_FROM_PORTFOLIO", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}, {"source": {"name": "XFER_TO_PORTFOLIO", "type": "String"}, "sink": {"name": "XFER_TO_PORTFOLIO", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}, {"source": {"name": "MAWER_PORTFOLIO_SUBTYPE", "type": "String"}, "sink": {"name": "MAWER_PORTFOLIO_SUBTYPE", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}, {"source": {"name": "PORTFOLIO_CLIENT_TYPE", "type": "String"}, "sink": {"name": "PORTFOLIO_CLIENT_TYPE", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}, {"source": {"name": "PORTFOLIO_END_OF_YEAR", "type": "String"}, "sink": {"name": "PORTFOLIO_END_OF_YEAR", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}, {"source": {"name": "COUNTRY", "type": "String"}, "sink": {"name": "COUNTRY", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}, {"source": {"name": "COUNTRY_NAME", "type": "String"}, "sink": {"name": "COUNTRY_NAME", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}, {"source": {"name": "OWNER_FIRST_NAME", "type": "String"}, "sink": {"name": "OWNER_FIRST_NAME", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}, {"source": {"name": "OWNER_LAST_NAME", "type": "String"}, "sink": {"name": "OWNER_LAST_NAME", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}, {"source": {"name": "OWNER_CONTACT_SSID", "type": "String"}, "sink": {"name": "OWNER_CONTACT_SSID", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}, {"source": {"name": "PRIMARY_OWNER_ID", "type": "String"}, "sink": {"name": "PRIMARY_OWNER_ID", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}, {"source": {"name": "BILLING_CITY", "type": "String"}, "sink": {"name": "BILLING_CITY", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}, {"source": {"name": "BILLING_COUNTRY", "type": "String"}, "sink": {"name": "BILLING_COUNTRY", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}, {"source": {"name": "BILLING_POSTAL_CODE", "type": "String"}, "sink": {"name": "BILLING_POSTAL_CODE", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}, {"source": {"name": "BILLING_STATE", "type": "String"}, "sink": {"name": "BILLING_STATE", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}, {"source": {"name": "BILLING_STREET", "type": "String"}, "sink": {"name": "BILLING_STREET", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}, {"source": {"name": "SHIPPING_CITY", "type": "String"}, "sink": {"name": "SHIPPING_CITY", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}, {"source": {"name": "SHIPPING_COUNTRY", "type": "String"}, "sink": {"name": "SHIPPING_COUNTRY", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}, {"source": {"name": "SHIPPING_POSTAL_CODE", "type": "String"}, "sink": {"name": "SHIPPING_POSTAL_CODE", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}, {"source": {"name": "SHIPPING_STATE", "type": "String"}, "sink": {"name": "SHIPPING_STATE", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}, {"source": {"name": "SHIPPING_STREET", "type": "String"}, "sink": {"name": "SHIPPING_STREET", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}, {"source": {"name": "JOINT_OWNER_FIRST_NAME", "type": "String"}, "sink": {"name": "JOINT_OWNER_FIRST_NAME", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}, {"source": {"name": "JOINT_OWNER_LAST_NAME", "type": "String"}, "sink": {"name": "JOINT_OWNER_LAST_NAME", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}, {"source": {"name": "JOINT_OWNER_CONTACT_SSID", "type": "String"}, "sink": {"name": "JOINT_OWNER_CONTACT_SSID", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}, {"source": {"name": "JOINT_OWNER_ID", "type": "String"}, "sink": {"name": "JOINT_OWNER_ID", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}, {"source": {"name": "FUND_ACCOUNT_NUMBER", "type": "String"}, "sink": {"name": "FUND_ACCOUNT_NUMBER", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}, {"source": {"name": "HOUSEHOLD_ID", "type": "String"}, "sink": {"name": "HOUSEHOLD_ID", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}, {"source": {"name": "OWNER_DIVISION", "type": "String"}, "sink": {"name": "OWNER_DIVISION", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}, {"source": {"name": "IS_CUSTODIAL_ACCOUNT", "type": "String"}, "sink": {"name": "IS_CUSTODIAL_ACCOUNT", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}, {"source": {"name": "CUSTODIAN_NAME", "type": "String"}, "sink": {"name": "CUSTODIAN_NAME", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}, {"source": {"name": "CUSTODIAN_NAME_OLD", "type": "String"}, "sink": {"name": "CUSTODIAN_NAME_OLD", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}, {"source": {"name": "CAD_CUSTODIAN_ACCT", "type": "String"}, "sink": {"name": "CAD_CUSTODIAN_ACCT", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}, {"source": {"name": "USD_CUSTODIAN_ACCT", "type": "String"}, "sink": {"name": "USD_CUSTODIAN_ACCT", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}, {"source": {"name": "PM_USER_ID", "type": "String"}, "sink": {"name": "PM_USER_ID", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}, {"source": {"name": "PM_TITLE", "type": "String"}, "sink": {"name": "PM_TITLE", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}, {"source": {"name": "PM_FIRST_NAME", "type": "String"}, "sink": {"name": "PM_FIRST_NAME", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}, {"source": {"name": "PM_LAST_NAME", "type": "String"}, "sink": {"name": "PM_LAST_NAME", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}, {"source": {"name": "PM_PHONE", "type": "String"}, "sink": {"name": "PM_PHONE", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}, {"source": {"name": "PM_EMAIL", "type": "String"}, "sink": {"name": "PM_EMAIL", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}, {"source": {"name": "PM_FAX", "type": "String"}, "sink": {"name": "PM_FAX", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}, {"source": {"name": "PM_SIGNATURE", "type": "String"}, "sink": {"name": "PM_SIGNATURE", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}, {"source": {"name": "PMA_USER_ID", "type": "String"}, "sink": {"name": "PMA_USER_ID", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}, {"source": {"name": "PMA_TITLE", "type": "String"}, "sink": {"name": "PMA_TITLE", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}, {"source": {"name": "PMA_FIRST_NAME", "type": "String"}, "sink": {"name": "PMA_FIRST_NAME", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}, {"source": {"name": "PMA_LAST_NAME", "type": "String"}, "sink": {"name": "PMA_LAST_NAME", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}, {"source": {"name": "PMA_PHONE", "type": "String"}, "sink": {"name": "PMA_PHONE", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}, {"source": {"name": "PMA_EMAIL", "type": "String"}, "sink": {"name": "PMA_EMAIL", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}, {"source": {"name": "PMA_FAX", "type": "String"}, "sink": {"name": "PMA_FAX", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}, {"source": {"name": "PMA_SIGNATURE", "type": "String"}, "sink": {"name": "PMA_SIGNATURE", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}, {"source": {"name": "PORTFOLIO_REPORTING_CURRENCY", "type": "String"}, "sink": {"name": "PORTFOLIO_REPORTING_CURRENCY", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}, {"source": {"name": "BILLABLE_FLAG", "type": "String"}, "sink": {"name": "BILLABLE_FLAG", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}, {"source": {"name": "ORIGINATING_SYSTEM_NO", "type": "Decimal"}, "sink": {"name": "ORIGINATING_SYSTEM_NO", "type": "Decimal", "physicalType": "decimal", "scale": 0, "precision": 14}}, {"source": {"name": "CURRENT_VERSION", "type": "String"}, "sink": {"name": "CURRENT_VERSION", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}, {"source": {"name": "PORTFOLIO_VALID_FROM_DATE", "type": "DateTime"}, "sink": {"name": "PORTFOLIO_VALID_FROM_DATE", "type": "DateTime", "physicalType": "date"}}, {"source": {"name": "PORTFOLIO_VALID_TO_DATE", "type": "DateTime"}, "sink": {"name": "PORTFOLIO_VALID_TO_DATE", "type": "DateTime", "physicalType": "date"}}, {"source": {"name": "FUND_INVESTMENT_VEHICLE_CODE", "type": "String"}, "sink": {"name": "FUND_INVESTMENT_VEHICLE_CODE", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}, {"source": {"name": "FUND_INVESTMENT_VEHICLE_NAME", "type": "String"}, "sink": {"name": "FUND_INVESTMENT_VEHICLE_NAME", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}, {"source": {"name": "PORTFOLIO_REPORTING_NUMBER", "type": "String"}, "sink": {"name": "PORTFOLIO_REPORTING_NUMBER", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}, {"source": {"name": "UNSUPERVISED_REPORTING", "type": "Decimal"}, "sink": {"name": "UNSUPERVISED_REPORTING", "type": "Decimal", "physicalType": "decimal", "scale": 0, "precision": 10}}], "typeConversion": true, "typeConversionSettings": {"allowDataTruncation": true, "treatBooleanAsNumber": false}}}, "inputs": [{"referenceName": "IMW_SCD_Oracle_UAT", "type": "DatasetReference", "parameters": {"Prefix": "SCDWH", "Table": "DM_PORTFOLIO"}}], "outputs": [{"referenceName": "DM_PORTFOLIO", "type": "DatasetReference"}]}, {"name": "DM_PORTFOLIO Row Count", "type": "Copy", "dependsOn": [{"activity": "DM_PORTFOLIO to MI", "dependencyConditions": ["Succeeded"]}], "policy": {"timeout": "7.00:00:00", "retry": 0, "retryIntervalInSeconds": 30, "secureOutput": false, "secureInput": false}, "userProperties": [], "typeProperties": {"source": {"type": "OracleSource", "oracleReaderQuery": "select NULL AS ProcessTS,'DM_PORTFOLIO' AS TableName,\nCOUNT(DWH_EXTRACT_PROCESS_IK) AS SourceCount,\nNULL AS TargetCount,\nNULL AS SourceSUM ,\nNULL as TargetSUM,\nNULL AS Status\nfrom SCDWH.DM_PORTFOLIO", "partitionOption": "None", "queryTimeout": "02:00:00"}, "sink": {"type": "SqlMISink", "writeBehavior": "insert", "sqlWriterUseTableLock": false}, "enableStaging": false, "translator": {"type": "TabularTranslator", "mappings": [{"source": {"name": "PROCESSTS", "type": "String"}, "sink": {"name": "ProcessTS", "type": "DateTime", "physicalType": "datetime"}}, {"source": {"name": "TABLENAME", "type": "String"}, "sink": {"name": "TableName", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}, {"source": {"name": "SOURCECOUNT", "type": "Double"}, "sink": {"name": "SourceCount", "type": "Int32", "physicalType": "int"}}, {"source": {"name": "TARGETCOUNT", "type": "String"}, "sink": {"name": "TargetCount", "type": "Int32", "physicalType": "int"}}, {"source": {"name": "SOURCESUM", "type": "String"}, "sink": {"name": "SourceSUM", "type": "Double", "physicalType": "float"}}, {"source": {"name": "TARGETSUM", "type": "String"}, "sink": {"name": "TargetSUM", "type": "Double", "physicalType": "float"}}, {"source": {"name": "STATUS", "type": "String"}, "sink": {"name": "Status", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}], "typeConversion": true, "typeConversionSettings": {"allowDataTruncation": true, "treatBooleanAsNumber": false}}}, "inputs": [{"referenceName": "IMW_SCD_Oracle_UAT", "type": "DatasetReference", "parameters": {"Prefix": "SCDWH", "Table": "DM_PORTFOLIO"}}], "outputs": [{"referenceName": "ReconLog", "type": "DatasetReference"}]}, {"name": "Log Final Status DM_PORTFOLIO", "type": "Lookup", "dependsOn": [{"activity": "DM_PORTFOLIO Row Count", "dependencyConditions": ["Succeeded"]}], "policy": {"timeout": "7.00:00:00", "retry": 0, "retryIntervalInSeconds": 30, "secureOutput": false, "secureInput": false}, "userProperties": [], "typeProperties": {"source": {"type": "SqlMISource", "sqlReaderQuery": "update dbo.ReconLog\nSET ProcessTS = getdate(),\nTargetCount = (SELECT COUNT(*) FROM dbo.DM_PORTFOLIO ),\nTargetSUM = NULL\nwhere TableName = 'DM_PORTFOLIO'\nand Status IS NULL\n\nupdate dbo.ReconLog\nset Status = (case when SourceCount = TargetCount  then 'Success' Else 'Failed' END)\nwhere Status IS NULL and TableName = 'DM_PORTFOLIO'\n\nSELECT top 1 * FROM dbo.ReconLog ", "partitionOption": "None"}, "dataset": {"referenceName": "DM_PORTFOLIO", "type": "DatasetReference"}}}]}}, {"name": "4 DM_PORTFOLIO_GROUP to MI", "type": "IfCondition", "dependsOn": [], "userProperties": [], "typeProperties": {"expression": {"value": "@equals(pipeline().parameters.Load_DM_PORTFOLIO_GROUP,'1')", "type": "Expression"}, "ifTrueActivities": [{"name": "DM_PORTFOLIO_GROUP to MI", "type": "Copy", "dependsOn": [], "policy": {"timeout": "7.00:00:00", "retry": 0, "retryIntervalInSeconds": 30, "secureOutput": false, "secureInput": false}, "userProperties": [], "typeProperties": {"source": {"type": "OracleSource", "partitionOption": "None", "queryTimeout": "02:00:00"}, "sink": {"type": "SqlMISink", "preCopyScript": "delete from DM_PORTFOLIO_GROUP"}, "enableStaging": false, "translator": {"type": "TabularTranslator", "mappings": [{"source": {"name": "DWH_EXTRACT_PROCESS_IK", "type": "Decimal"}, "sink": {"name": "DWH_EXTRACT_PROCESS_IK", "type": "Decimal", "physicalType": "decimal", "scale": 0, "precision": 10}}, {"source": {"name": "DWH_LOAD_TS", "type": "DateTime"}, "sink": {"name": "DWH_LOAD_TS", "type": "DateTime", "physicalType": "datetime"}}, {"source": {"name": "LAST_CHANGE_TS", "type": "DateTime"}, "sink": {"name": "LAST_CHANGE_TS", "type": "DateTime", "physicalType": "datetime"}}, {"source": {"name": "GROUP_KEY", "type": "Decimal"}, "sink": {"name": "GROUP_KEY", "type": "Decimal", "physicalType": "decimal", "scale": 0, "precision": 14}}, {"source": {"name": "GROUP_IK", "type": "Decimal"}, "sink": {"name": "GROUP_IK", "type": "Decimal", "physicalType": "decimal", "scale": 0, "precision": 10}}, {"source": {"name": "GROUP_CODE", "type": "String"}, "sink": {"name": "GROUP_CODE", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}, {"source": {"name": "GROUP_NAME", "type": "String"}, "sink": {"name": "GROUP_NAME", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}, {"source": {"name": "GROUP_TYPE", "type": "String"}, "sink": {"name": "GROUP_TYPE", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}, {"source": {"name": "GROUP_TYPE_NAME", "type": "String"}, "sink": {"name": "GROUP_TYPE_NAME", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}, {"source": {"name": "ORIGINATING_SYSTEM_NO", "type": "Decimal"}, "sink": {"name": "ORIGINATING_SYSTEM_NO", "type": "Decimal", "physicalType": "decimal", "scale": 0, "precision": 14}}, {"source": {"name": "CURRENT_VERSION", "type": "String"}, "sink": {"name": "CURRENT_VERSION", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}, {"source": {"name": "VALID_FROM_DATE", "type": "DateTime"}, "sink": {"name": "VALID_FROM_DATE", "type": "DateTime", "physicalType": "date"}}, {"source": {"name": "VALID_TO_DATE", "type": "DateTime"}, "sink": {"name": "VALID_TO_DATE", "type": "DateTime", "physicalType": "date"}}, {"source": {"name": "MODEL_MANDATE", "type": "String"}, "sink": {"name": "MODEL_MANDATE", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}, {"source": {"name": "MODEL_MANDATE_NAME", "type": "String"}, "sink": {"name": "MODEL_MANDATE_NAME", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}, {"source": {"name": "EQUITY_LOWER_LIMIT", "type": "Decimal"}, "sink": {"name": "EQUITY_LOWER_LIMIT", "type": "Decimal", "physicalType": "decimal", "scale": 0, "precision": 10}}, {"source": {"name": "EQUITY_UPPER_LIMIT", "type": "Decimal"}, "sink": {"name": "EQUITY_UPPER_LIMIT", "type": "Decimal", "physicalType": "decimal", "scale": 0, "precision": 10}}, {"source": {"name": "GROUP_INCEPTION_DATE", "type": "String"}, "sink": {"name": "GROUP_INCEPTION_DATE", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}], "typeConversion": true, "typeConversionSettings": {"allowDataTruncation": true, "treatBooleanAsNumber": false}}}, "inputs": [{"referenceName": "IMW_SCD_Oracle_UAT", "type": "DatasetReference", "parameters": {"Prefix": "SCDWH", "Table": "DM_PORTFOLIO_GROUP"}}], "outputs": [{"referenceName": "DM_PORTFOLIO_GROUP", "type": "DatasetReference"}]}, {"name": "DM_PORTFOLIO_GROUP Row Count", "type": "Copy", "dependsOn": [{"activity": "DM_PORTFOLIO_GROUP to MI", "dependencyConditions": ["Succeeded"]}], "policy": {"timeout": "7.00:00:00", "retry": 0, "retryIntervalInSeconds": 30, "secureOutput": false, "secureInput": false}, "userProperties": [], "typeProperties": {"source": {"type": "OracleSource", "oracleReaderQuery": "select NULL AS ProcessTS,'DM_PORTFOLIO_GROUP' AS TableName,\nCOUNT(DWH_EXTRACT_PROCESS_IK) AS SourceCount,\nNULL AS TargetCount,\nNULL AS SourceSUM ,\nNULL as TargetSUM,\nNULL AS Status\nfrom SCDWH.DM_PORTFOLIO_GROUP", "partitionOption": "None", "queryTimeout": "02:00:00"}, "sink": {"type": "SqlMISink", "writeBehavior": "insert", "sqlWriterUseTableLock": false}, "enableStaging": false, "translator": {"type": "TabularTranslator", "mappings": [{"source": {"name": "PROCESSTS", "type": "String"}, "sink": {"name": "ProcessTS", "type": "DateTime", "physicalType": "datetime"}}, {"source": {"name": "TABLENAME", "type": "String"}, "sink": {"name": "TableName", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}, {"source": {"name": "SOURCECOUNT", "type": "Double"}, "sink": {"name": "SourceCount", "type": "Int32", "physicalType": "int"}}, {"source": {"name": "TARGETCOUNT", "type": "String"}, "sink": {"name": "TargetCount", "type": "Int32", "physicalType": "int"}}, {"source": {"name": "SOURCESUM", "type": "String"}, "sink": {"name": "SourceSUM", "type": "Double", "physicalType": "float"}}, {"source": {"name": "TARGETSUM", "type": "String"}, "sink": {"name": "TargetSUM", "type": "Double", "physicalType": "float"}}, {"source": {"name": "STATUS", "type": "String"}, "sink": {"name": "Status", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}], "typeConversion": true, "typeConversionSettings": {"allowDataTruncation": true, "treatBooleanAsNumber": false}}}, "inputs": [{"referenceName": "IMW_SCD_Oracle_UAT", "type": "DatasetReference", "parameters": {"Prefix": "SCDWH", "Table": "DM_PORTFOLIO_GROUP"}}], "outputs": [{"referenceName": "ReconLog", "type": "DatasetReference"}]}, {"name": "Log Final Status DM_PORTFOLIO_GROUP", "type": "Lookup", "dependsOn": [{"activity": "DM_PORTFOLIO_GROUP Row Count", "dependencyConditions": ["Succeeded"]}], "policy": {"timeout": "7.00:00:00", "retry": 0, "retryIntervalInSeconds": 30, "secureOutput": false, "secureInput": false}, "userProperties": [], "typeProperties": {"source": {"type": "SqlMISource", "sqlReaderQuery": "update dbo.ReconLog\nSET ProcessTS = getdate(),\nTargetCount = (SELECT COUNT(*) FROM dbo.DM_PORTFOLIO_GROUP ),\nTargetSUM = NULL\nwhere TableName = 'DM_PORTFOLIO_GROUP'\nand Status IS NULL\n\nupdate dbo.ReconLog\nset Status = (case when SourceCount = TargetCount  then 'Success' Else 'Failed' END)\nwhere Status IS NULL and TableName = 'DM_PORTFOLIO_GROUP'\n\nSELECT top 1 * FROM dbo.ReconLog ", "partitionOption": "None"}, "dataset": {"referenceName": "DM_PORTFOLIO_GROUP", "type": "DatasetReference"}}}]}}, {"name": "5 DM_COUNTRY to MI", "type": "IfCondition", "dependsOn": [], "userProperties": [], "typeProperties": {"expression": {"value": "@equals(pipeline().parameters.Load_DM_COUNTRY,'1')", "type": "Expression"}, "ifTrueActivities": [{"name": "DM_COUNTRY to MI", "type": "Copy", "dependsOn": [], "policy": {"timeout": "7.00:00:00", "retry": 0, "retryIntervalInSeconds": 30, "secureOutput": false, "secureInput": false}, "userProperties": [], "typeProperties": {"source": {"type": "OracleSource", "partitionOption": "None", "queryTimeout": "02:00:00"}, "sink": {"type": "SqlMISink", "preCopyScript": "delete from dbo.DM_COUNTRY"}, "enableStaging": false, "translator": {"type": "TabularTranslator", "mappings": [{"source": {"name": "DWH_EXTRACT_PROCESS_IK", "type": "Decimal"}, "sink": {"name": "DWH_EXTRACT_PROCESS_IK", "type": "Decimal", "physicalType": "decimal", "scale": 0, "precision": 10}}, {"source": {"name": "DWH_LOAD_TS", "type": "DateTime"}, "sink": {"name": "DWH_LOAD_TS", "type": "DateTime", "physicalType": "datetime"}}, {"source": {"name": "LAST_CHANGE_TS", "type": "DateTime"}, "sink": {"name": "LAST_CHANGE_TS", "type": "DateTime", "physicalType": "datetime"}}, {"source": {"name": "COUNTRY_KEY", "type": "Decimal"}, "sink": {"name": "COUNTRY_KEY", "type": "Decimal", "physicalType": "decimal", "scale": 0, "precision": 14}}, {"source": {"name": "COUNTRY_IK", "type": "Decimal"}, "sink": {"name": "COUNTRY_IK", "type": "Decimal", "physicalType": "decimal", "scale": 0, "precision": 5}}, {"source": {"name": "COUNTRY", "type": "String"}, "sink": {"name": "COUNTRY", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}, {"source": {"name": "COUNTRY_NAME", "type": "String"}, "sink": {"name": "COUNTRY_NAME", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}, {"source": {"name": "COUNTRY_ZONE", "type": "String"}, "sink": {"name": "COUNTRY_ZONE", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}, {"source": {"name": "COUNTRY_ZONE_NAME", "type": "String"}, "sink": {"name": "COUNTRY_ZONE_NAME", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}, {"source": {"name": "COUNTRY_CODE_ISO3", "type": "String"}, "sink": {"name": "COUNTRY_CODE_ISO3", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}, {"source": {"name": "MSCI_ACWI_CLASSIFICATION", "type": "String"}, "sink": {"name": "MSCI_ACWI_CLASSIFICATION", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}, {"source": {"name": "MSCI_REGION_OTHER", "type": "String"}, "sink": {"name": "MSCI_REGION_OTHER", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}, {"source": {"name": "TRADING_DESK_CLASSIFICATION", "type": "String"}, "sink": {"name": "TRADING_DESK_CLASSIFICATION", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}, {"source": {"name": "ORIGINATING_SYSTEM_NO", "type": "Decimal"}, "sink": {"name": "ORIGINATING_SYSTEM_NO", "type": "Decimal", "physicalType": "decimal", "scale": 0, "precision": 14}}, {"source": {"name": "CURRENT_VERSION", "type": "String"}, "sink": {"name": "CURRENT_VERSION", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}, {"source": {"name": "VALID_FROM_DATE", "type": "DateTime"}, "sink": {"name": "VALID_FROM_DATE", "type": "DateTime", "physicalType": "date"}}, {"source": {"name": "VALID_TO_DATE", "type": "DateTime"}, "sink": {"name": "VALID_TO_DATE", "type": "DateTime", "physicalType": "date"}}, {"source": {"name": "MSCI_MARKET_TYPE", "type": "String"}, "sink": {"name": "MSCI_MARKET_TYPE", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}, {"source": {"name": "MSCI_REGION_EM", "type": "String"}, "sink": {"name": "MSCI_REGION_EM", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}], "typeConversion": true, "typeConversionSettings": {"allowDataTruncation": true, "treatBooleanAsNumber": false}}}, "inputs": [{"referenceName": "IMW_SCD_Oracle_UAT", "type": "DatasetReference", "parameters": {"Prefix": "SCDWH", "Table": "DM_COUNTRY"}}], "outputs": [{"referenceName": "DM_COUNTRY", "type": "DatasetReference"}]}, {"name": "DM_COUNTRY Row Count", "type": "Copy", "dependsOn": [{"activity": "DM_COUNTRY to MI", "dependencyConditions": ["Succeeded"]}], "policy": {"timeout": "7.00:00:00", "retry": 0, "retryIntervalInSeconds": 30, "secureOutput": false, "secureInput": false}, "userProperties": [], "typeProperties": {"source": {"type": "OracleSource", "oracleReaderQuery": "select NULL AS ProcessTS,'DM_COUNTRY' AS TableName,\nCOUNT(DWH_EXTRACT_PROCESS_IK) AS SourceCount,\nNULL AS TargetCount,\nNULL AS SourceSUM ,\nNULL as TargetSUM,\nNULL AS Status\nfrom SCDWH.DM_COUNTRY", "partitionOption": "None", "queryTimeout": "02:00:00"}, "sink": {"type": "SqlMISink", "writeBehavior": "insert", "sqlWriterUseTableLock": false}, "enableStaging": false, "translator": {"type": "TabularTranslator", "mappings": [{"source": {"name": "PROCESSTS", "type": "String"}, "sink": {"name": "ProcessTS", "type": "DateTime", "physicalType": "datetime"}}, {"source": {"name": "TABLENAME", "type": "String"}, "sink": {"name": "TableName", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}, {"source": {"name": "SOURCECOUNT", "type": "Double"}, "sink": {"name": "SourceCount", "type": "Int32", "physicalType": "int"}}, {"source": {"name": "TARGETCOUNT", "type": "String"}, "sink": {"name": "TargetCount", "type": "Int32", "physicalType": "int"}}, {"source": {"name": "SOURCESUM", "type": "String"}, "sink": {"name": "SourceSUM", "type": "Double", "physicalType": "float"}}, {"source": {"name": "TARGETSUM", "type": "String"}, "sink": {"name": "TargetSUM", "type": "Double", "physicalType": "float"}}, {"source": {"name": "STATUS", "type": "String"}, "sink": {"name": "Status", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}], "typeConversion": true, "typeConversionSettings": {"allowDataTruncation": true, "treatBooleanAsNumber": false}}}, "inputs": [{"referenceName": "IMW_SCD_Oracle_UAT", "type": "DatasetReference", "parameters": {"Prefix": "SCDWH", "Table": "DM_COUNTRY"}}], "outputs": [{"referenceName": "ReconLog", "type": "DatasetReference"}]}, {"name": "Log Final Status DM_COUNTRY", "type": "Lookup", "dependsOn": [{"activity": "DM_COUNTRY Row Count", "dependencyConditions": ["Succeeded"]}], "policy": {"timeout": "7.00:00:00", "retry": 0, "retryIntervalInSeconds": 30, "secureOutput": false, "secureInput": false}, "userProperties": [], "typeProperties": {"source": {"type": "SqlMISource", "sqlReaderQuery": "update dbo.ReconLog\nSET ProcessTS = getdate(),\nTargetCount = (SELECT COUNT(*) FROM dbo.DM_COUNTRY ),\nTargetSUM = NULL\nwhere TableName = 'DM_COUNTRY'\nand Status IS NULL\n\nupdate dbo.ReconLog\nset Status = (case when SourceCount = TargetCount  then 'Success' Else 'Failed' END)\nwhere Status IS NULL and TableName = 'DM_COUNTRY'\n\nSELECT top 1 * FROM dbo.ReconLog ", "partitionOption": "None"}, "dataset": {"referenceName": "DM_COUNTRY", "type": "DatasetReference"}}}]}}, {"name": "6 DM_SECURITY to MI", "type": "IfCondition", "dependsOn": [], "userProperties": [], "typeProperties": {"expression": {"value": "@equals(pipeline().parameters.Load_DM_SECURITY,'1')", "type": "Expression"}, "ifTrueActivities": [{"name": "DM_SECURITY to MI", "type": "Copy", "dependsOn": [], "policy": {"timeout": "7.00:00:00", "retry": 0, "retryIntervalInSeconds": 30, "secureOutput": false, "secureInput": false}, "userProperties": [], "typeProperties": {"source": {"type": "OracleSource", "partitionOption": "None", "queryTimeout": "02:00:00"}, "sink": {"type": "SqlMISink", "preCopyScript": "delete from dbo.DM_SECURITY", "writeBehavior": "insert", "sqlWriterUseTableLock": false}, "enableStaging": false, "translator": {"type": "TabularTranslator", "mappings": [{"source": {"name": "DWH_EXTRACT_PROCESS_IK", "type": "Decimal"}, "sink": {"name": "DWH_EXTRACT_PROCESS_IK", "type": "Decimal", "physicalType": "decimal", "scale": 0, "precision": 10}}, {"source": {"name": "DWH_LOAD_TS", "type": "DateTime"}, "sink": {"name": "DWH_LOAD_TS", "type": "DateTime", "physicalType": "datetime"}}, {"source": {"name": "LAST_CHANGE_TS", "type": "DateTime"}, "sink": {"name": "LAST_CHANGE_TS", "type": "DateTime", "physicalType": "datetime"}}, {"source": {"name": "SECURITY_KEY", "type": "Decimal"}, "sink": {"name": "SECURITY_KEY", "type": "Decimal", "physicalType": "decimal", "scale": 0, "precision": 14}}, {"source": {"name": "SECURITY_IK", "type": "Decimal"}, "sink": {"name": "SECURITY_IK", "type": "Decimal", "physicalType": "decimal", "scale": 0, "precision": 10}}, {"source": {"name": "SECURITY_ID", "type": "String"}, "sink": {"name": "SECURITY_ID", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}, {"source": {"name": "SECURITY_NAME", "type": "String"}, "sink": {"name": "SECURITY_NAME", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}, {"source": {"name": "SECURITY_NAME_REPORTING", "type": "String"}, "sink": {"name": "SECURITY_NAME_REPORTING", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}, {"source": {"name": "CUSIP", "type": "String"}, "sink": {"name": "CUSIP", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}, {"source": {"name": "ISIN", "type": "String"}, "sink": {"name": "ISIN", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}, {"source": {"name": "SEDOL", "type": "String"}, "sink": {"name": "SEDOL", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}, {"source": {"name": "TICKER", "type": "String"}, "sink": {"name": "TICKER", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}, {"source": {"name": "BB_UNIQUE_ID", "type": "String"}, "sink": {"name": "BB_UNIQUE_ID", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}, {"source": {"name": "CINS", "type": "String"}, "sink": {"name": "CINS", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}, {"source": {"name": "SMF_SOURCE", "type": "Decimal"}, "sink": {"name": "SMF_SOURCE", "type": "Decimal", "physicalType": "decimal", "scale": 2, "precision": 18}}, {"source": {"name": "SECURITY_GROUP", "type": "String"}, "sink": {"name": "SECURITY_GROUP", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}, {"source": {"name": "SECURITY_GROUP_NAME", "type": "String"}, "sink": {"name": "SECURITY_GROUP_NAME", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}, {"source": {"name": "SECURITY_TYPE", "type": "String"}, "sink": {"name": "SECURITY_TYPE", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}, {"source": {"name": "SECURITY_TYPE_NAME", "type": "String"}, "sink": {"name": "SECURITY_TYPE_NAME", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}, {"source": {"name": "INSTRUMENT_TYPE", "type": "String"}, "sink": {"name": "INSTRUMENT_TYPE", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}, {"source": {"name": "TRADE_ACTIVE_STATUS", "type": "String"}, "sink": {"name": "TRADE_ACTIVE_STATUS", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}, {"source": {"name": "ISSUER_NAME", "type": "String"}, "sink": {"name": "ISSUER_NAME", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}, {"source": {"name": "ISSUER_COUNTRY", "type": "String"}, "sink": {"name": "ISSUER_COUNTRY", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}, {"source": {"name": "EXCHANGE", "type": "String"}, "sink": {"name": "EXCHANGE", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}, {"source": {"name": "EXCHANGE_COUNTRY", "type": "String"}, "sink": {"name": "EXCHANGE_COUNTRY", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}, {"source": {"name": "QUOTATION_CURRENCY", "type": "String"}, "sink": {"name": "QUOTATION_CURRENCY", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}, {"source": {"name": "US_SECTION_13F_FLAG", "type": "String"}, "sink": {"name": "US_SECTION_13F_FLAG", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}, {"source": {"name": "SGC_ASSET_VS_CASH", "type": "String"}, "sink": {"name": "SGC_ASSET_VS_CASH", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}, {"source": {"name": "SGC_FO_EQ_MARKET_FLAG", "type": "String"}, "sink": {"name": "SGC_FO_EQ_MARKET_FLAG", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}, {"source": {"name": "SGC_FIXED_FLOATING", "type": "String"}, "sink": {"name": "SGC_FIXED_FLOATING", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}, {"source": {"name": "SGC_FRONT_OFFICE_AC", "type": "String"}, "sink": {"name": "SGC_FRONT_OFFICE_AC", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}, {"source": {"name": "SGC_HY_IG", "type": "String"}, "sink": {"name": "SGC_HY_IG", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}, {"source": {"name": "SGC_ISSUE_DATE", "type": "String"}, "sink": {"name": "SGC_ISSUE_DATE", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}, {"source": {"name": "SGC_MARKET_CAP", "type": "String"}, "sink": {"name": "SGC_MARKET_CAP", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}, {"source": {"name": "SGC_MATURITY_BUCKET", "type": "String"}, "sink": {"name": "SGC_MATURITY_BUCKET", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}, {"source": {"name": "SGC_OLD_RATING", "type": "String"}, "sink": {"name": "SGC_OLD_RATING", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}, {"source": {"name": "SGC_PAR_ISSUER_FIPA", "type": "String"}, "sink": {"name": "SGC_PAR_ISSUER_FIPA", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}, {"source": {"name": "SGC_PAR_SECTOR_FIPA", "type": "String"}, "sink": {"name": "SGC_PAR_SECTOR_FIPA", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}, {"source": {"name": "SGC_RATING_CHANGE", "type": "String"}, "sink": {"name": "SGC_RATING_CHANGE", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}, {"source": {"name": "SGC_REPORTING_AC", "type": "String"}, "sink": {"name": "SGC_REPORTING_AC", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}, {"source": {"name": "SGC_SECURITY_COUNTRY", "type": "String"}, "sink": {"name": "SGC_SECURITY_COUNTRY", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}, {"source": {"name": "SGC_SECURITY_ISSUER", "type": "String"}, "sink": {"name": "SGC_SECURITY_ISSUER", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}, {"source": {"name": "CIRCULATING_SHARES", "type": "Decimal"}, "sink": {"name": "CIRCULATING_SHARES", "type": "Decimal", "physicalType": "decimal", "scale": 2, "precision": 17}}, {"source": {"name": "MAX_NOTES_PER_SHRE", "type": "Decimal"}, "sink": {"name": "MAX_NOTES_PER_SHRE", "type": "Decimal", "physicalType": "decimal", "scale": 3, "precision": 8}}, {"source": {"name": "NOMINAL_PER_SHARE", "type": "Decimal"}, "sink": {"name": "NOMINAL_PER_SHARE", "type": "Decimal", "physicalType": "decimal", "scale": 9, "precision": 20}}, {"source": {"name": "VOTES_PER_SHARE", "type": "Decimal"}, "sink": {"name": "VOTES_PER_SHARE", "type": "Decimal", "physicalType": "decimal", "scale": 4, "precision": 9}}, {"source": {"name": "VOTING_RIGHTS", "type": "Decimal"}, "sink": {"name": "VOTING_RIGHTS", "type": "Decimal", "physicalType": "decimal", "scale": 0, "precision": 5}}, {"source": {"name": "SHARES_OUTSTANDING", "type": "String"}, "sink": {"name": "SHARES_OUTSTANDING", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}, {"source": {"name": "BILLABLE_FLAG", "type": "String"}, "sink": {"name": "BILLABLE_FLAG", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}, {"source": {"name": "ORIGINATING_SYSTEM_NO", "type": "Decimal"}, "sink": {"name": "ORIGINATING_SYSTEM_NO", "type": "Decimal", "physicalType": "decimal", "scale": 0, "precision": 14}}, {"source": {"name": "CURRENT_VERSION", "type": "String"}, "sink": {"name": "CURRENT_VERSION", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}, {"source": {"name": "VALID_FROM_DATE", "type": "DateTime"}, "sink": {"name": "VALID_FROM_DATE", "type": "DateTime", "physicalType": "date"}}, {"source": {"name": "VALID_TO_DATE", "type": "DateTime"}, "sink": {"name": "VALID_TO_DATE", "type": "DateTime", "physicalType": "date"}}, {"source": {"name": "ISSUED_VOLUME", "type": "Double"}, "sink": {"name": "ISSUED_VOLUME", "type": "Decimal", "physicalType": "decimal", "scale": 0, "precision": 28}}, {"source": {"name": "MSCI_COUNTRY", "type": "String"}, "sink": {"name": "MSCI_COUNTRY", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}, {"source": {"name": "REUTER_TICKER_CODE", "type": "String"}, "sink": {"name": "REUTER_TICKER_CODE", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}, {"source": {"name": "GICS_SECTOR", "type": "String"}, "sink": {"name": "GICS_SECTOR", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}, {"source": {"name": "GICS_SECTOR_NAME", "type": "String"}, "sink": {"name": "GICS_SECTOR_NAME", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}, {"source": {"name": "GICS_INDUSTRY", "type": "String"}, "sink": {"name": "GICS_INDUSTRY", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}, {"source": {"name": "GICS_INDUSTRY_NAME", "type": "String"}, "sink": {"name": "GICS_INDUSTRY_NAME", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}, {"source": {"name": "GICS_INDUSTRY_GROUP", "type": "String"}, "sink": {"name": "GICS_INDUSTRY_GROUP", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}, {"source": {"name": "GICS_INDUSTRY_GROUP_NAME", "type": "String"}, "sink": {"name": "GICS_INDUSTRY_GROUP_NAME", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}], "typeConversion": true, "typeConversionSettings": {"allowDataTruncation": true, "treatBooleanAsNumber": false}}}, "inputs": [{"referenceName": "IMW_SCD_Oracle_UAT", "type": "DatasetReference", "parameters": {"Prefix": "SCDWH", "Table": "DM_SECURITY"}}], "outputs": [{"referenceName": "DM_SECURITY", "type": "DatasetReference"}]}, {"name": "DM_SECURITY Row Count", "type": "Copy", "dependsOn": [{"activity": "DM_SECURITY to MI", "dependencyConditions": ["Succeeded"]}], "policy": {"timeout": "7.00:00:00", "retry": 0, "retryIntervalInSeconds": 30, "secureOutput": false, "secureInput": false}, "userProperties": [], "typeProperties": {"source": {"type": "OracleSource", "oracleReaderQuery": "select NULL AS ProcessTS,'DM_SECURITY' AS TableName,\nCOUNT(DWH_EXTRACT_PROCESS_IK) AS SourceCount,\nNULL AS TargetCount,\nNULL AS SourceSUM ,\nNULL as TargetSUM,\nNULL AS Status\nfrom SCDWH.DM_SECURITY", "partitionOption": "None", "queryTimeout": "02:00:00"}, "sink": {"type": "SqlMISink", "writeBehavior": "insert", "sqlWriterUseTableLock": false}, "enableStaging": false, "translator": {"type": "TabularTranslator", "mappings": [{"source": {"name": "PROCESSTS", "type": "String"}, "sink": {"name": "ProcessTS", "type": "DateTime", "physicalType": "datetime"}}, {"source": {"name": "TABLENAME", "type": "String"}, "sink": {"name": "TableName", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}, {"source": {"name": "SOURCECOUNT", "type": "Double"}, "sink": {"name": "SourceCount", "type": "Int32", "physicalType": "int"}}, {"source": {"name": "TARGETCOUNT", "type": "String"}, "sink": {"name": "TargetCount", "type": "Int32", "physicalType": "int"}}, {"source": {"name": "SOURCESUM", "type": "String"}, "sink": {"name": "SourceSUM", "type": "Double", "physicalType": "float"}}, {"source": {"name": "TARGETSUM", "type": "String"}, "sink": {"name": "TargetSUM", "type": "Double", "physicalType": "float"}}, {"source": {"name": "STATUS", "type": "String"}, "sink": {"name": "Status", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}], "typeConversion": true, "typeConversionSettings": {"allowDataTruncation": true, "treatBooleanAsNumber": false}}}, "inputs": [{"referenceName": "IMW_SCD_Oracle_UAT", "type": "DatasetReference", "parameters": {"Prefix": "SCDWH", "Table": "DM_SECURITY"}}], "outputs": [{"referenceName": "ReconLog", "type": "DatasetReference"}]}, {"name": "Log Final Status DM_SECURITY", "type": "Lookup", "dependsOn": [{"activity": "DM_SECURITY Row Count", "dependencyConditions": ["Succeeded"]}], "policy": {"timeout": "7.00:00:00", "retry": 0, "retryIntervalInSeconds": 30, "secureOutput": false, "secureInput": false}, "userProperties": [], "typeProperties": {"source": {"type": "SqlMISource", "sqlReaderQuery": "update dbo.ReconLog\nSET ProcessTS = getdate(),\nTargetCount = (SELECT COUNT(*) FROM dbo.DM_SECURITY ),\nTargetSUM = NULL\nwhere TableName = 'DM_SECURITY'\nand Status IS NULL\n\nupdate dbo.ReconLog\nset Status = (case when SourceCount = TargetCount  then 'Success' Else 'Failed' END)\nwhere Status IS NULL and TableName = 'DM_SECURITY'\n\nSELECT top 1 * FROM dbo.ReconLog ", "partitionOption": "None"}, "dataset": {"referenceName": "DM_SECURITY", "type": "DatasetReference"}}}]}}, {"name": "7 DM_CALENDAR to MI", "type": "IfCondition", "dependsOn": [], "userProperties": [], "typeProperties": {"expression": {"value": "@equals(pipeline().parameters.Load_DM_CALENDAR,'1')", "type": "Expression"}, "ifTrueActivities": [{"name": "DM_CALENDAR to MI", "type": "Copy", "dependsOn": [], "policy": {"timeout": "7.00:00:00", "retry": 0, "retryIntervalInSeconds": 30, "secureOutput": false, "secureInput": false}, "userProperties": [], "typeProperties": {"source": {"type": "OracleSource", "partitionOption": "None", "queryTimeout": "02:00:00"}, "sink": {"type": "SqlMISink", "preCopyScript": "delete from dbo.DM_CALENDAR", "writeBehavior": "insert", "sqlWriterUseTableLock": false}, "enableStaging": false, "translator": {"type": "TabularTranslator", "mappings": [{"source": {"name": "DWH_EXTRACT_PROCESS_IK", "type": "Decimal"}, "sink": {"name": "DWH_EXTRACT_PROCESS_IK", "type": "Decimal", "physicalType": "decimal", "scale": 0, "precision": 10}}, {"source": {"name": "DWH_LOAD_TS", "type": "DateTime"}, "sink": {"name": "DWH_LOAD_TS", "type": "DateTime", "physicalType": "datetime"}}, {"source": {"name": "LAST_CHANGE_TS", "type": "DateTime"}, "sink": {"name": "LAST_CHANGE_TS", "type": "DateTime", "physicalType": "datetime"}}, {"source": {"name": "CALENDAR_KEY", "type": "Decimal"}, "sink": {"name": "CALENDAR_KEY", "type": "Decimal", "physicalType": "decimal", "scale": 0, "precision": 14}}, {"source": {"name": "DATE_FULL", "type": "DateTime"}, "sink": {"name": "DATE_FULL", "type": "DateTime", "physicalType": "date"}}, {"source": {"name": "DATE_EU", "type": "String"}, "sink": {"name": "DATE_EU", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}, {"source": {"name": "DATE_ISO", "type": "String"}, "sink": {"name": "DATE_ISO", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}, {"source": {"name": "DATE_US", "type": "String"}, "sink": {"name": "DATE_US", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}, {"source": {"name": "DAY_NAME", "type": "String"}, "sink": {"name": "DAY_NAME", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}, {"source": {"name": "DAY_NAME_LONG", "type": "String"}, "sink": {"name": "DAY_NAME_LONG", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}, {"source": {"name": "DAY_NUMBER_IN_MONTH", "type": "String"}, "sink": {"name": "DAY_NUMBER_IN_MONTH", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}, {"source": {"name": "DAY_NUMBER_IN_YEAR", "type": "String"}, "sink": {"name": "DAY_NUMBER_IN_YEAR", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}, {"source": {"name": "DAY_OF_WEEK", "type": "String"}, "sink": {"name": "DAY_OF_WEEK", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}, {"source": {"name": "FIRST_DAY_IN_MONTH", "type": "String"}, "sink": {"name": "FIRST_DAY_IN_MONTH", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}, {"source": {"name": "FIRST_DAY_IN_QUARTER", "type": "String"}, "sink": {"name": "FIRST_DAY_IN_QUARTER", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}, {"source": {"name": "FIRST_DAY_IN_YEAR", "type": "String"}, "sink": {"name": "FIRST_DAY_IN_YEAR", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}, {"source": {"name": "LAST_BUSINESS_DAY_IN_MONTH", "type": "String"}, "sink": {"name": "LAST_BUSINESS_DAY_IN_MONTH", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}, {"source": {"name": "LAST_BUSINESS_DAY_IN_QUARTER", "type": "String"}, "sink": {"name": "LAST_BUSINESS_DAY_IN_QUARTER", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}, {"source": {"name": "LAST_BUSINESS_DAY_IN_YEAR", "type": "String"}, "sink": {"name": "LAST_BUSINESS_DAY_IN_YEAR", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}, {"source": {"name": "LAST_DAY_IN_MONTH", "type": "String"}, "sink": {"name": "LAST_DAY_IN_MONTH", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}, {"source": {"name": "LAST_DAY_IN_QUARTER", "type": "String"}, "sink": {"name": "LAST_DAY_IN_QUARTER", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}, {"source": {"name": "LAST_DAY_IN_YEAR", "type": "String"}, "sink": {"name": "LAST_DAY_IN_YEAR", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}, {"source": {"name": "MONTH_NAME", "type": "String"}, "sink": {"name": "MONTH_NAME", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}, {"source": {"name": "MONTH_NAME_LONG", "type": "String"}, "sink": {"name": "MONTH_NAME_LONG", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}, {"source": {"name": "MONTH_NUMBER", "type": "String"}, "sink": {"name": "MONTH_NUMBER", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}, {"source": {"name": "QUARTER_NUMBER", "type": "String"}, "sink": {"name": "QUARTER_NUMBER", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}, {"source": {"name": "SAME_DAY_YEAR_AGO", "type": "DateTime"}, "sink": {"name": "SAME_DAY_YEAR_AGO", "type": "DateTime", "physicalType": "date"}}, {"source": {"name": "YEAR_MONTH", "type": "String"}, "sink": {"name": "YEAR_MONTH", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}, {"source": {"name": "YEAR_MONTH_PREVIOUSLY", "type": "String"}, "sink": {"name": "YEAR_MONTH_PREVIOUSLY", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}, {"source": {"name": "YEAR_NUMBER", "type": "String"}, "sink": {"name": "YEAR_NUMBER", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}, {"source": {"name": "YEAR_QUARTER", "type": "String"}, "sink": {"name": "YEAR_QUARTER", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}, {"source": {"name": "YEAR_QUARTER_PREVIOUSLY", "type": "String"}, "sink": {"name": "YEAR_QUARTER_PREVIOUSLY", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}, {"source": {"name": "XTSE_HOLIDAY_DATE", "type": "DateTime"}, "sink": {"name": "XTSE_HOLIDAY_DATE", "type": "DateTime", "physicalType": "date"}}, {"source": {"name": "XNYS_HOLIDAY_DATE", "type": "DateTime"}, "sink": {"name": "XNYS_HOLIDAY_DATE", "type": "DateTime", "physicalType": "date"}}, {"source": {"name": "PREVIOUS_BUSINESS_DAY_CAN", "type": "DateTime"}, "sink": {"name": "PREVIOUS_BUSINESS_DAY_CAN", "type": "DateTime", "physicalType": "date"}}, {"source": {"name": "ORIGINATING_SYSTEM_NO", "type": "Decimal"}, "sink": {"name": "ORIGINATING_SYSTEM_NO", "type": "Decimal", "physicalType": "decimal", "scale": 0, "precision": 14}}, {"source": {"name": "CURRENT_VERSION", "type": "String"}, "sink": {"name": "CURRENT_VERSION", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}, {"source": {"name": "VALID_FROM_DATE", "type": "DateTime"}, "sink": {"name": "VALID_FROM_DATE", "type": "DateTime", "physicalType": "date"}}, {"source": {"name": "VALID_TO_DATE", "type": "DateTime"}, "sink": {"name": "VALID_TO_DATE", "type": "DateTime", "physicalType": "date"}}, {"source": {"name": "NEXT_BUSINESS_DAY_CAN", "type": "DateTime"}, "sink": {"name": "NEXT_BUSINESS_DAY_CAN", "type": "DateTime", "physicalType": "date"}}], "typeConversion": true, "typeConversionSettings": {"allowDataTruncation": true, "treatBooleanAsNumber": false}}}, "inputs": [{"referenceName": "IMW_SCD_Oracle_UAT", "type": "DatasetReference", "parameters": {"Prefix": "SCDWH", "Table": "DM_CALENDAR"}}], "outputs": [{"referenceName": "DM_CALENDAR", "type": "DatasetReference"}]}, {"name": "DM_CALENDAR Row Count", "type": "Copy", "dependsOn": [{"activity": "DM_CALENDAR to MI", "dependencyConditions": ["Succeeded"]}], "policy": {"timeout": "7.00:00:00", "retry": 0, "retryIntervalInSeconds": 30, "secureOutput": false, "secureInput": false}, "userProperties": [], "typeProperties": {"source": {"type": "OracleSource", "oracleReaderQuery": "select NULL AS ProcessTS,'DM_CALENDAR' AS TableName,\nCOUNT(DWH_EXTRACT_PROCESS_IK) AS SourceCount,\nNULL AS TargetCount,\nNULL AS SourceSUM ,\nNULL as TargetSUM,\nNULL AS Status\nfrom SCDWH.DM_CALENDAR", "partitionOption": "None", "queryTimeout": "02:00:00"}, "sink": {"type": "SqlMISink", "writeBehavior": "insert", "sqlWriterUseTableLock": false}, "enableStaging": false, "translator": {"type": "TabularTranslator", "mappings": [{"source": {"name": "PROCESSTS", "type": "String"}, "sink": {"name": "ProcessTS", "type": "DateTime", "physicalType": "datetime"}}, {"source": {"name": "TABLENAME", "type": "String"}, "sink": {"name": "TableName", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}, {"source": {"name": "SOURCECOUNT", "type": "Double"}, "sink": {"name": "SourceCount", "type": "Int32", "physicalType": "int"}}, {"source": {"name": "TARGETCOUNT", "type": "String"}, "sink": {"name": "TargetCount", "type": "Int32", "physicalType": "int"}}, {"source": {"name": "SOURCESUM", "type": "String"}, "sink": {"name": "SourceSUM", "type": "Double", "physicalType": "float"}}, {"source": {"name": "TARGETSUM", "type": "String"}, "sink": {"name": "TargetSUM", "type": "Double", "physicalType": "float"}}, {"source": {"name": "STATUS", "type": "String"}, "sink": {"name": "Status", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}], "typeConversion": true, "typeConversionSettings": {"allowDataTruncation": true, "treatBooleanAsNumber": false}}}, "inputs": [{"referenceName": "IMW_SCD_Oracle_UAT", "type": "DatasetReference", "parameters": {"Prefix": "SCDWH", "Table": "DM_CALENDAR"}}], "outputs": [{"referenceName": "ReconLog", "type": "DatasetReference"}]}, {"name": "Log Final Status DM_CALENDAR", "type": "Lookup", "dependsOn": [{"activity": "DM_CALENDAR Row Count", "dependencyConditions": ["Succeeded"]}], "policy": {"timeout": "7.00:00:00", "retry": 0, "retryIntervalInSeconds": 30, "secureOutput": false, "secureInput": false}, "userProperties": [], "typeProperties": {"source": {"type": "SqlMISource", "sqlReaderQuery": "update dbo.ReconLog\nSET ProcessTS = getdate(),\nTargetCount = (SELECT COUNT(*) FROM dbo.DM_CALENDAR ),\nTargetSUM = NULL\nwhere TableName = 'DM_CALENDAR '\nand Status IS NULL\n\nupdate dbo.ReconLog\nset Status = (case when SourceCount = TargetCount  then 'Success' Else 'Failed' END)\nwhere Status IS NULL and TableName = 'DM_CALENDAR'\n\nSELECT top 1 * FROM dbo.ReconLog ", "partitionOption": "None"}, "dataset": {"referenceName": "DM_CALENDAR", "type": "DatasetReference"}}}]}}, {"name": "8 DM_BLENDED_INDEX_COMPONENTS to MI", "type": "IfCondition", "dependsOn": [], "userProperties": [], "typeProperties": {"expression": {"value": "@equals(pipeline().parameters.Load_DM_BLENDED_INDEX_COMPONENTS,'1')", "type": "Expression"}, "ifTrueActivities": [{"name": "DM_BLENDED_INDEX_COMPONENTS to MI", "type": "Copy", "dependsOn": [], "policy": {"timeout": "7.00:00:00", "retry": 0, "retryIntervalInSeconds": 30, "secureOutput": false, "secureInput": false}, "userProperties": [], "typeProperties": {"source": {"type": "OracleSource", "partitionOption": "None", "queryTimeout": "02:00:00"}, "sink": {"type": "SqlMISink", "preCopyScript": "delete from dbo.DM_BLENDED_INDEX_COMPONENTS", "writeBehavior": "insert", "sqlWriterUseTableLock": false}, "enableStaging": false, "translator": {"type": "TabularTranslator", "mappings": [{"source": {"name": "DWH_EXTRACT_PROCESS_IK", "type": "Decimal"}, "sink": {"name": "DWH_EXTRACT_PROCESS_IK", "type": "Int32"}}, {"source": {"name": "DWH_LOAD_TS", "type": "DateTime"}, "sink": {"name": "DWH_LOAD_TS", "type": "DateTime"}}, {"source": {"name": "LAST_CHANGE_TS", "type": "DateTime"}, "sink": {"name": "LAST_CHANGE_TS", "type": "DateTime"}}, {"source": {"name": "BLENDED_IX_COMPONENTS_KEY", "type": "Decimal"}, "sink": {"name": "BLENDED_IX_COMPONENTS_KEY", "type": "Decimal"}}, {"source": {"name": "BENCHMARK_KEY", "type": "Decimal"}, "sink": {"name": "BENCHMARK_KEY", "type": "Decimal"}}, {"source": {"name": "BENCHMARK_IK", "type": "Decimal"}, "sink": {"name": "BENCHMARK_IK", "type": "Int32"}}, {"source": {"name": "BENCHMARK", "type": "String"}, "sink": {"name": "BENCHMARK", "type": "String"}}, {"source": {"name": "BENCHMARK_NAME", "type": "String"}, "sink": {"name": "BENCHMARK_NAME", "type": "String"}}, {"source": {"name": "FROM_DATE", "type": "DateTime"}, "sink": {"name": "FROM_DATE", "type": "DateTime"}}, {"source": {"name": "SUB_INDEX_KEY", "type": "Decimal"}, "sink": {"name": "SUB_INDEX_KEY", "type": "Decimal"}}, {"source": {"name": "SUB_INDEX_IK", "type": "Decimal"}, "sink": {"name": "SUB_INDEX_IK", "type": "Int32"}}, {"source": {"name": "SUB_INDEX", "type": "String"}, "sink": {"name": "SUB_INDEX", "type": "String"}}, {"source": {"name": "SUB_INDEX_NAME", "type": "String"}, "sink": {"name": "SUB_INDEX_NAME", "type": "String"}}, {"source": {"name": "INDEX_WEIGHT", "type": "Decimal"}, "sink": {"name": "INDEX_WEIGHT", "type": "Decimal"}}, {"source": {"name": "REBAL_FIRST_DATE", "type": "DateTime"}, "sink": {"name": "REBAL_FIRST_DATE", "type": "DateTime"}}, {"source": {"name": "REBAL_FREQ", "type": "Decimal"}, "sink": {"name": "REBAL_FREQ", "type": "Int32"}}, {"source": {"name": "REBAL_METHOD", "type": "String"}, "sink": {"name": "REBAL_METHOD", "type": "String"}}, {"source": {"name": "ORIGINATING_SYSTEM_NO", "type": "Decimal"}, "sink": {"name": "ORIGINATING_SYSTEM_NO", "type": "Decimal"}}, {"source": {"name": "VALID_FROM_DATE", "type": "DateTime"}, "sink": {"name": "VALID_FROM_DATE", "type": "DateTime"}}, {"source": {"name": "VALID_TO_DATE", "type": "DateTime"}, "sink": {"name": "VALID_TO_DATE", "type": "DateTime"}}]}}, "inputs": [{"referenceName": "IMW_SCD_Oracle_UAT", "type": "DatasetReference", "parameters": {"Prefix": "SCDWH", "Table": "DM_BLENDED_INDEX_COMPONENTS"}}], "outputs": [{"referenceName": "DM_BLENDED_INDEX_COMPONENTS", "type": "DatasetReference"}]}, {"name": "DM_BLENDED_INDEX_COMPONENTS Row Count", "type": "Copy", "dependsOn": [{"activity": "DM_BLENDED_INDEX_COMPONENTS to MI", "dependencyConditions": ["Succeeded"]}], "policy": {"timeout": "7.00:00:00", "retry": 0, "retryIntervalInSeconds": 30, "secureOutput": false, "secureInput": false}, "userProperties": [], "typeProperties": {"source": {"type": "OracleSource", "oracleReaderQuery": "select NULL AS ProcessTS,'DM_BLENDED_INDEX_COMPONENTS' AS TableName,\nCOUNT(DWH_EXTRACT_PROCESS_IK) AS SourceCount,\nNULL AS TargetCount,\nNULL AS SourceSUM ,\nNULL as TargetSUM,\nNULL AS Status\nfrom SCDWH.DM_BLENDED_INDEX_COMPONENTS", "partitionOption": "None", "queryTimeout": "02:00:00"}, "sink": {"type": "SqlMISink", "writeBehavior": "insert", "sqlWriterUseTableLock": false}, "enableStaging": false, "translator": {"type": "TabularTranslator", "mappings": [{"source": {"name": "PROCESSTS", "type": "String"}, "sink": {"name": "ProcessTS", "type": "DateTime", "physicalType": "datetime"}}, {"source": {"name": "TABLENAME", "type": "String"}, "sink": {"name": "TableName", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}, {"source": {"name": "SOURCECOUNT", "type": "Double"}, "sink": {"name": "SourceCount", "type": "Int32", "physicalType": "int"}}, {"source": {"name": "TARGETCOUNT", "type": "String"}, "sink": {"name": "TargetCount", "type": "Int32", "physicalType": "int"}}, {"source": {"name": "SOURCESUM", "type": "String"}, "sink": {"name": "SourceSUM", "type": "Double", "physicalType": "float"}}, {"source": {"name": "TARGETSUM", "type": "String"}, "sink": {"name": "TargetSUM", "type": "Double", "physicalType": "float"}}, {"source": {"name": "STATUS", "type": "String"}, "sink": {"name": "Status", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}], "typeConversion": true, "typeConversionSettings": {"allowDataTruncation": true, "treatBooleanAsNumber": false}}}, "inputs": [{"referenceName": "IMW_SCD_Oracle_UAT", "type": "DatasetReference", "parameters": {"Prefix": "SCDWH", "Table": "DM_BLENDED_INDEX_COMPONENTS"}}], "outputs": [{"referenceName": "ReconLog", "type": "DatasetReference"}]}, {"name": "Log Final Status DM_BLENDED_INDEX_COMPONENTS", "type": "Lookup", "dependsOn": [{"activity": "DM_BLENDED_INDEX_COMPONENTS Row Count", "dependencyConditions": ["Succeeded"]}], "policy": {"timeout": "7.00:00:00", "retry": 0, "retryIntervalInSeconds": 30, "secureOutput": false, "secureInput": false}, "userProperties": [], "typeProperties": {"source": {"type": "SqlMISource", "sqlReaderQuery": "update dbo.ReconLog\nSET ProcessTS = getdate(),\nTargetCount = (SELECT COUNT(*) FROM dbo.DM_BLENDED_INDEX_COMPONENTS),\nTargetSUM = NULL\nwhere TableName = 'DM_BLENDED_INDEX_COMPONENTS'\nand Status IS NULL\n\nupdate dbo.ReconLog\nset Status = (case when SourceCount = TargetCount  then 'Success' Else 'Failed' END)\nwhere Status IS NULL and TableName = 'DM_BLENDED_INDEX_COMPONENTS'\n\nSELECT top 1 * FROM dbo.ReconLog ", "partitionOption": "None"}, "dataset": {"referenceName": "DM_BLENDED_INDEX_COMPONENTS", "type": "DatasetReference"}}}]}}, {"name": "9 DM_CURRENCY_CROSS to MI", "type": "IfCondition", "dependsOn": [], "userProperties": [], "typeProperties": {"expression": {"value": "@equals(pipeline().parameters.Load_DM_CURRENCY_CROSS,'1')", "type": "Expression"}, "ifTrueActivities": [{"name": "DM_CURRENCY_CROSS to MI", "type": "Copy", "dependsOn": [], "policy": {"timeout": "7.00:00:00", "retry": 0, "retryIntervalInSeconds": 30, "secureOutput": false, "secureInput": false}, "userProperties": [], "typeProperties": {"source": {"type": "OracleSource", "partitionOption": "None", "queryTimeout": "02:00:00"}, "sink": {"type": "SqlMISink", "preCopyScript": "delete from dbo.DM_CURRENCY_CROSS", "writeBehavior": "insert", "sqlWriterUseTableLock": false}, "enableStaging": false, "translator": {"type": "TabularTranslator", "mappings": [{"source": {"name": "DWH_EXTRACT_PROCESS_IK", "type": "Decimal"}, "sink": {"name": "DWH_EXTRACT_PROCESS_IK", "type": "Decimal"}}, {"source": {"name": "DWH_LOAD_TS", "type": "DateTime"}, "sink": {"name": "DWH_LOAD_TS", "type": "DateTime"}}, {"source": {"name": "LAST_CHANGE_TS", "type": "DateTime"}, "sink": {"name": "LAST_CHANGE_TS", "type": "DateTime"}}, {"source": {"name": "CURRENCY_CROSS_KEY", "type": "Decimal"}, "sink": {"name": "CURRENCY_CROSS_KEY", "type": "Decimal"}}, {"source": {"name": "CURRENCY_CROSS_IK", "type": "Decimal"}, "sink": {"name": "CURRENCY_CROSS_IK", "type": "Decimal"}}, {"source": {"name": "CURRENCY_CROSS", "type": "String"}, "sink": {"name": "CURRENCY_CROSS", "type": "String"}}, {"source": {"name": "BASE_CURRENCY_IK", "type": "Decimal"}, "sink": {"name": "BASE_CURRENCY_IK", "type": "Decimal"}}, {"source": {"name": "BASE_CURRENCY", "type": "String"}, "sink": {"name": "BASE_CURRENCY", "type": "String"}}, {"source": {"name": "PRICE_CURRENCY_IK", "type": "Decimal"}, "sink": {"name": "PRICE_CURRENCY_IK", "type": "Decimal"}}, {"source": {"name": "PRICE_CURRENCY", "type": "String"}, "sink": {"name": "PRICE_CURRENCY", "type": "String"}}, {"source": {"name": "INVERTED", "type": "String"}, "sink": {"name": "INVERTED", "type": "String"}}, {"source": {"name": "ORIGINATING_SYSTEM_NO", "type": "Decimal"}, "sink": {"name": "ORIGINATING_SYSTEM_NO", "type": "Decimal"}}, {"source": {"name": "CURRENT_VERSION", "type": "String"}, "sink": {"name": "CURRENT_VERSION", "type": "String"}}, {"source": {"name": "VALID_FROM_DATE", "type": "DateTime"}, "sink": {"name": "VALID_FROM_DATE", "type": "DateTime"}}, {"source": {"name": "VALID_TO_DATE", "type": "DateTime"}, "sink": {"name": "VALID_TO_DATE", "type": "DateTime"}}]}}, "inputs": [{"referenceName": "IMW_SCD_Oracle_UAT", "type": "DatasetReference", "parameters": {"Prefix": "SCDWH", "Table": "DM_CURRENCY_CROSS"}}], "outputs": [{"referenceName": "DM_CURRENCY_CROSS", "type": "DatasetReference"}]}, {"name": "DM_CURRENCY_CROSS Row Count", "type": "Copy", "dependsOn": [{"activity": "DM_CURRENCY_CROSS to MI", "dependencyConditions": ["Succeeded"]}], "policy": {"timeout": "7.00:00:00", "retry": 0, "retryIntervalInSeconds": 30, "secureOutput": false, "secureInput": false}, "userProperties": [], "typeProperties": {"source": {"type": "OracleSource", "oracleReaderQuery": "select NULL AS ProcessTS,'DM_CURRENCY_CROSS' AS TableName,\nCOUNT(DWH_EXTRACT_PROCESS_IK) AS SourceCount,\nNULL AS TargetCount,\nNULL AS SourceSUM ,\nNULL as TargetSUM,\nNULL AS Status\nfrom SCDWH.DM_CURRENCY_CROSS", "partitionOption": "None", "queryTimeout": "02:00:00"}, "sink": {"type": "SqlMISink", "writeBehavior": "insert", "sqlWriterUseTableLock": false}, "enableStaging": false, "translator": {"type": "TabularTranslator", "mappings": [{"source": {"name": "PROCESSTS", "type": "String"}, "sink": {"name": "ProcessTS", "type": "DateTime", "physicalType": "datetime"}}, {"source": {"name": "TABLENAME", "type": "String"}, "sink": {"name": "TableName", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}, {"source": {"name": "SOURCECOUNT", "type": "Double"}, "sink": {"name": "SourceCount", "type": "Int32", "physicalType": "int"}}, {"source": {"name": "TARGETCOUNT", "type": "String"}, "sink": {"name": "TargetCount", "type": "Int32", "physicalType": "int"}}, {"source": {"name": "SOURCESUM", "type": "String"}, "sink": {"name": "SourceSUM", "type": "Double", "physicalType": "float"}}, {"source": {"name": "TARGETSUM", "type": "String"}, "sink": {"name": "TargetSUM", "type": "Double", "physicalType": "float"}}, {"source": {"name": "STATUS", "type": "String"}, "sink": {"name": "Status", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}], "typeConversion": true, "typeConversionSettings": {"allowDataTruncation": true, "treatBooleanAsNumber": false}}}, "inputs": [{"referenceName": "IMW_SCD_Oracle_UAT", "type": "DatasetReference", "parameters": {"Prefix": "SCDWH", "Table": "DM_CURRENCY_CROSS"}}], "outputs": [{"referenceName": "ReconLog", "type": "DatasetReference"}]}, {"name": "Log Final Status DM_CURRENCY_CROSS", "type": "Lookup", "dependsOn": [{"activity": "DM_CURRENCY_CROSS Row Count", "dependencyConditions": ["Succeeded"]}], "policy": {"timeout": "7.00:00:00", "retry": 0, "retryIntervalInSeconds": 30, "secureOutput": false, "secureInput": false}, "userProperties": [], "typeProperties": {"source": {"type": "SqlMISource", "sqlReaderQuery": "update dbo.ReconLog\nSET ProcessTS = getdate(),\nTargetCount = (SELECT COUNT(*) FROM dbo.DM_CURRENCY_CROSS),\nTargetSUM = NULL\nwhere TableName = 'DM_CURRENCY_CROSS'\nand Status IS NULL\n\nupdate dbo.ReconLog\nset Status = (case when SourceCount = TargetCount  then 'Success' Else 'Failed' END)\nwhere Status IS NULL and TableName = 'DM_CURRENCY_CROSS'\n\nSELECT top 1 * FROM dbo.ReconLog ", "partitionOption": "None"}, "dataset": {"referenceName": "DM_CURRENCY_CROSS", "type": "DatasetReference"}}}]}}, {"name": "10 DM_OPEN_PAYMENTS to MI", "type": "IfCondition", "dependsOn": [], "userProperties": [], "typeProperties": {"expression": {"value": "@equals(pipeline().parameters.Load_DM_OPEN_PAYMENTS,'1')", "type": "Expression"}, "ifTrueActivities": [{"name": "DM_OPEN_PAYMENTS to MI", "type": "Copy", "dependsOn": [], "policy": {"timeout": "7.00:00:00", "retry": 0, "retryIntervalInSeconds": 30, "secureOutput": false, "secureInput": false}, "userProperties": [], "typeProperties": {"source": {"type": "OracleSource", "partitionOption": "None", "queryTimeout": "02:00:00"}, "sink": {"type": "SqlMISink", "preCopyScript": "delete from dbo.DM_OPEN_PAYMENTS", "writeBehavior": "insert", "sqlWriterUseTableLock": false}, "enableStaging": false, "translator": {"type": "TabularTranslator", "mappings": [{"source": {"name": "DWH_EXTRACT_PROCESS_IK", "type": "Decimal"}, "sink": {"name": "DWH_EXTRACT_PROCESS_IK", "type": "Decimal"}}, {"source": {"name": "DWH_LOAD_TS", "type": "DateTime"}, "sink": {"name": "DWH_LOAD_TS", "type": "DateTime"}}, {"source": {"name": "LAST_CHANGE_TS", "type": "DateTime"}, "sink": {"name": "LAST_CHANGE_TS", "type": "DateTime"}}, {"source": {"name": "VALUATION_DATE_FULL", "type": "DateTime"}, "sink": {"name": "VALUATION_DATE_FULL", "type": "DateTime"}}, {"source": {"name": "PFC_OPEN_PAYMENTS_KEY", "type": "Decimal"}, "sink": {"name": "PFC_OPEN_PAYMENTS_KEY", "type": "Decimal"}}, {"source": {"name": "SETTLEMENT_CURRENCY", "type": "String"}, "sink": {"name": "SETTLEMENT_CURRENCY", "type": "String"}}, {"source": {"name": "TRANSACTION_NO", "type": "Decimal"}, "sink": {"name": "TRANSACTION_NO", "type": "Decimal"}}, {"source": {"name": "TRANSACTION_IK", "type": "Decimal"}, "sink": {"name": "TRANSACTION_IK", "type": "Decimal"}}, {"source": {"name": "PORTFOLIO_IK", "type": "Decimal"}, "sink": {"name": "PORTFOLIO_IK", "type": "Decimal"}}, {"source": {"name": "SECURITY_ID", "type": "String"}, "sink": {"name": "SECURITY_ID", "type": "String"}}, {"source": {"name": "REPORTING_ASSET_CLASS", "type": "String"}, "sink": {"name": "REPORTING_ASSET_CLASS", "type": "String"}}, {"source": {"name": "BANK_ACCOUNT", "type": "String"}, "sink": {"name": "BANK_ACCOUNT", "type": "String"}}, {"source": {"name": "PMT_AMT_PC", "type": "Decimal"}, "sink": {"name": "PMT_AMT_PC", "type": "Decimal"}}, {"source": {"name": "PMT_AMT_QC", "type": "Decimal"}, "sink": {"name": "PMT_AMT_QC", "type": "Decimal"}}, {"source": {"name": "QUOTATION_CURRENCY", "type": "String"}, "sink": {"name": "QUOTATION_CURRENCY", "type": "String"}}, {"source": {"name": "PMT_AMT_SC", "type": "Decimal"}, "sink": {"name": "PMT_AMT_SC", "type": "Decimal"}}, {"source": {"name": "PMT_DATE", "type": "DateTime"}, "sink": {"name": "PMT_DATE", "type": "DateTime"}}, {"source": {"name": "OVERDUE", "type": "Decimal"}, "sink": {"name": "OVERDUE", "type": "Decimal"}}, {"source": {"name": "PENDING", "type": "Decimal"}, "sink": {"name": "PENDING", "type": "Decimal"}}, {"source": {"name": "PAYABLE", "type": "Decimal"}, "sink": {"name": "PAYABLE", "type": "Decimal"}}, {"source": {"name": "BUSINESS_TRANS_CODE", "type": "String"}, "sink": {"name": "BUSINESS_TRANS_CODE", "type": "String"}}, {"source": {"name": "ELEMENTARY_TRANS_CODE", "type": "String"}, "sink": {"name": "ELEMENTARY_TRANS_CODE", "type": "String"}}, {"source": {"name": "TRANSACTION_DATE_TYPE", "type": "String"}, "sink": {"name": "TRANSACTION_DATE_TYPE", "type": "String"}}, {"source": {"name": "PAY_RECEIVE_BUCKET", "type": "String"}, "sink": {"name": "PAY_RECEIVE_BUCKET", "type": "String"}}, {"source": {"name": "TRANSACTION_CANCELLATION_FLAG", "type": "String"}, "sink": {"name": "TRANSACTION_CANCELLATION_FLAG", "type": "String"}}, {"source": {"name": "ORIGINATING_SYSTEM_NO", "type": "Decimal"}, "sink": {"name": "ORIGINATING_SYSTEM_NO", "type": "Decimal"}}, {"source": {"name": "SECURITY_IK", "type": "Decimal"}, "sink": {"name": "SECURITY_IK", "type": "Decimal"}}, {"source": {"name": "UNR_FX_PL_PC", "type": "Decimal"}, "sink": {"name": "UNR_FX_PL_PC", "type": "Decimal"}}, {"source": {"name": "EARLY_UNPAID_AMT_PC", "type": "Decimal"}, "sink": {"name": "EARLY_UNPAID_AMT_PC", "type": "Decimal"}}, {"source": {"name": "EARLY_UNPAID_AMT_QC", "type": "Decimal"}, "sink": {"name": "EARLY_UNPAID_AMT_QC", "type": "Decimal"}}, {"source": {"name": "EARLY_UNPAID_AMT_SC", "type": "Decimal"}, "sink": {"name": "EARLY_UNPAID_AMT_SC", "type": "Decimal"}}, {"source": {"name": "UNR_FX_PL_RC", "type": "Decimal"}, "sink": {"name": "UNR_FX_PL_RC", "type": "Decimal"}}, {"source": {"name": "EARLY_UNPAID_AMT_RC", "type": "Decimal"}, "sink": {"name": "EARLY_UNPAID_AMT_RC", "type": "Decimal"}}, {"source": {"name": "PMT_AMT_RC", "type": "Decimal"}, "sink": {"name": "PMT_AMT_RC", "type": "Decimal"}}]}}, "inputs": [{"referenceName": "IMW_SCD_Oracle_UAT", "type": "DatasetReference", "parameters": {"Prefix": "SCDWH", "Table": "DM_OPEN_PAYMENTS"}}], "outputs": [{"referenceName": "DM_OPEN_PAYMENTS", "type": "DatasetReference"}]}, {"name": "DM_OPEN_PAYMENTS Row Count", "type": "Copy", "dependsOn": [{"activity": "DM_OPEN_PAYMENTS to MI", "dependencyConditions": ["Succeeded"]}], "policy": {"timeout": "7.00:00:00", "retry": 0, "retryIntervalInSeconds": 30, "secureOutput": false, "secureInput": false}, "userProperties": [], "typeProperties": {"source": {"type": "OracleSource", "oracleReaderQuery": "select NULL AS ProcessTS,'DM_OPEN_PAYMENTS' AS TableName,\nCOUNT(DWH_EXTRACT_PROCESS_IK) AS SourceCount,\nNULL AS TargetCount,\nNULL AS SourceSUM ,\nNULL as TargetSUM,\nNULL AS Status\nfrom SCDWH.DM_OPEN_PAYMENTS", "partitionOption": "None", "queryTimeout": "02:00:00"}, "sink": {"type": "SqlMISink", "writeBehavior": "insert", "sqlWriterUseTableLock": false}, "enableStaging": false, "translator": {"type": "TabularTranslator", "mappings": [{"source": {"name": "PROCESSTS", "type": "String"}, "sink": {"name": "ProcessTS", "type": "DateTime", "physicalType": "datetime"}}, {"source": {"name": "TABLENAME", "type": "String"}, "sink": {"name": "TableName", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}, {"source": {"name": "SOURCECOUNT", "type": "Double"}, "sink": {"name": "SourceCount", "type": "Int32", "physicalType": "int"}}, {"source": {"name": "TARGETCOUNT", "type": "String"}, "sink": {"name": "TargetCount", "type": "Int32", "physicalType": "int"}}, {"source": {"name": "SOURCESUM", "type": "String"}, "sink": {"name": "SourceSUM", "type": "Double", "physicalType": "float"}}, {"source": {"name": "TARGETSUM", "type": "String"}, "sink": {"name": "TargetSUM", "type": "Double", "physicalType": "float"}}, {"source": {"name": "STATUS", "type": "String"}, "sink": {"name": "Status", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}], "typeConversion": true, "typeConversionSettings": {"allowDataTruncation": true, "treatBooleanAsNumber": false}}}, "inputs": [{"referenceName": "IMW_SCD_Oracle_UAT", "type": "DatasetReference", "parameters": {"Prefix": "SCDWH", "Table": "DM_OPEN_PAYMENTS"}}], "outputs": [{"referenceName": "ReconLog", "type": "DatasetReference"}]}, {"name": "Log Final Status DM_OPEN_PAYMENTS", "type": "Lookup", "dependsOn": [{"activity": "DM_OPEN_PAYMENTS Row Count", "dependencyConditions": ["Succeeded"]}], "policy": {"timeout": "7.00:00:00", "retry": 0, "retryIntervalInSeconds": 30, "secureOutput": false, "secureInput": false}, "userProperties": [], "typeProperties": {"source": {"type": "SqlMISource", "sqlReaderQuery": "update dbo.ReconLog\nSET ProcessTS = getdate(),\nTargetCount = (SELECT COUNT(*) FROM dbo.DM_OPEN_PAYMENTS),\nTargetSUM = NULL\nwhere TableName = 'DM_OPEN_PAYMENTS'\nand Status IS NULL\n\nupdate dbo.ReconLog\nset Status = (case when SourceCount = TargetCount  then 'Success' Else 'Failed' END)\nwhere Status IS NULL and TableName = 'DM_OPEN_PAYMENTS'\n\nSELECT top 1 * FROM dbo.ReconLog ", "partitionOption": "None"}, "dataset": {"referenceName": "DM_OPEN_PAYMENTS", "type": "DatasetReference"}}}]}}], "parameters": {"Load_DM_FUND": {"type": "string", "defaultValue": "1"}, "Load_DM_FUND_CERTIFICATE": {"type": "string", "defaultValue": "1"}, "Load_DM_PORTFOLIO": {"type": "string", "defaultValue": "1"}, "Load_DM_PORTFOLIO_GROUP": {"type": "string", "defaultValue": "1"}, "Load_DM_COUNTRY": {"type": "string", "defaultValue": "1"}, "Load_DM_SECURITY": {"type": "string", "defaultValue": "1"}, "Load_DM_CALENDAR": {"type": "string", "defaultValue": "1"}, "Load_DM_BLENDED_INDEX_COMPONENTS": {"type": "string", "defaultValue": "1"}, "Load_DM_CURRENCY_CROSS": {"type": "string", "defaultValue": "1"}, "Load_DM_OPEN_PAYMENTS": {"type": "string", "defaultValue": "0"}, "Load_DM_TRANSACTIONS_COSTS": {"type": "string", "defaultValue": "1"}}, "folder": {"name": "Internal/IMW/IWM-MI CS CP/CompleteLoad"}, "annotations": [], "lastPublishTime": "2023-12-01T04:11:50Z"}, "type": "Microsoft.DataFactory/factories/pipelines"}