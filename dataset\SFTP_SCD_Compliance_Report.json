{"name": "SFTP_SCD_Compliance_Report", "properties": {"linkedServiceName": {"referenceName": "SCD_SFTP_Prod", "type": "LinkedServiceReference"}, "parameters": {"FilePath": {"type": "string"}, "FileName": {"type": "string"}}, "folder": {"name": "Outbound/Compliance Post Trade Workflow"}, "annotations": [], "type": "DelimitedText", "typeProperties": {"location": {"type": "SftpLocation", "fileName": {"value": "@dataset().FileName", "type": "Expression"}, "folderPath": {"value": "@dataset().FilePath", "type": "Expression"}}, "columnDelimiter": "\t", "escapeChar": "\\", "firstRowAsHeader": true, "quoteChar": ""}, "schema": [{"name": "Cash management setup\tReport date\tPortfolio\tPortfolio name\tPortfolio currency\tBank account ID\tBank currency\tMoney Market Positions QC\tTD\tTD+1BD\tTD+2BD\tTD+3BD\tPending money market orders\tPortfolio manager\tPortfolio manager associate", "type": "String"}]}, "type": "Microsoft.DataFactory/factories/datasets"}