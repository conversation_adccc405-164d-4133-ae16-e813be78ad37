{"name": "IN-729 SIS Report Ballot Level Detail_DF", "properties": {"folder": {"name": "Inbound"}, "type": "MappingDataFlow", "typeProperties": {"sources": [{"dataset": {"referenceName": "AzureDataLakeStorage_SISDF", "type": "DatasetReference"}, "name": "TempFile"}], "sinks": [{"dataset": {"referenceName": "SISBallot_SINK", "type": "DatasetReference"}, "name": "sink1", "description": "Add sink dataset\n"}], "transformations": [{"name": "MapDriftedColumn"}, {"name": "derivedColumn"}, {"name": "FinalSelection"}, {"name": "FilterValidRows"}], "scriptLines": ["source(allowSchemaDrift: true,", "     validateSchema: false,", "     ignoreNoFilesFound: false,", "     wildcardPaths:[(concat(\"SIS Report Ballot Level Detail/Temp/*.XLSX\")\r", "        )]) ~> TempFile", "TempFile derive({Location Name} = toString(byName('Location Name')),", "          {Location ID} = toString(byName('Location ID')),", "          {Meeting ID} = toString(byName('Meeting ID')),", "          {Ballot ID} = toString(byName('Ballot ID')),", "          Company = toString(byName('Company')),", "          {Meeting Date} = toString(byName('Meeting Date')),", "          {Meeting Type} = toString(byName('Meeting Type')),", "          Country = toString(by<PERSON><PERSON>('Country')),", "          {Institutional Account Name} = toString(by<PERSON><PERSON>('Institutional Account Name')),", "          Custodian = toString(byName('Custodian')),", "          {Custodian Account Name} = toString(by<PERSON><PERSON>('Custodian Account Name')),", "          {Custodian Account Number} = toString(byName('Custodian Account Number')),", "          {Proposal Category} = toString(byName('Proposal Category')),", "          {Proposal Code} = toString(byName('Proposal Code')),", "          {Proposal Code Description} = toString(byName('Proposal Code Description')),", "          {Item Number} = toString(byName('Item Number')),", "          Proposal = toString(byName('Proposal')),", "          {Management Recommendation} = toString(byName('Management Recommendation')),", "          {Vote Cast} = toString(by<PERSON>ame('Vote Cast')),", "          {Vote For} = toString(byName('Vote For')),", "          {Vote Against} = toString(byName('Vote Against')),", "          {Vote Abstain} = toString(byName('Vote Abstain')),", "          {Vote Withhold} = toString(byName('Vote Withhold')),", "          DNV = toString(byName('DNV')),", "          {One Year} = toString(byName('One Year')),", "          {Two Years} = toString(byName('Two Years')),", "          {Three Years} = toString(byName('Three Years')),", "          {Vote None} = toString(byName('Vote None')),", "          {Vote Against Management} = toString(byName('Vote Against Management')),", "          {Vote With Management} = toString(byName('Vote With Management')),", "          {Vote Against ISS} = toString(byName('Vote Against ISS')),", "          {Vote with ISS} = toString(byName('Vote with ISS')),", "          {Vote Against Policy} = toString(byName('Vote Against Policy')),", "          {Vote With Policy} = toString(byName('Vote With Policy'))) ~> MapDriftedColumn", "MapDriftedColumn derive({Load Date} = currentDate('yyyymmddhhmmss')) ~> derivedColumn", "derivedColumn select(mapColumn(", "          {Location Name},", "          {Location ID},", "          {Meeting ID},", "          {Ballot ID},", "          Company,", "          {Meeting Date},", "          {Meeting Type},", "          Country,", "          {Institutional Account Name},", "          <PERSON><PERSON><PERSON><PERSON>,", "          {<PERSON><PERSON>odian Account Name},", "          {Custodian Account Number},", "          {Proposal Category},", "          {Proposal Code},", "          {Proposal Code Description},", "          {Item Number},", "          Proposal,", "          {Management Recommendation},", "          {Vote Cast},", "          {Vote For},", "          {Vote Against},", "          {<PERSON>ote Abstain},", "          {Vote Withhold},", "          DNV,", "          {One Year},", "          {Two Years},", "          {Three Years},", "          {Vote None},", "          {Vote Against Management},", "          {Vote With Management},", "          {Vote Against ISS},", "          {Vote with ISS},", "          {Vote Against Policy},", "          {Vote With Policy},", "          {Load Date}", "     ),", "     skipDuplicateMapInputs: true,", "     skipDuplicateMapOutputs: true) ~> FinalSelection", "FinalSelection filter(equals({Location Name}, 'Mawer Investment Management')) ~> FilterValidRows", "FilterValidRows sink(allowSchemaDrift: true,", "     validateSchema: false,", "     partitionFileNames:[(concat('SISReportBallotFinal.csv'))],", "     umask: 0022,", "     preCommands: [],", "     postCommands: [],", "     skipDuplicateMapInputs: true,", "     skipDuplicateMapOutputs: true,", "     partitionBy('hash', 1)) ~> sink1"]}}}