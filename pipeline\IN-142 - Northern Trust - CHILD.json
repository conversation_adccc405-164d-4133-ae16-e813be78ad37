{"name": "IN-142 - Northern Trust - CHILD", "properties": {"activities": [{"name": "Copy from CSV to ADX", "type": "Copy", "dependsOn": [{"activity": "Clean ADX", "dependencyConditions": ["Succeeded"]}], "policy": {"timeout": "7.00:00:00", "retry": 0, "retryIntervalInSeconds": 30, "secureOutput": false, "secureInput": false}, "userProperties": [], "typeProperties": {"source": {"type": "DelimitedTextSource", "additionalColumns": [{"name": "CreatedDate", "value": {"value": "@concat(substring(pipeline().parameters.P_DATE,0,4),'-',\nsubstring(pipeline().parameters.P_DATE,4,2),'-',\nsubstring(pipeline().parameters.P_DATE,6,2),'T00:00:00Z')", "type": "Expression"}}], "storeSettings": {"type": "AzureBlobFSReadSettings", "recursive": true, "wildcardFileName": "Pending_FX_Detail_by_Currency.csv", "enablePartitionDiscovery": false}, "formatSettings": {"type": "DelimitedTextReadSettings"}}, "sink": {"type": "AzureDataExplorerSink"}, "enableStaging": false, "translator": {"type": "TabularTranslator", "mappings": [{"source": {"name": "CreatedDate", "type": "String"}, "sink": {"name": "CreatedDate", "type": "DateTime"}}, {"source": {"name": "Transaction origin Trade ID", "type": "String", "physicalType": "String"}, "sink": {"name": "Transaction origin Trade ID", "type": "String"}}, {"source": {"name": "Consolidation", "type": "String", "physicalType": "String"}, "sink": {"name": "Consolidation", "type": "String"}}, {"source": {"name": "Account Number", "type": "String", "physicalType": "String"}, "sink": {"name": "Account Number", "type": "String"}}, {"source": {"name": "From Date", "type": "String", "physicalType": "String"}, "sink": {"name": "From date", "type": "DateTime"}}, {"source": {"name": "Through date", "type": "String", "physicalType": "String"}, "sink": {"name": "Through date", "type": "DateTime"}}, {"source": {"name": "D-CNTRT-ENTR", "type": "String", "physicalType": "String"}, "sink": {"name": "D-CNTRT-ENTR", "type": "DateTime"}}, {"source": {"name": "D-CNTRT-SET-CASH", "type": "String", "physicalType": "String"}, "sink": {"name": "D-CNTRT-SET-CASH", "type": "DateTime"}}, {"source": {"name": "T-NARR-LONG", "type": "String", "physicalType": "String"}, "sink": {"name": "T-NARR-LONG", "type": "String"}}, {"source": {"name": "Buy Currency", "type": "String", "physicalType": "String"}, "sink": {"name": "Buy Currency", "type": "String"}}, {"source": {"name": "Sold Currency", "type": "String", "physicalType": "String"}, "sink": {"name": "Sold Currency", "type": "String"}}, {"source": {"name": "C-CURR", "type": "String", "physicalType": "String"}, "sink": {"name": "C-CURR", "type": "String"}}, {"source": {"name": "A-FX-BUY-SELL", "type": "String", "physicalType": "String"}, "sink": {"name": "A-FX-BUY-SELL", "type": "Double"}}, {"source": {"name": "A-MV-BSE", "type": "String", "physicalType": "String"}, "sink": {"name": "A-MV-BSE", "type": "Double"}}, {"source": {"name": "A-PURCH-SALE-BSE", "type": "String", "physicalType": "String"}, "sink": {"name": "A-PURCH-SALE-BSE", "type": "Double"}}, {"source": {"name": "A-EXCH-GNLS-BSE", "type": "String", "physicalType": "String"}, "sink": {"name": "A-EXCH-GNLS-BSE", "type": "Double"}}, {"source": {"name": "C-FX-TYPE", "type": "String", "physicalType": "String"}, "sink": {"name": "C-FX-TYPE", "type": "String"}}, {"source": {"name": "I-TRAN", "type": "String", "physicalType": "String"}, "sink": {"name": "I-TRAN", "type": "Int64"}}, {"source": {"name": "C-PURCH-SALE", "type": "String", "physicalType": "String"}, "sink": {"name": "C-PURCH-SALE", "type": "Int64"}}, {"source": {"name": "R-EXCH", "type": "String", "physicalType": "String"}, "sink": {"name": "R-EXCH", "type": "Double"}}, {"source": {"name": "C-VALN-TYPE-O", "type": "String", "physicalType": "String"}, "sink": {"name": "C-VALN-TYPE-O", "type": "String"}}, {"source": {"name": "Error code", "type": "String", "physicalType": "String"}, "sink": {"name": "Error code", "type": "String"}}, {"source": {"name": "Instrument Type", "type": "String", "physicalType": "String"}, "sink": {"name": "Instrument Type", "type": "String"}}, {"source": {"name": "Buy Amount"}, "sink": {"name": "Buy Amount"}}, {"source": {"name": "Sold Amount"}, "sink": {"name": "Sold Amount"}}], "typeConversion": true, "typeConversionSettings": {"allowDataTruncation": true, "treatBooleanAsNumber": false}}}, "inputs": [{"referenceName": "ADL_NorthernTrust_TEMP", "type": "DatasetReference"}], "outputs": [{"referenceName": "ADX_FX", "type": "DatasetReference", "parameters": {"Table": "NorthernTrust_PendingFXDetailByCurrency"}}]}, {"name": "Copy from ADX to Temp", "type": "Copy", "dependsOn": [{"activity": "Copy from CSV to ADX", "dependencyConditions": ["Succeeded"]}], "policy": {"timeout": "7.00:00:00", "retry": 0, "retryIntervalInSeconds": 30, "secureOutput": false, "secureInput": false}, "userProperties": [], "typeProperties": {"source": {"type": "AzureDataExplorerSource", "query": {"value": "@concat('NorthernTrust_PendingFXDetailByCurrency | where format_datetime(CreatedDate,''yyyyMMdd'')==\"',pipeline().parameters.P_DATE,'\"\n| project [\"Instrument Type\"]=[\"Instrument Type\"], [\"Transaction Cancellation Flag\"]=\"N\", [\"Block Trade ID\"]=[\"I-TRAN\"], \n[\"Transaction origin Trade ID\"]=[\"Transaction origin Trade ID\"], [\"Security ID\"]=\"FXS\", [\"Security ID Type\"]=\"SCD Sec ID\", [\"Portfolio\"]=[\"Account Number\"], \n[\"Model portfolio\"]=\"\", [\"Purpose\"]=\"\", [\"Counterparty\"]=\"NORT\", [\"Counterpartys dealer\"]=\"\", [\"Broker\"]=\"\", [\"Trans code\"]=\"BuyFXFwdDbd\", [\"Open close\"]=\"\", \n[\"Nominal\"]=[\"Buy Amount\"],\n[\"Price\"]=\"\", [\"Quotation\"]=\"\", [\"Exchange\"]=\"\", [\"Margin clearer\"]=\"\", [\"Trade Date\"]=[\"D-CNTRT-ENTR\"], [\"Payment date\"]=[\"D-CNTRT-SET-CASH\"], \n[\"Type of settlement\"]=\"\", [\"Trade time\"]=\"\", [\"Execution time\"]=\"\", [\"Clearing time\"]=\"\", [\"Confirmation time\"]=\"\", [\"Settlement comment\"]=\"\", [\"Instruction comment\"]=\"\", \n[\"Dealer\"]=\"\", [\"Blocking type\"]=\"\", [\"Blocked until\"]=\"\", [\"IFRS 9 purpose\"]=\"\", [\"Special holding mark\"]=\"\", [\"Client\"]=\"\", [\"Compound\"]=\"\", [\"Commission\"]=\"\", \n[\"Nominal currency\"]=[\"Buy Currency\"], \n[\"Nominal L2\"]=[\"Sold Amount\"], [\"Keep amount in base Ccy\"]=\"TRUE\", \n[\"Nominal L2 currency\"]=[\"Sold Currency\"]| extend fx_spot_calc = round(iif(todouble([\"Nominal\"]) > todouble([\"Nominal L2\"]), todouble([\"Nominal\"])/todouble([\"Nominal L2\"]), todouble([\"Nominal\"])/todouble([\"Nominal L2\"])), 9)\n| project [\"Instrument Type\"]=[\"Instrument Type\"], [\"Transaction Cancellation Flag\"]=\"N\", [\"Block Trade ID\"]=[\"Block Trade ID\"], \n[\"Transaction origin Trade ID\"]=[\"Transaction origin Trade ID\"], [\"Security ID\"]=\"FXS\", [\"Security ID Type\"]=\"SCD Sec ID\", [\"Portfolio\"]=[\"Portfolio\"], \n[\"Model portfolio\"]=\"CENTRAL\", [\"Purpose\"]=\"\", [\"Counterparty\"]=\"NORT\", [\"Counterpartys dealer\"]=\"\", [\"Broker\"]=\"\", [\"Trans code\"]=\"BuyFXFwdDbd\", [\"Open close\"]=\"\", \n[\"Nominal\"]=[\"Nominal\"],\n[\"Price\"]=\"\", [\"Quotation\"]=\"\", [\"Exchange\"]=\"\", [\"Margin clearer\"]=\"\", [\"Trade Date\"]=[\"Trade Date\"], [\"Payment date\"]=[\"Payment date\"], \n[\"Type of settlement\"]=\"\", [\"Trade time\"]=\"\", [\"Execution time\"]=\"\", [\"Clearing time\"]=\"\", [\"Confirmation time\"]=\"\", [\"Settlement comment\"]=\"\", [\"Instruction comment\"]=\"\", \n[\"Dealer\"]=\"\", [\"Blocking type\"]=\"\", [\"Blocked until\"]=\"\", [\"IFRS 9 purpose\"]=\"\", [\"Special holding mark\"]=\"\", [\"Client\"]=\"\", [\"Compound\"]=\"\", [\"Commission\"]=\"\", \n[\"Nominal currency\"]=[\"Nominal currency\"], \n[\"Nominal L2\"]=[\"Nominal L2\"], [\"Keep amount in base Ccy\"]=\"TRUE\", \n[\"Nominal L2 currency\"]=[\"Nominal L2 currency\"], \n[\"FX rate spot\"]=\"\", [\"FX tics\"]=\"\", [\"FX rate forward\"]=\"\", [\"Place PL on price Ccy\"]=\"\", [\"Maturity date\"]=[\"Payment date\"]\n, [\"First fixing date\"]=\"\", [\"Fixing date\"]=\"\", \n[\"Time option start date\"]=\"\", [\"Nominal far\"]=\"\", [\"Nominal L2 far\"]=\"\", [\"Maturity date drawn bonds\"]=\"\", [\"Maturity price drawn bonds\"]=\"\", [\"Collateral pool\"]=\"\", \n[\"Currency overlay model portfolio\"]=\"\", [\"Currency overlay model portfolio L2\"]=\"\", [\"Settlement currency\"]=\"\", [\"FX rate QS\"]=\"\", [\"Transaction origin\"]=\"Internal\", \n[\"Cost\"]=\"\", [\"Currency\"]=\"\", [\"Amount cost currency\"]=\"\", [\"Cost L2\"]=\"\", [\"Currency L2\"]=\"\", [\"Amount cost currency L2\"]=\"\", [\"Underlying Security ID\"]=\"\", \n[\"Requirement percentage\"]=\"\", [\"Dividend all in percentage\"]=\"\", [\"Rate\"]=\"\", [\"Fee type\"]=\"\", [\"Split trade\"]=\"\", [\"Underlying price\"]=\"\", [\"Underlying holding portfolio\"]=\"\", \n[\"Underlying holding nominal\"]=\"\", [\"Pledge Security ID\"]=\"\", [\"Account ID\"]=\"\", [\"Execution Reference\"]=\"\", [\"Quote currency\"]=\"\", [\"Current face\"]=\"\", [\"Daylight indicator\"]=\"\", \n[\"Collateral result ID\"]=\"\", [\"Destination custody\"]=\"\", [\"Delivery type\"]=\"\", \n[\"ExpansionPlaceHolder83\"]=\"\", [\"ExpansionPlaceHolder84\"]=\"\", [\"ExpansionPlaceHolder85\"]=\"\", [\"ExpansionPlaceHolder86\"]=\"\", \n[\"ExpansionPlaceHolder87\"]=\"\", [\"ExpansionPlaceHolder88\"]=\"\", [\"ExpansionPlaceHolder89\"]=\"\", [\"ExpansionPlaceHolder90\"]=\"\", [\"ExpansionPlaceHolder91\"]=\"\", \n[\"ExpansionPlaceHolder92\"]=\"\", [\"ExpansionPlaceHolder93\"]=\"\", [\"ExpansionPlaceHolder94\"]=\"\", [\"ExpansionPlaceHolder95\"]=\"\", [\"ExpansionPlaceHolder96\"]=\"\", \n[\"ExpansionPlaceHolder97\"]=\"\", [\"ExpansionPlaceHolder98\"]=\"\", [\"ExpansionPlaceHolder99\"]=\"\", [\"ExpansionPlaceHolder100\"]=\"\", [\"ExpansionPlaceHolder101\"]=\"\", \n[\"ExpansionPlaceHolder102\"]=\"\", [\"ExpansionPlaceHolder103\"]=\"\", [\"ExpansionPlaceHolder104\"]=\"\", [\"ExpansionPlaceHolder105\"]=\"\", [\"ExpansionPlaceHolder106\"]=\"\", \n[\"ExpansionPlaceHolder107\"]=\"\", [\"ExpansionPlaceHolder108\"]=\"\", [\"ExpansionPlaceHolder109\"]=\"\", [\"ExpansionPlaceHolder110\"]=\"\", [\"ExpansionPlaceHolder111\"]=\"\", \n[\"ExpansionPlaceHolder112\"]=\"\", [\"ExpansionPlaceHolder113\"]=\"\", [\"ExpansionPlaceHolder114\"]=\"\", [\"ExpansionPlaceHolder115\"]=\"\", [\"ExpansionPlaceHolder116\"]=\"\", \n[\"ExpansionPlaceHolder117\"]=\"\", [\"ExpansionPlaceHolder118\"]=\"\", [\"ExpansionPlaceHolder119\"]=\"\", [\"ExpansionPlaceHolder120\"]=\"\", [\"ExpansionPlaceHolder121\"]=\"\", \n[\"ExpansionPlaceHolder122\"]=\"\", [\"ExpansionPlaceHolder123\"]=\"\", [\"ExpansionPlaceHolder124\"]=\"\", [\"ExpansionPlaceHolder125\"]=\"\", [\"ExpansionPlaceHolder126\"]=\"\", \n[\"ExpansionPlaceHolder127\"]=\"\", [\"ExpansionPlaceHolder128\"]=\"\", [\"ExpansionPlaceHolder129\"]=\"\", [\"ExpansionPlaceHolder130\"]=\"\", [\"ExpansionPlaceHolder131\"]=\"\", \n[\"ExpansionPlaceHolder132\"]=\"\", [\"ExpansionPlaceHolder133\"]=\"\", [\"ExpansionPlaceHolder134\"]=\"\", [\"ExpansionPlaceHolder135\"]=\"\", [\"ExpansionPlaceHolder136\"]=\"\", \n[\"ExpansionPlaceHolder137\"]=\"\", [\"ExpansionPlaceHolder138\"]=\"\", [\"ExpansionPlaceHolder139\"]=\"\", [\"ExpansionPlaceHolder140\"]=\"\", [\"ExpansionPlaceHolder141\"]=\"\", \n[\"ExpansionPlaceHolder142\"]=\"\", [\"ExpansionPlaceHolder143\"]=\"\", [\"ExpansionPlaceHolder144\"]=\"\", [\"ExpansionPlaceHolder145\"]=\"\", [\"ExpansionPlaceHolder146\"]=\"\", \n[\"ExpansionPlaceHolder147\"]=\"\", [\"ExpansionPlaceHolder148\"]=\"\", [\"ExpansionPlaceHolder149\"]=\"\", [\"ExpansionPlaceHolder150\"]=\"\", [\"ExpansionPlaceHolder151\"]=\"\", \n[\"ExpansionPlaceHolder152\"]=\"\", [\"ExpansionPlaceHolder153\"]=\"\", [\"ExpansionPlaceHolder154\"]=\"\", [\"ExpansionPlaceHolder155\"]=\"\", [\"ExpansionPlaceHolder156\"]=\"\", \n[\"ExpansionPlaceHolder157\"]=\"\", [\"ExpansionPlaceHolder158\"]=\"\", [\"ExpansionPlaceHolder159\"]=\"\", [\"ExpansionPlaceHolder160\"]=\"\", [\"ExpansionPlaceHolder161\"]=\"\", \n[\"ExpansionPlaceHolder162\"]=\"\", [\"ExpansionPlaceHolder163\"]=\"\", [\"ExpansionPlaceHolder164\"]=\"\", [\"ExpansionPlaceHolder165\"]=\"\", [\"ExpansionPlaceHolder166\"]=\"\", \n[\"ExpansionPlaceHolder167\"]=\"\", [\"ExpansionPlaceHolder168\"]=\"\", [\"ExpansionPlaceHolder169\"]=\"\", [\"ExpansionPlaceHolder170\"]=\"\", [\"ExpansionPlaceHolder171\"]=\"\", \n[\"ExpansionPlaceHolder172\"]=\"\", [\"ExpansionPlaceHolder173\"]=\"\", [\"ExpansionPlaceHolder174\"]=\"\", [\"ExpansionPlaceHolder175\"]=\"\", [\"ExpansionPlaceHolder176\"]=\"\", \n[\"ExpansionPlaceHolder177\"]=\"\", [\"ExpansionPlaceHolder178\"]=\"\", [\"ExpansionPlaceHolder179\"]=\"\"\n')\n", "type": "Expression"}, "queryTimeout": "00:10:00", "noTruncation": false}, "sink": {"type": "DelimitedTextSink", "storeSettings": {"type": "AzureBlobFSWriteSettings"}, "formatSettings": {"type": "DelimitedTextWriteSettings", "quoteAllText": true, "fileExtension": ".txt"}}, "enableStaging": false, "translator": {"type": "TabularTranslator", "typeConversion": true, "typeConversionSettings": {"allowDataTruncation": true, "treatBooleanAsNumber": false}}}, "inputs": [{"referenceName": "ADX_FX", "type": "DatasetReference", "parameters": {"Table": "NorthernTrust_PendingFXDetailByCurrency"}}], "outputs": [{"referenceName": "ADL_NorthernTrust_TEMP_FILE", "type": "DatasetReference"}]}, {"name": "Tranform from TXT to CSV", "type": "ExecuteDataFlow", "dependsOn": [{"activity": "Custodian Portfolio Code Mapping", "dependencyConditions": ["Succeeded"]}], "policy": {"timeout": "1.00:00:00", "retry": 0, "retryIntervalInSeconds": 30, "secureOutput": false, "secureInput": false}, "userProperties": [], "typeProperties": {"dataflow": {"referenceName": "IN-142 - Northern Trust TXT To CSV Transform", "type": "DataFlowReference"}, "compute": {"coreCount": 16, "computeType": "General"}, "traceLevel": "Fine"}}, {"name": "Archive IN", "type": "Copy", "dependsOn": [{"activity": "Send to SCD", "dependencyConditions": ["Succeeded"]}], "policy": {"timeout": "7.00:00:00", "retry": 0, "retryIntervalInSeconds": 30, "secureOutput": false, "secureInput": false}, "userProperties": [], "typeProperties": {"source": {"type": "BinarySource", "storeSettings": {"type": "AzureBlobFSReadSettings", "recursive": true, "deleteFilesAfterCompletion": true}, "formatSettings": {"type": "BinaryReadSettings"}}, "sink": {"type": "BinarySink", "storeSettings": {"type": "AzureBlobFSWriteSettings"}}, "enableStaging": false}, "inputs": [{"referenceName": "ADL_Binary_Dir", "type": "DatasetReference", "parameters": {"Path": "FX/Northern Trust/IN"}}], "outputs": [{"referenceName": "ADL_Binary_Dir", "type": "DatasetReference", "parameters": {"Path": {"value": "@concat('FX/Northern Trust/ARCHIVE/',pipeline().parameters.P_DATE,'/IN')", "type": "Expression"}}}]}, {"name": "IN-142 - Northern Trust Adding Additional Headers", "type": "ExecuteDataFlow", "dependsOn": [{"activity": "Set OutputFileName", "dependencyConditions": ["Succeeded"]}], "policy": {"timeout": "1.00:00:00", "retry": 0, "retryIntervalInSeconds": 30, "secureOutput": false, "secureInput": false}, "userProperties": [], "typeProperties": {"dataflow": {"referenceName": "IN-142 - Northern Trust Adding Additional Headers", "type": "DataFlowReference", "parameters": {"OutputFileName": {"value": "'@{variables('OutputFileName')}'", "type": "Expression"}}}, "compute": {"coreCount": 8, "computeType": "General"}, "traceLevel": "Fine"}}, {"name": "Archive OUT", "type": "Copy", "dependsOn": [{"activity": "Send to SCD", "dependencyConditions": ["Succeeded"]}], "policy": {"timeout": "7.00:00:00", "retry": 0, "retryIntervalInSeconds": 30, "secureOutput": false, "secureInput": false}, "userProperties": [], "typeProperties": {"source": {"type": "BinarySource", "storeSettings": {"type": "AzureBlobFSReadSettings", "recursive": true, "deleteFilesAfterCompletion": true}, "formatSettings": {"type": "BinaryReadSettings"}}, "sink": {"type": "BinarySink", "storeSettings": {"type": "AzureBlobFSWriteSettings"}}, "enableStaging": false}, "inputs": [{"referenceName": "ADL_Binary_Dir", "type": "DatasetReference", "parameters": {"Path": "FX/Northern Trust/OUT"}}], "outputs": [{"referenceName": "ADL_Binary_Dir", "type": "DatasetReference", "parameters": {"Path": {"value": "@concat('FX/Northern Trust/ARCHIVE/',pipeline().parameters.P_DATE,'/OUT')", "type": "Expression"}}}]}, {"name": "Archive TEMP", "type": "Copy", "dependsOn": [{"activity": "Send to SCD", "dependencyConditions": ["Succeeded"]}], "policy": {"timeout": "7.00:00:00", "retry": 0, "retryIntervalInSeconds": 30, "secureOutput": false, "secureInput": false}, "userProperties": [], "typeProperties": {"source": {"type": "BinarySource", "storeSettings": {"type": "AzureBlobFSReadSettings", "recursive": true, "deleteFilesAfterCompletion": true}, "formatSettings": {"type": "BinaryReadSettings"}}, "sink": {"type": "BinarySink", "storeSettings": {"type": "AzureBlobFSWriteSettings"}}, "enableStaging": false}, "inputs": [{"referenceName": "ADL_Binary_Dir", "type": "DatasetReference", "parameters": {"Path": "FX/Northern Trust/TEMP"}}], "outputs": [{"referenceName": "ADL_Binary_Dir", "type": "DatasetReference", "parameters": {"Path": {"value": "@concat('FX/Northern Trust/ARCHIVE/',pipeline().parameters.P_DATE,'/TEMP')", "type": "Expression"}}}]}, {"name": "Set OutputFileName", "type": "SetVariable", "dependsOn": [{"activity": "Copy from ADX to Temp", "dependencyConditions": ["Succeeded"]}], "policy": {"secureOutput": false, "secureInput": false}, "userProperties": [], "typeProperties": {"variableName": "OutputFileName", "value": {"value": "@concat(pipeline().parameters.P_DATE,\nformatDateTime(utcnow(), 'HHmmss'), '_',\npipeline().parameters.P_DATE, \n'_NorthernTrust_FXTrades.csv' )", "type": "Expression"}}}, {"name": "Send to SCD", "type": "IfCondition", "dependsOn": [{"activity": "IN-142 - Northern Trust Adding Additional Headers", "dependencyConditions": ["Succeeded"]}], "userProperties": [], "typeProperties": {"expression": {"value": "@pipeline().globalParameters.SendToSCD_FX", "type": "Expression"}, "ifTrueActivities": [{"name": "Create DCS CSV in SCD SFTP_copy1", "type": "Copy", "dependsOn": [], "policy": {"timeout": "7.00:00:00", "retry": 0, "retryIntervalInSeconds": 30, "secureOutput": false, "secureInput": false}, "userProperties": [], "typeProperties": {"source": {"type": "BinarySource", "storeSettings": {"type": "AzureBlobFSReadSettings", "recursive": true}, "formatSettings": {"type": "BinaryReadSettings"}}, "sink": {"type": "BinarySink", "storeSettings": {"type": "SftpWriteSettings", "operationTimeout": "01:00:00", "useTempFileRename": true}}, "enableStaging": false}, "inputs": [{"referenceName": "ADL_Binary_File", "type": "DatasetReference", "parameters": {"FileName": {"value": "@variables('OutputFileName')", "type": "Expression"}, "Path": {"value": "@concat('FX/Northern Trust/OUT')", "type": "Expression"}, "Container": {"value": "scdintegration", "type": "Expression"}}}], "outputs": [{"referenceName": "SCD_SFTP_Binary_Dir", "type": "DatasetReference", "parameters": {"Path": {"value": "@concat(pipeline().globalParameters.SCD_ENV, pipeline().globalParameters.SCD_SFTP_FX)", "type": "Expression"}}}]}]}}, {"name": "Custodian Portfolio Code Mapping", "type": "Copy", "dependsOn": [], "policy": {"timeout": "7.00:00:00", "retry": 0, "retryIntervalInSeconds": 30, "secureOutput": false, "secureInput": false}, "userProperties": [], "typeProperties": {"source": {"type": "SqlMISource", "sqlReaderQuery": "SELECT Portfolio\tAS ID\n\t , Portfolio\tAS SSID\n\t , CUSTODY\t\tAS CADCustodianAccount\nFROM dbo.VW_PORTFOLIO_CUSTODY", "partitionOption": "None"}, "sink": {"type": "DelimitedTextSink", "storeSettings": {"type": "AzureBlobFSWriteSettings"}, "formatSettings": {"type": "DelimitedTextWriteSettings", "quoteAllText": true, "fileExtension": ".txt"}}, "enableStaging": false, "translator": {"type": "TabularTranslator", "typeConversion": true, "typeConversionSettings": {"allowDataTruncation": true, "treatBooleanAsNumber": false}}}, "inputs": [{"referenceName": "SQLMI_RESEARCH_PARAM", "type": "DatasetReference", "parameters": {"DBName": "SCDAT", "SchemaName": "dbo.", "TableName": "VW_PORTFOLIO_CUSTODY"}}], "outputs": [{"referenceName": "AzureDataLakeStorage_FX_03", "type": "DatasetReference", "parameters": {"FileName": "CustodianPortfolioMapping.csv", "SubFolder": "FX/Northern Trust/TEMP", "ColDelimiter": ",", "HasHeaderRow": true}}]}, {"name": "Clean ADX", "type": "ExecutePipeline", "dependsOn": [{"activity": "Tranform from TXT to CSV", "dependencyConditions": ["Succeeded"]}], "policy": {"secureInput": false}, "userProperties": [], "typeProperties": {"pipeline": {"referenceName": "GNRL05_CLEAN_ADX_TABLE", "type": "PipelineReference"}, "waitOnCompletion": true, "parameters": {"TableName": "NorthernTrust_PendingFXDetailByCurrency", "FilterCondition": {"value": "@concat('format_datetime(CreatedDate, \"yyyyMMdd\")  == \"', pipeline().parameters.P_DATE,'\"')", "type": "Expression"}, "ADXDatabase": "FX"}}}], "parameters": {"P_DATE": {"type": "string"}}, "variables": {"OutputFileName": {"type": "String"}}, "folder": {"name": "Northern Trust/Northern Trust FX"}, "annotations": [], "lastPublishTime": "2024-05-29T21:01:01Z"}, "type": "Microsoft.DataFactory/factories/pipelines"}