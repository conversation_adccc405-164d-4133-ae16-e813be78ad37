{"name": "SFTP_SCD_RIMES_Parameterized_Directory", "properties": {"linkedServiceName": {"referenceName": "SCD_SFTP", "type": "LinkedServiceReference"}, "parameters": {"FilePath": {"type": "string"}, "FileName": {"type": "string"}, "col_delim": {"type": "string", "defaultValue": ";"}}, "folder": {"name": "Inbound/Other/IN-180 RIMES to SCD"}, "annotations": [], "type": "DelimitedText", "typeProperties": {"location": {"type": "SftpLocation", "fileName": {"value": "@dataset().FileName", "type": "Expression"}, "folderPath": {"value": "@dataset().FilePath", "type": "Expression"}}, "columnDelimiter": ";", "escapeChar": "\\", "firstRowAsHeader": false, "quoteChar": ""}, "schema": []}, "type": "Microsoft.DataFactory/factories/datasets"}