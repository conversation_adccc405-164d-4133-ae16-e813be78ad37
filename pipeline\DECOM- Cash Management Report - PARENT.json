{"name": "DECOM- Cash Management Report - PARENT", "properties": {"activities": [{"name": "Load Cash Management Report to ADX", "type": "Copy", "dependsOn": [{"activity": "From SCD SFTP to ADL", "dependencyConditions": ["Succeeded"]}], "policy": {"timeout": "7.00:00:00", "retry": 0, "retryIntervalInSeconds": 30, "secureOutput": false, "secureInput": false}, "userProperties": [], "typeProperties": {"source": {"type": "DelimitedTextSource", "additionalColumns": [{"name": "Loaded", "value": {"value": "@utcNow()", "type": "Expression"}}], "storeSettings": {"type": "AzureBlobFSReadSettings", "recursive": true, "enablePartitionDiscovery": false}, "formatSettings": {"type": "DelimitedTextReadSettings"}}, "sink": {"type": "AzureDataExplorerSink"}, "enableStaging": false, "translator": {"type": "TabularTranslator", "mappings": [{"source": {"name": "Cash management setup", "type": "String", "physicalType": "String"}, "sink": {"name": "Cash management setup", "type": "String", "physicalType": "string"}}, {"source": {"name": "Report date", "type": "String", "physicalType": "String"}, "sink": {"name": "Report date", "type": "String", "physicalType": "string"}}, {"source": {"name": "Portfolio", "type": "String", "physicalType": "String"}, "sink": {"name": "Portfolio", "type": "String", "physicalType": "string"}}, {"source": {"name": "Portfolio name", "type": "String", "physicalType": "String"}, "sink": {"name": "Portfolio name", "type": "String", "physicalType": "string"}}, {"source": {"name": "Portfolio currency", "type": "String", "physicalType": "String"}, "sink": {"name": "Portfolio currency", "type": "String", "physicalType": "string"}}, {"source": {"name": "Bank account ID", "type": "String", "physicalType": "String"}, "sink": {"name": "Bank account ID", "type": "String", "physicalType": "string"}}, {"source": {"name": "Bank currency", "type": "String", "physicalType": "String"}, "sink": {"name": "Bank currency", "type": "String", "physicalType": "string"}}, {"source": {"name": "Money market positions QC", "type": "String", "physicalType": "String"}, "sink": {"name": "Money market positions QC", "type": "String", "physicalType": "string"}}, {"source": {"name": "TD PC", "type": "String", "physicalType": "String"}, "sink": {"name": "TD PC", "type": "Double", "physicalType": "real"}}, {"source": {"name": "TD+1BD PC", "type": "String", "physicalType": "String"}, "sink": {"name": "TD_1BD PC", "type": "Double", "physicalType": "real"}}, {"source": {"name": "TD+2BD PC", "type": "String", "physicalType": "String"}, "sink": {"name": "TD_2BD PC", "type": "Double", "physicalType": "real"}}, {"source": {"name": "TD+3BD PC", "type": "String", "physicalType": "String"}, "sink": {"name": "TD_3BD PC", "type": "Double", "physicalType": "real"}}, {"source": {"name": "Pending money market orders QC", "type": "String", "physicalType": "String"}, "sink": {"name": "Pending money market orders QC", "type": "String", "physicalType": "string"}}, {"source": {"name": "Portfolio manager", "type": "String", "physicalType": "String"}, "sink": {"name": "Portfolio manager", "type": "String", "physicalType": "string"}}, {"source": {"name": "Portfolio manager associate", "type": "String", "physicalType": "String"}, "sink": {"name": "Portfolio manager associate", "type": "String", "physicalType": "string"}}, {"source": {"name": "Client type", "type": "String", "physicalType": "String"}, "sink": {"name": "Client type", "type": "String", "physicalType": "string"}}, {"source": {"name": "Money market tool", "type": "String", "physicalType": "String"}, "sink": {"name": "Money market tool", "type": "String", "physicalType": "string"}}, {"source": {"name": "Portfolio holdings", "type": "String", "physicalType": "String"}, "sink": {"name": "Portfolio holdings", "type": "String", "physicalType": "string"}}, {"source": {"name": "Loaded", "type": "String"}, "sink": {"name": "Loaded", "type": "DateTime", "physicalType": "datetime"}}], "typeConversion": true, "typeConversionSettings": {"allowDataTruncation": true, "treatBooleanAsNumber": false}}}, "inputs": [{"referenceName": "ADL_Cash_Management_TSV", "type": "DatasetReference", "parameters": {"FilePath": {"value": "@concat('Report Automation/Money Market Report/IN')", "type": "Expression"}, "FileName": {"value": "@variables('FileName')", "type": "Expression"}}}], "outputs": [{"referenceName": "ADX_PARAMETER", "type": "DatasetReference", "parameters": {"Table": "Money_Market_Report", "Database": "GENERAL"}}]}, {"name": "Get Distinct PM PMA Name from Latest Load", "type": "Lookup", "dependsOn": [{"activity": "Load Cash Management Report to ADX", "dependencyConditions": ["Succeeded"]}], "policy": {"timeout": "7.00:00:00", "retry": 0, "retryIntervalInSeconds": 30, "secureOutput": false, "secureInput": false}, "userProperties": [], "typeProperties": {"source": {"type": "AzureDataExplorerSource", "query": "let maxLoaded = Money_Market_Report\n| summarize maxLoaded  = max(Loaded);\nMoney_Market_Report\n| join kind = inner maxLoaded on $left.Loaded == $right.maxLoaded\n| summarize  by ['Portfolio manager'], ['Portfolio manager associate']\n| where isempty(['Portfolio manager']) == false and  isempty(['Portfolio manager associate']) == false ", "queryTimeout": "00:10:00", "noTruncation": true}, "dataset": {"referenceName": "ADX_PARAMETER", "type": "DatasetReference", "parameters": {"Table": "Money_Market_Report", "Database": "GENERAL"}}, "firstRowOnly": false}}, {"name": "For Each PM and PMA", "type": "ForEach", "dependsOn": [{"activity": "Get Distinct PM PMA Name from Latest Load", "dependencyConditions": ["Succeeded"]}], "userProperties": [], "typeProperties": {"items": {"value": "@activity('Get Distinct PM PMA Name from Latest Load').output.value", "type": "Expression"}, "isSequential": false, "activities": [{"name": "Execute Cash Management Report - Child", "type": "ExecutePipeline", "dependsOn": [], "policy": {"secureInput": false}, "userProperties": [], "typeProperties": {"pipeline": {"referenceName": "DECOM- Cash Management Report - CHILD", "type": "PipelineReference"}, "waitOnCompletion": false, "parameters": {"PMName": {"value": "@item()['Portfolio manager']", "type": "Expression"}, "PMAName": {"value": "@item()['Portfolio manager associate']", "type": "Expression"}}}}]}}, {"name": "From SCD SFTP to ADL", "type": "Copy", "dependsOn": [{"activity": "Set FileName", "dependencyConditions": ["Succeeded"]}], "policy": {"timeout": "7.00:00:00", "retry": 0, "retryIntervalInSeconds": 30, "secureOutput": false, "secureInput": false}, "userProperties": [], "typeProperties": {"source": {"type": "DelimitedTextSource", "storeSettings": {"type": "SftpReadSettings", "recursive": true, "enablePartitionDiscovery": false, "disableChunking": false}, "formatSettings": {"type": "DelimitedTextReadSettings"}}, "sink": {"type": "DelimitedTextSink", "storeSettings": {"type": "AzureBlobFSWriteSettings"}, "formatSettings": {"type": "DelimitedTextWriteSettings", "quoteAllText": true, "fileExtension": ".txt"}}, "enableStaging": false, "translator": {"type": "TabularTranslator", "typeConversion": true, "typeConversionSettings": {"allowDataTruncation": true, "treatBooleanAsNumber": false}}}, "inputs": [{"referenceName": "SFTP_SCD_CashManagement_2", "type": "DatasetReference", "parameters": {"FilePath": {"value": "@concat('MAWERP1/ActiveData/Interfaces/Out/SFTP/Front Office/Outgoing')", "type": "Expression"}, "FileName": {"value": "@variables('FileName')", "type": "Expression"}}}], "outputs": [{"referenceName": "ADL_Cash_Management_TSV", "type": "DatasetReference", "parameters": {"FilePath": {"value": "@concat('Report Automation/Money Market Report/IN')", "type": "Expression"}, "FileName": {"value": "@variables('FileName')", "type": "Expression"}}}]}, {"name": "Find File in SCD SFTP", "type": "GetMetadata", "dependsOn": [], "policy": {"timeout": "7.00:00:00", "retry": 0, "retryIntervalInSeconds": 30, "secureOutput": false, "secureInput": false}, "userProperties": [], "typeProperties": {"dataset": {"referenceName": "SFTP_SCD_CashManagement", "type": "DatasetReference", "parameters": {"FilePath": "MAWERP1/ActiveData/Interfaces/Out/SFTP/Front Office/Outgoing"}}, "fieldList": ["childItems"], "storeSettings": {"type": "SftpReadSettings", "recursive": true, "modifiedDatetimeStart": {"value": "@getPastTime(1,'Day' )", "type": "Expression"}, "modifiedDatetimeEnd": {"value": "@utcNow()", "type": "Expression"}, "enablePartitionDiscovery": false, "disableChunking": false}, "formatSettings": {"type": "DelimitedTextReadSettings"}}}, {"name": "Filter Current File", "type": "Filter", "dependsOn": [{"activity": "Find File in SCD SFTP", "dependencyConditions": ["Succeeded"]}], "userProperties": [], "typeProperties": {"items": {"value": "@activity('Find File in SCD SFTP').output.childItems", "type": "Expression"}, "condition": {"value": "@and(contains(item().name, 'Cash_management_report_'),contains(item().name, formatDateTime(\nconvertFromUtc(utcnow(), 'Mountain Standard Time')\n,'yyyyMMdd')))", "type": "Expression"}}}, {"name": "Set FileName", "type": "SetVariable", "dependsOn": [{"activity": "Set Index to Get Lastest File", "dependencyConditions": ["Succeeded"]}], "policy": {"secureOutput": false, "secureInput": false}, "userProperties": [], "typeProperties": {"variableName": "FileName", "value": {"value": "@activity('Filter Current File').output.Value[add(int(variables('FileCountIndex')),-1)].name", "type": "Expression"}}}, {"name": "Move to Archive IN", "type": "Copy", "dependsOn": [{"activity": "Wait for all Pipelines to Complete RUN", "dependencyConditions": ["Succeeded"]}], "policy": {"timeout": "7.00:00:00", "retry": 0, "retryIntervalInSeconds": 30, "secureOutput": false, "secureInput": false}, "userProperties": [], "typeProperties": {"source": {"type": "DelimitedTextSource", "storeSettings": {"type": "AzureBlobFSReadSettings", "recursive": true, "enablePartitionDiscovery": false}, "formatSettings": {"type": "DelimitedTextReadSettings"}}, "sink": {"type": "DelimitedTextSink", "storeSettings": {"type": "AzureBlobFSWriteSettings"}, "formatSettings": {"type": "DelimitedTextWriteSettings", "quoteAllText": true, "fileExtension": ".txt"}}, "enableStaging": false, "translator": {"type": "TabularTranslator", "typeConversion": true, "typeConversionSettings": {"allowDataTruncation": true, "treatBooleanAsNumber": false}}}, "inputs": [{"referenceName": "ADL_Cash_Management_TSV", "type": "DatasetReference", "parameters": {"FilePath": {"value": "@concat('Report Automation/Money Market Report/IN')", "type": "Expression"}, "FileName": {"value": "@variables('FileName')", "type": "Expression"}}}], "outputs": [{"referenceName": "ADL_Cash_Management_TSV", "type": "DatasetReference", "parameters": {"FilePath": {"value": "@concat('Report Automation/Money Market Report/ARCHIVE/IN/',formatDateTime(\nconvertFromUtc(utcnow(), 'Mountain Standard Time')\n,'yyyyMMdd'))", "type": "Expression"}, "FileName": {"value": "@variables('FileName')", "type": "Expression"}}}]}, {"name": "Clean Up IN", "type": "Delete", "dependsOn": [{"activity": "Move to Archive IN", "dependencyConditions": ["Succeeded"]}], "policy": {"timeout": "7.00:00:00", "retry": 0, "retryIntervalInSeconds": 30, "secureOutput": false, "secureInput": false}, "userProperties": [], "typeProperties": {"dataset": {"referenceName": "ADL_Cash_Management_2", "type": "DatasetReference", "parameters": {"FilePath": {"value": "@concat('Report Automation/Money Market Report/','IN')", "type": "Expression"}, "FileName": {"value": "@variables('FileName')", "type": "Expression"}}}, "enableLogging": false, "storeSettings": {"type": "AzureBlobFSReadSettings", "recursive": true, "enablePartitionDiscovery": false}}}, {"name": "Set Index to Get Lastest File", "type": "SetVariable", "dependsOn": [{"activity": "Filter Current File", "dependencyConditions": ["Succeeded"]}], "policy": {"secureOutput": false, "secureInput": false}, "userProperties": [], "typeProperties": {"variableName": "FileCountIndex", "value": {"value": "@string(activity('Filter Current File').output.FilteredItemsCount)", "type": "Expression"}}}, {"name": "Move to Archive OUT", "type": "Copy", "dependsOn": [{"activity": "Wait for all Pipelines to Complete RUN", "dependencyConditions": ["Succeeded"]}], "policy": {"timeout": "7.00:00:00", "retry": 0, "retryIntervalInSeconds": 30, "secureOutput": false, "secureInput": false}, "userProperties": [], "typeProperties": {"source": {"type": "DelimitedTextSource", "storeSettings": {"type": "AzureBlobFSReadSettings", "recursive": true, "wildcardFileName": "*.csv", "enablePartitionDiscovery": false}, "formatSettings": {"type": "DelimitedTextReadSettings"}}, "sink": {"type": "DelimitedTextSink", "storeSettings": {"type": "AzureBlobFSWriteSettings"}, "formatSettings": {"type": "DelimitedTextWriteSettings", "quoteAllText": true, "fileExtension": ".txt"}}, "enableStaging": false, "translator": {"type": "TabularTranslator", "typeConversion": true, "typeConversionSettings": {"allowDataTruncation": true, "treatBooleanAsNumber": false}}}, "inputs": [{"referenceName": "ADL_Cash_Management_3", "type": "DatasetReference", "parameters": {"FilePath": {"value": "@concat('Report Automation/Money Market Report/OUT')", "type": "Expression"}}}], "outputs": [{"referenceName": "ADL_Cash_Management_3", "type": "DatasetReference", "parameters": {"FilePath": {"value": "@concat('Report Automation/Money Market Report/ARCHIVE/OUT/',formatDateTime(\nconvertFromUtc(utcnow(), 'Mountain Standard Time')\n,'yyyyMMdd'))\n", "type": "Expression"}}}]}, {"name": "Clean Up OUT", "type": "Delete", "dependsOn": [{"activity": "Move to Archive OUT", "dependencyConditions": ["Succeeded"]}], "policy": {"timeout": "7.00:00:00", "retry": 0, "retryIntervalInSeconds": 30, "secureOutput": false, "secureInput": false}, "userProperties": [], "typeProperties": {"dataset": {"referenceName": "ADL_Cash_Management_3", "type": "DatasetReference", "parameters": {"FilePath": {"value": "@concat('Report Automation/Money Market Report/OUT')", "type": "Expression"}}}, "enableLogging": false, "storeSettings": {"type": "AzureBlobFSReadSettings", "recursive": true, "wildcardFileName": "*.csv", "enablePartitionDiscovery": false}}}, {"name": "Wait for all Pipelines to Complete RUN", "type": "Wait", "dependsOn": [{"activity": "For Each PM and PMA", "dependencyConditions": ["Succeeded"]}], "userProperties": [], "typeProperties": {"waitTimeInSeconds": 600}}], "variables": {"FileName": {"type": "String"}, "PMName": {"type": "String"}, "PMAName": {"type": "String"}, "EmailBody": {"type": "String"}, "OutputFileName": {"type": "String", "defaultValue": "scdintegration/Report Automation/ARCHIVE/OUT/20220726/Cash Management Report_<PERSON><PERSON>_Portfolio Manager.csv"}, "FileCountIndex": {"type": "String"}}, "folder": {"name": "Internal/Cash Management Report Workflow"}, "annotations": [], "lastPublishTime": "2023-12-01T04:11:49Z"}, "type": "Microsoft.DataFactory/factories/pipelines"}