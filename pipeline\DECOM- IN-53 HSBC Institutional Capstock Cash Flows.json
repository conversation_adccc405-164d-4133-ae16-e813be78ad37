{"name": "DECOM- IN-53 HSBC Institutional Capstock Cash Flows", "properties": {"activities": [{"name": "Copy HSBC data from xlsx into csv", "type": "Copy", "dependsOn": [{"activity": "Set HSBC FileName", "dependencyConditions": ["Succeeded"]}], "policy": {"timeout": "7.00:00:00", "retry": 0, "retryIntervalInSeconds": 30, "secureOutput": false, "secureInput": false}, "userProperties": [], "typeProperties": {"source": {"type": "ExcelSource", "storeSettings": {"type": "AzureBlobFSReadSettings", "recursive": false, "wildcardFolderPath": "InstitutionalCapstockCashFlows/HSBC_Transactions/IN", "wildcardFileName": {"value": "@concat(pipeline().parameters.Date,'Mawer Managed Funds Cash Movement *.xlsx')", "type": "Expression"}, "enablePartitionDiscovery": false}}, "sink": {"type": "DelimitedTextSink", "storeSettings": {"type": "AzureBlobFSWriteSettings"}, "formatSettings": {"type": "DelimitedTextWriteSettings", "quoteAllText": true, "fileExtension": ".csv"}}, "enableStaging": false}, "inputs": [{"referenceName": "ADL_INSTITUTIONAL_CAPSTOCK_EXCEL", "type": "DatasetReference", "parameters": {"HasHeader": true}}], "outputs": [{"referenceName": "ADL_Delimited_File", "type": "DatasetReference", "parameters": {"FilePath": "InstitutionalCapstockCashFlows/HSBC_Transactions/TEMP", "FileName": {"value": "@variables('HSBCFileName')", "type": "Expression"}, "ColDelimiter": ",", "FirstRowHeader": true}}]}, {"name": "Set HSBC FileName", "type": "SetVariable", "dependsOn": [], "policy": {"secureOutput": false, "secureInput": false}, "userProperties": [], "typeProperties": {"variableName": "HSBCFileName", "value": {"value": "@concat(pipeline().parameters.Date,'MawerManagedFundsCashMovement.csv')", "type": "Expression"}}}, {"name": "Stage Data into ADX HSBC_Cash_Trans", "type": "Copy", "dependsOn": [{"activity": "Cleanup ADX HSBC_Cash_Trans", "dependencyConditions": ["Succeeded"]}], "policy": {"timeout": "7.00:00:00", "retry": 0, "retryIntervalInSeconds": 30, "secureOutput": false, "secureInput": false}, "userProperties": [], "typeProperties": {"source": {"type": "DelimitedTextSource", "additionalColumns": [{"name": "CreatedDate", "value": {"value": "@concat(\nsubstring(pipeline().parameters.Date,0,4),\n'-',\nsubstring(pipeline().parameters.Date,4,2),\n'-',\nsubstring(pipeline().parameters.Date,6,2)\n)", "type": "Expression"}}], "storeSettings": {"type": "AzureBlobFSReadSettings", "recursive": true, "enablePartitionDiscovery": false}, "formatSettings": {"type": "DelimitedTextReadSettings"}}, "sink": {"type": "AzureDataExplorerSink"}, "enableStaging": false, "translator": {"type": "TabularTranslator", "mappings": [{"source": {"name": "CreatedDate", "type": "String"}, "sink": {"name": "CreatedDate", "type": "DateTime", "physicalType": "datetime"}}, {"source": {"name": "Sub-Advisor", "type": "String", "physicalType": "String"}, "sink": {"name": "SubAdvisor", "type": "String", "physicalType": "string"}}, {"source": {"name": "Fund Name", "type": "String", "physicalType": "String"}, "sink": {"name": "FundName", "type": "String", "physicalType": "string"}}, {"source": {"name": "Fund", "type": "String", "physicalType": "String"}, "sink": {"name": "Fund", "type": "String", "physicalType": "string"}}, {"source": {"name": "Trade Date", "type": "String", "physicalType": "String"}, "sink": {"name": "TradeDate", "type": "DateTime", "physicalType": "datetime"}}, {"source": {"name": "Settle Date", "type": "String", "physicalType": "String"}, "sink": {"name": "SettleDate", "type": "DateTime", "physicalType": "datetime"}}, {"source": {"name": "<PERSON><PERSON><PERSON><PERSON>", "type": "String", "physicalType": "String"}, "sink": {"name": "<PERSON><PERSON><PERSON><PERSON>", "type": "String", "physicalType": "string"}}, {"source": {"name": "Purchases", "type": "String", "physicalType": "String"}, "sink": {"name": "Purchases", "type": "Double", "physicalType": "real"}}, {"source": {"name": "Redemptions", "type": "String", "physicalType": "String"}, "sink": {"name": "Redemptions", "type": "Double", "physicalType": "real"}}, {"source": {"name": "Net", "type": "String", "physicalType": "String"}, "sink": {"name": "Net", "type": "Double", "physicalType": "real"}}], "typeConversion": true, "typeConversionSettings": {"allowDataTruncation": false, "treatBooleanAsNumber": false}}}, "inputs": [{"referenceName": "ADL_Delimited_File", "type": "DatasetReference", "parameters": {"FilePath": "InstitutionalCapstockCashFlows/HSBC_Transactions/TEMP", "FileName": {"value": "@variables('HSBCFileName')", "type": "Expression"}, "ColDelimiter": ",", "FirstRowHeader": true}}], "outputs": [{"referenceName": "ADX_TransactionOrder", "type": "DatasetReference", "parameters": {"Table": "HSBC_Cash_Transactions"}}]}, {"name": "Cleanup ADX HSBC_Cash_Trans", "type": "ExecutePipeline", "dependsOn": [{"activity": "Copy HSBC data from xlsx into csv", "dependencyConditions": ["Succeeded"]}], "policy": {"secureInput": false}, "userProperties": [], "typeProperties": {"pipeline": {"referenceName": "GNRL05_CLEAN_ADX_TABLE", "type": "PipelineReference"}, "waitOnCompletion": true, "parameters": {"TableName": "HSBC_Cash_Transactions", "FilterCondition": {"value": "@concat('format_datetime(CreatedDate,\"yyyyMMdd\") == \"',\npipeline().parameters.Date,'\"')", "type": "Expression"}, "ADXDatabase": "TransactionOrder"}}}, {"name": "Archive IN - HSBC", "type": "Copy", "dependsOn": [{"activity": "If Copy HSBC file to SCD SFTP", "dependencyConditions": ["Succeeded"]}], "policy": {"timeout": "7.00:00:00", "retry": 0, "retryIntervalInSeconds": 30, "secureOutput": false, "secureInput": false}, "userProperties": [], "typeProperties": {"source": {"type": "BinarySource", "storeSettings": {"type": "AzureBlobFSReadSettings", "recursive": true, "deleteFilesAfterCompletion": true}, "formatSettings": {"type": "BinaryReadSettings"}}, "sink": {"type": "BinarySink", "storeSettings": {"type": "AzureBlobFSWriteSettings"}}, "enableStaging": false}, "inputs": [{"referenceName": "ADL_Binary_Dir", "type": "DatasetReference", "parameters": {"Path": "InstitutionalCapstockCashFlows/HSBC_Transactions/IN"}}], "outputs": [{"referenceName": "ADL_Binary_Dir", "type": "DatasetReference", "parameters": {"Path": {"value": "@concat('InstitutionalCapstockCashFlows/HSBC_Transactions/ARCHIVE/',pipeline().parameters.Date,'/IN')", "type": "Expression"}}}]}, {"name": "Archive OUT - HSBC", "type": "Copy", "dependsOn": [{"activity": "If Copy HSBC file to SCD SFTP", "dependencyConditions": ["Succeeded"]}], "policy": {"timeout": "7.00:00:00", "retry": 0, "retryIntervalInSeconds": 30, "secureOutput": false, "secureInput": false}, "userProperties": [], "typeProperties": {"source": {"type": "BinarySource", "storeSettings": {"type": "AzureBlobFSReadSettings", "recursive": true, "deleteFilesAfterCompletion": true}, "formatSettings": {"type": "BinaryReadSettings"}}, "sink": {"type": "BinarySink", "storeSettings": {"type": "AzureBlobFSWriteSettings"}}, "enableStaging": false}, "inputs": [{"referenceName": "ADL_Binary_Dir", "type": "DatasetReference", "parameters": {"Path": "InstitutionalCapstockCashFlows/HSBC_Transactions/OUT"}}], "outputs": [{"referenceName": "ADL_Binary_Dir", "type": "DatasetReference", "parameters": {"Path": {"value": "@concat('InstitutionalCapstockCashFlows/HSBC_Transactions/ARCHIVE/',pipeline().parameters.Date,'/OUT')", "type": "Expression"}}}]}, {"name": "Archive TEMP - HSBC", "type": "Copy", "dependsOn": [{"activity": "If Copy HSBC file to SCD SFTP", "dependencyConditions": ["Succeeded"]}], "policy": {"timeout": "7.00:00:00", "retry": 0, "retryIntervalInSeconds": 30, "secureOutput": false, "secureInput": false}, "userProperties": [], "typeProperties": {"source": {"type": "BinarySource", "storeSettings": {"type": "AzureBlobFSReadSettings", "recursive": true, "deleteFilesAfterCompletion": true}, "formatSettings": {"type": "BinaryReadSettings"}}, "sink": {"type": "BinarySink", "storeSettings": {"type": "AzureBlobFSWriteSettings"}}, "enableStaging": false}, "inputs": [{"referenceName": "ADL_Binary_Dir", "type": "DatasetReference", "parameters": {"Path": "InstitutionalCapstockCashFlows/HSBC_Transactions/TEMP"}}], "outputs": [{"referenceName": "ADL_Binary_Dir", "type": "DatasetReference", "parameters": {"Path": {"value": "@concat('InstitutionalCapstockCashFlows/HSBC_Transactions/ARCHIVE/',pipeline().parameters.Date,'/TEMP')", "type": "Expression"}}}]}, {"name": "If Copy HSBC file to SCD SFTP", "type": "IfCondition", "dependsOn": [{"activity": "Create HSBC Trans File", "dependencyConditions": ["Succeeded"]}], "userProperties": [], "typeProperties": {"expression": {"value": "@pipeline().globalParameters.SendToSCD_InstitutionalCapstock", "type": "Expression"}, "ifTrueActivities": [{"name": "Copy HSBC file to SCD SFTP", "type": "Copy", "dependsOn": [], "policy": {"timeout": "7.00:00:00", "retry": 0, "retryIntervalInSeconds": 30, "secureOutput": false, "secureInput": false}, "userProperties": [], "typeProperties": {"source": {"type": "BinarySource", "storeSettings": {"type": "AzureBlobFSReadSettings", "recursive": false, "deleteFilesAfterCompletion": false}, "formatSettings": {"type": "BinaryReadSettings"}}, "sink": {"type": "BinarySink", "storeSettings": {"type": "SftpWriteSettings", "operationTimeout": "01:00:00", "useTempFileRename": true}}, "enableStaging": false}, "inputs": [{"referenceName": "ADL_Binary_Dir", "type": "DatasetReference", "parameters": {"Path": "InstitutionalCapstockCashFlows/HSBC_Transactions/OUT"}}], "outputs": [{"referenceName": "SCD_SFTP_Binary_Dir", "type": "DatasetReference", "parameters": {"Path": "@concat(pipeline().globalParameters.SCD_ENV,pipeline().globalParameters.SCD_SFTP_INSTITUTIONAL_CAPSTOCK)"}}]}]}}, {"name": "Create HSBC Trans File", "type": "Copy", "dependsOn": [{"activity": "Stage Data into ADX HSBC_Cash_Trans", "dependencyConditions": ["Succeeded"]}], "policy": {"timeout": "7.00:00:00", "retry": 0, "retryIntervalInSeconds": 30, "secureOutput": false, "secureInput": false}, "userProperties": [], "typeProperties": {"source": {"type": "AzureDataExplorerSource", "query": {"value": "@concat('HSBC_Cash_Transactions \n| where format_datetime(CreatedDate,\"yyyyMMdd\") == \"',pipeline().parameters.Date,'\" \nand Net<>0\n| summarize SumNet=sum(toreal(Net)) by Fund, Currency, SettleDate, CreatedDate\n| serialize rn=row_number()\n| project Portfolio=iff(Fund==505,\"1712649\",\"1712648\"), \nCurrency=Currency, Amount=tostring(round(abs(SumNet),2)), \n[\"Trans. code\"]=iif(SumNet<0,\"Withdraw\",\"Deposit\"), \n[\"Trade date\"]=format_datetime(SettleDate,\"yyyyMMdd\"), \n[\"Payment date\"]=format_datetime(SettleDate,\"yyyyMMdd\"), \n[\"External transaction number\"]=strcat(\"HSBCCAPSTOCK\",format_datetime(CreatedDate,\"yyyyMMdd\"),rn)')", "type": "Expression"}, "queryTimeout": "00:10:00", "noTruncation": false}, "sink": {"type": "DelimitedTextSink", "storeSettings": {"type": "AzureBlobFSWriteSettings"}, "formatSettings": {"type": "DelimitedTextWriteSettings", "quoteAllText": true, "fileExtension": ".txt"}}, "enableStaging": false, "translator": {"type": "TabularTranslator", "typeConversion": true, "typeConversionSettings": {"allowDataTruncation": true, "treatBooleanAsNumber": false}}}, "inputs": [{"referenceName": "ADX_TransactionOrder", "type": "DatasetReference", "parameters": {"Table": "HSBC_Cash_Transactions"}}], "outputs": [{"referenceName": "ADL_Delimited_File", "type": "DatasetReference", "parameters": {"FilePath": "InstitutionalCapstockCashFlows/HSBC_Transactions/OUT", "FileName": {"value": "@concat('HSBC_CAPSTOCK_',pipeline().parameters.Date,'.csv')", "type": "Expression"}, "ColDelimiter": ",", "FirstRowHeader": true}}]}], "parameters": {"Date": {"type": "string"}}, "variables": {"HSBCFileName": {"type": "String"}}, "folder": {"name": "HSBC/HSBC Institutional Capstock Cash Flows"}, "annotations": [], "lastPublishTime": "2023-12-01T04:12:15Z"}, "type": "Microsoft.DataFactory/factories/pipelines"}