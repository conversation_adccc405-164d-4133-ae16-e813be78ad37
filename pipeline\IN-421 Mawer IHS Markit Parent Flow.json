{"name": "IN-421 Mawer IHS Markit Parent Flow", "properties": {"activities": [{"name": "Execute Broker Flow", "type": "ExecutePipeline", "dependsOn": [{"activity": "Execute Allocation Flow", "dependencyConditions": ["Succeeded"]}], "policy": {"secureInput": false}, "userProperties": [], "typeProperties": {"pipeline": {"referenceName": "DECOM- IN-409 Mawer Broker Flow", "type": "PipelineReference"}, "waitOnCompletion": true}}, {"name": "Execute Mawer Exchange and Timezone Flow", "type": "ExecutePipeline", "dependsOn": [{"activity": "Execute Broker Flow", "dependencyConditions": ["Succeeded"]}], "policy": {"secureInput": false}, "userProperties": [], "typeProperties": {"pipeline": {"referenceName": "DECOM- IN-410 Mawer Exchange and timezone Flow", "type": "PipelineReference"}, "waitOnCompletion": true}}, {"name": "Execute Mawer Fills", "type": "ExecutePipeline", "dependsOn": [{"activity": "Execute Mawer Exchange and Timezone Flow", "dependencyConditions": ["Succeeded"]}], "policy": {"secureInput": false}, "userProperties": [], "typeProperties": {"pipeline": {"referenceName": "DECOM- IN-411 Mawer Fills Flow", "type": "PipelineReference"}, "waitOnCompletion": true}}, {"name": "Execute Mawer Fixincoming", "type": "ExecutePipeline", "dependsOn": [{"activity": "Execute Mawer Fills", "dependencyConditions": ["Succeeded"]}], "policy": {"secureInput": false}, "userProperties": [], "typeProperties": {"pipeline": {"referenceName": "DECOM- IN-412 Mawer Fixincoming Flow", "type": "PipelineReference"}, "waitOnCompletion": true}}, {"name": "Execute Mawer Fixoutgoing Flow", "type": "ExecutePipeline", "dependsOn": [{"activity": "Execute Mawer Fixincoming", "dependencyConditions": ["Succeeded"]}], "policy": {"secureInput": false}, "userProperties": [], "typeProperties": {"pipeline": {"referenceName": "DECOM- IN-413  Mawer Fixoutgoing Flow", "type": "PipelineReference"}, "waitOnCompletion": true}}, {"name": "Execute Mawer  GICS", "type": "ExecutePipeline", "dependsOn": [{"activity": "Execute Mawer Fixoutgoing Flow", "dependencyConditions": ["Succeeded"]}], "policy": {"secureInput": false}, "userProperties": [], "typeProperties": {"pipeline": {"referenceName": "DECOM- IN-414 Mawer GICS Flow", "type": "PipelineReference"}, "waitOnCompletion": true}}, {"name": "Execute Mawer Oderhistory Flow", "type": "ExecutePipeline", "dependsOn": [{"activity": "Execute Mawer  GICS", "dependencyConditions": ["Succeeded"]}], "policy": {"secureInput": false}, "userProperties": [], "typeProperties": {"pipeline": {"referenceName": "DECOM- IN-415 Mawer Orderhistory Flow", "type": "PipelineReference"}, "waitOnCompletion": true}}, {"name": "Execute Mawer Order Flow", "type": "ExecutePipeline", "dependsOn": [{"activity": "Execute Mawer Oderhistory Flow", "dependencyConditions": ["Succeeded"]}], "policy": {"secureInput": false}, "userProperties": [], "typeProperties": {"pipeline": {"referenceName": "DECOM- IN-416 Mawer Order Flow", "type": "PipelineReference"}, "waitOnCompletion": true}}, {"name": "Execute Mawer placement", "type": "ExecutePipeline", "dependsOn": [{"activity": "Execute Mawer Order Flow", "dependencyConditions": ["Succeeded"]}], "policy": {"secureInput": false}, "userProperties": [], "typeProperties": {"pipeline": {"referenceName": "DECOM- IN-417 Mawer Placement", "type": "PipelineReference"}, "waitOnCompletion": true}}, {"name": "Execute Mawer Security Asset class Flow", "type": "ExecutePipeline", "dependsOn": [{"activity": "Execute Mawer placement", "dependencyConditions": ["Succeeded"]}], "policy": {"secureInput": false}, "userProperties": [], "typeProperties": {"pipeline": {"referenceName": "DECOM- IN-419 Mawer Security Asset class Flow", "type": "PipelineReference"}, "waitOnCompletion": true}}, {"name": "Execute Mawer User and Usergroup Flow", "type": "ExecutePipeline", "dependsOn": [{"activity": "Execute Mawer Security Asset class Flow", "dependencyConditions": ["Succeeded"]}], "policy": {"secureInput": false}, "userProperties": [], "typeProperties": {"pipeline": {"referenceName": "DECOM- IN-420 Mawer User and Usergroup Flow", "type": "PipelineReference"}, "waitOnCompletion": true}}, {"name": "Email", "type": "WebActivity", "dependsOn": [{"activity": "Execute Mawer User and Usergroup Flow", "dependencyConditions": ["Skipped"]}], "policy": {"timeout": "7.00:00:00", "retry": 0, "retryIntervalInSeconds": 30, "secureOutput": false, "secureInput": false}, "userProperties": [], "typeProperties": {"method": "POST", "headers": {"Content-Type": "application/json"}, "url": {"value": "@pipeline().globalParameters.SendEmailLogicFunctionUrl", "type": "Expression"}, "body": {"value": "@concat('{\"BodyContents\":\"Greetings everybody,\n\n TCA Data Extracts were processed and sent to the SFTP server successfully.\n \n\nRegards,\nMawer Support Group.\",',\n'\"SubjectContents\":\"TCA Data Extract Success\",\"ToContents\"',\n':\"',\npipeline().globalParameters.ProdReportEmails,',<EMAIL>,<EMAIL>',\n'\"',\n' }'\n)", "type": "Expression"}}}, {"name": "Execute Allocation Flow", "type": "ExecutePipeline", "dependsOn": [], "policy": {"secureInput": false}, "userProperties": [], "typeProperties": {"pipeline": {"referenceName": "DECOM- IN-408 Mawer Allocation Flow", "type": "PipelineReference"}, "waitOnCompletion": true}}], "folder": {"name": "Internal/DDI/IN-329 DDI Migration/IN-384 Mawer IHSMarkit - TransactionCostAnalysis - Workflow"}, "annotations": [], "lastPublishTime": "2023-12-01T04:12:15Z"}, "type": "Microsoft.DataFactory/factories/pipelines"}