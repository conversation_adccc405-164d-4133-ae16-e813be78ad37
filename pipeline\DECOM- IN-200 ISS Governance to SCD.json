{"name": "DECOM- IN-200 ISS Governance to SCD", "properties": {"activities": [{"name": "IN-200 ISS Governance to Datalake", "type": "Copy", "dependsOn": [], "policy": {"timeout": "7.00:00:00", "retry": 0, "retryIntervalInSeconds": 30, "secureOutput": false, "secureInput": false}, "userProperties": [], "typeProperties": {"source": {"type": "SqlServerSource", "sqlReaderQuery": {"value": "@replace('SELECT [ID]\n      ,[Location Name]\n      ,[Location ID]\n      ,[Meeting ID]\n      ,[Ballot ID]\n      ,[Company]\n      ,[Meeting Date]\n      ,[Meeting Type]\n      ,[Country]\n      ,[Institutional Account Name]\n      ,[Custodian]\n      ,[Custodian Account Name]\n      ,[Custodian Account Number]\n      ,[Category Description]\n      ,[Proposal Type]\n      ,[Proposal Code]\n      ,[Proposal Description]\n      ,[Item Number]\n      ,[Proposal]\n      ,[Management Recommendation]\n      ,[Vote Cast]\n      ,[Vote For]\n      ,[Vote Against]\n      ,[Vote Abstain]\n      ,[Vote Withhold]\n      ,[DNV]\n      ,[One Year]\n      ,[Two Years]\n      ,[Three Years]\n      ,[Vote None]\n      ,[Vote Against Management]\n      ,[Vote With Management]\n      ,[Vote Against ISS]\n      ,[Vote with ISS]\n      ,[Vote Against Policy]\n      ,[Vote With Policy]\n      ,[LoadDate]\n  FROM [ExternalData].[dbo].[BallotLevelEquityFunds]\n  where [LoadDate] = CAST(? AS varchar) ','?',pipeline().parameters.runDate)", "type": "Expression"}, "queryTimeout": "02:00:00", "partitionOption": "None"}, "sink": {"type": "DelimitedTextSink", "storeSettings": {"type": "AzureBlobFSWriteSettings"}, "formatSettings": {"type": "DelimitedTextWriteSettings", "quoteAllText": true, "fileExtension": ".txt"}}, "enableStaging": false, "translator": {"type": "TabularTranslator", "typeConversion": true, "typeConversionSettings": {"allowDataTruncation": true, "treatBooleanAsNumber": false}}}, "inputs": [{"referenceName": "ODS_ExternalData_IFDS_ACCT_01", "type": "DatasetReference"}], "outputs": [{"referenceName": "AzureDataLakeStorage_ISS_Governance", "type": "DatasetReference", "parameters": {"subFolder": {"value": "@concat('ISS Governance/IN/',pipeline().parameters.runDate)", "type": "Expression"}, "fileName": "ballot_level_equity_funds.txt"}}]}, {"name": "IN-200 ISS Governance to SCD", "type": "Copy", "dependsOn": [{"activity": "IN-200 ISS Governance to Datalake", "dependencyConditions": ["Succeeded"]}], "policy": {"timeout": "7.00:00:00", "retry": 0, "retryIntervalInSeconds": 30, "secureOutput": false, "secureInput": false}, "userProperties": [], "typeProperties": {"source": {"type": "DelimitedTextSource", "storeSettings": {"type": "AzureBlobFSReadSettings", "recursive": true, "enablePartitionDiscovery": false}, "formatSettings": {"type": "DelimitedTextReadSettings"}}, "sink": {"type": "DelimitedTextSink", "storeSettings": {"type": "SftpWriteSettings", "operationTimeout": "01:00:00", "useTempFileRename": true}, "formatSettings": {"type": "DelimitedTextWriteSettings", "quoteAllText": true, "fileExtension": ".txt"}}, "enableStaging": false, "translator": {"type": "TabularTranslator", "typeConversion": true, "typeConversionSettings": {"allowDataTruncation": true, "treatBooleanAsNumber": false}}}, "inputs": [{"referenceName": "AzureDataLakeStorage_ISS_Governance", "type": "DatasetReference", "parameters": {"subFolder": "@concat('ISS Governance/IN/',pipeline().parameters.runDate)", "fileName": "ballot_level_equity_funds.txt"}}], "outputs": [{"referenceName": "SCD_SFTP_ISS_Governance", "type": "DatasetReference", "parameters": {"subFolder": {"value": "@concat(pipeline().globalParameters.SCD_ENV,'/ActiveData/DWH/ExtFiles')", "type": "Expression"}, "fileName": "ballot_level_equity_funds.txt"}}]}], "parameters": {"runDate": {"type": "string"}}, "folder": {"name": "Internal/ISS Governance to SCD"}, "annotations": [], "lastPublishTime": "2023-12-01T04:12:15Z"}, "type": "Microsoft.DataFactory/factories/pipelines"}