{"name": "IN108_IFDS_MAWFTRF_CAD_F_to_ADX", "properties": {"folder": {"name": "Inbound"}, "type": "MappingDataFlow", "typeProperties": {"sources": [{"dataset": {"referenceName": "ADL_IFDS_CashFlow_RAW_CSV", "type": "DatasetReference"}, "name": "LoadFileFromADL"}], "sinks": [{"dataset": {"referenceName": "ADX_ExternalIFDS_MAWFTRF_CAD_F", "type": "DatasetReference"}, "name": "ToADX"}], "transformations": [{"name": "AddingIDandLoadDate"}], "scriptLines": ["parameters{", "     pDate as string,", "     pADLRawFileDirectory as string", "}", "source(output(", "          {File Name} as string,", "          {Custodian Name} as string,", "          {Acct Grp Name} as string,", "          {Current Date} as string,", "          {Client Code} as string,", "          {Company Code} as string,", "          Currency as string,", "          {Cycle Date} as string,", "          {Fund Name} as string,", "          PricingLevel as string,", "          Fund as string,", "          {FundCustodianRef#} as string,", "          Pur as string,", "          ExchIn as string,", "          TfrIn as string,", "          Red as string,", "          ExchOut as string,", "          TfrOut as string,", "          CashDiv as string,", "          CashMFR as string,", "          <PERSON>m<PERSON><PERSON> as string,", "          Mgmt<PERSON>ee as string,", "          <PERSON><PERSON><PERSON><PERSON> as string,", "          <PERSON><PERSON><PERSON><PERSON><PERSON> as string,", "          NRTax as string,", "          NetCash as string,", "          {N$MPur} as string,", "          {N$MRed} as string,", "          {N$MPurRev} as string,", "          {N$MRedRev} as string,", "          ICTXls as string,", "          {N$MRej} as string,", "          ICTXlsRev as string,", "          {N$MRejRev} as string,", "          DivOut as string,", "          DivIn as string,", "          <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> as string,", "          <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> as string,", "          <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> as string,", "          <PERSON><PERSON>ee as string,", "          <PERSON>TH<PERSON><PERSON> as string,", "          FinalTrfReq as string", "     ),", "     allowSchemaDrift: true,", "     validateSchema: false,", "     ignoreNoFilesFound: false,", "     wildcardPaths:[(concat($pADLRawFileDirectory, '/MAWFTRF_CAD_F_*'))]) ~> LoadFileFromADL", "LoadFileFromADL derive(ID = uuid(),", "          LoadedDate = toDate($pDate,'yyyyMMdd')) ~> AddingIDandLoadDate", "AddingIDandLoadDate sink(allowSchemaDrift: true,", "     validateSchema: false,", "     format: 'table',", "     skipDuplicateMapInputs: true,", "     skipDuplicateMapOutputs: true,", "     saveOrder: 1,", "     mapColumn(", "          {File Name},", "          {<PERSON><PERSON>odian Name},", "          {Acct Grp Name},", "          {Current Date},", "          {Client Code},", "          {Company Code},", "          <PERSON><PERSON><PERSON><PERSON>,", "          {Cycle Date},", "          {Fund Name},", "          PricingLevel,", "          Fund,", "          FundCustodianRe = {FundCustodianRef#},", "          <PERSON><PERSON>,", "          ExchIn,", "          TfrIn,", "          Red,", "          ExchOut,", "          Tfr<PERSON>ut,", "          CashDiv,", "          CashMFR,", "          <PERSON><PERSON><PERSON><PERSON>,", "          MgmtFee,", "          <PERSON><PERSON><PERSON><PERSON>,", "          <PERSON><PERSON><PERSON><PERSON><PERSON>,", "          NRTax,", "          NetCash,", "          NMPur = {N$MPur},", "          NMRed = {N$MRed},", "          NMPurRev = {N$MPurRev},", "          NMRedRev = {N$MRedRev},", "          ICTXls,", "          NMRej = {N$MRej},", "          ICTXlsRev,", "          NMRejRev = {N$MRejRev},", "          Div<PERSON>ut,", "          DivIn,", "          <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,", "          <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,", "          <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,", "          <PERSON><PERSON><PERSON>,", "          <PERSON><PERSON><PERSON><PERSON>,", "          FinalTrfReq,", "          ID,", "          LoadedDate", "     )) ~> ToADX"]}}}