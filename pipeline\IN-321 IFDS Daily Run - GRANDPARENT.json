{"name": "IN-321 IFDS Daily Run - GRANDPARENT", "properties": {"activities": [{"name": "IN-85 IFDS Production Report WorkFlow", "type": "ExecutePipeline", "dependsOn": [{"activity": "IN-105 Pipeline_MAWSHORTTRD", "dependencyConditions": ["Succeeded"]}], "policy": {"secureInput": false}, "userProperties": [], "typeProperties": {"pipeline": {"referenceName": "DECOM- IN-85 IFDS Production Report WorkFlow", "type": "PipelineReference"}, "waitOnCompletion": true, "parameters": {"ADLRawFileDirectory": {"value": "@variables('ADLSourceDirectory')", "type": "Expression"}, "Date": {"value": "@variables('Date')", "type": "Expression"}}}}, {"name": "IN-91 IFDS Account", "type": "ExecutePipeline", "dependsOn": [{"activity": "IN-105 Pipeline_MAWSHORTTRD", "dependencyConditions": ["Succeeded"]}], "policy": {"secureInput": false}, "userProperties": [], "typeProperties": {"pipeline": {"referenceName": "IN-91 IFDS Account", "type": "PipelineReference"}, "waitOnCompletion": true, "parameters": {"ADLRawFileDirectory": {"value": "@variables('ADLSourceDirectory')", "type": "Expression"}, "Date": {"value": "@variables('Date')", "type": "Expression"}}}}, {"name": "IN-92 IFDS DEALER", "type": "ExecutePipeline", "dependsOn": [{"activity": "IN-105 Pipeline_MAWSHORTTRD", "dependencyConditions": ["Succeeded"]}], "policy": {"secureInput": false}, "userProperties": [], "typeProperties": {"pipeline": {"referenceName": "IN-92 IFDS DEALER", "type": "PipelineReference"}, "waitOnCompletion": true, "parameters": {"ADLRawFileDirectory": {"value": "@variables('ADLSourceDirectory')", "type": "Expression"}, "Date": {"value": "@variables('Date')", "type": "Expression"}}}}, {"name": "IN-101 External Files", "type": "ExecutePipeline", "dependsOn": [{"activity": "IN-105 Pipeline_MAWSHORTTRD", "dependencyConditions": ["Succeeded"]}], "policy": {"secureInput": false}, "userProperties": [], "typeProperties": {"pipeline": {"referenceName": "IN-101 External Files", "type": "PipelineReference"}, "waitOnCompletion": true, "parameters": {"ADLRawFileDirectory": {"value": "@variables('ADLSourceDirectory')", "type": "Expression"}, "Date": {"value": "@variables('Date')", "type": "Expression"}}}}, {"name": "ADLRawFilesDirectory", "type": "SetVariable", "dependsOn": [{"activity": "Set Date Variable", "dependencyConditions": ["Succeeded"]}], "policy": {"secureOutput": false, "secureInput": false}, "userProperties": [], "typeProperties": {"variableName": "ADLSourceDirectory", "value": {"value": "@concat('IFDS/Raw Files/',variables('Date'))", "type": "Expression"}}}, {"name": "Set Date Variable", "type": "SetVariable", "dependsOn": [{"activity": "FAILS if Not in PROD", "dependencyConditions": ["Succeeded"]}], "policy": {"secureOutput": false, "secureInput": false}, "userProperties": [], "typeProperties": {"variableName": "Date", "value": {"value": "@pipeline().parameters.FileDate", "type": "Expression"}}}, {"name": "IN-105 Pipeline_MAWSHORTTRD", "type": "ExecutePipeline", "dependsOn": [{"activity": "ADLRawFilesDirectory", "dependencyConditions": ["Succeeded"]}], "policy": {"secureInput": false}, "userProperties": [], "typeProperties": {"pipeline": {"referenceName": "IN-105 Pipeline_MAWSHORTTRD", "type": "PipelineReference"}, "waitOnCompletion": true, "parameters": {"ADLRawFileDirectory": {"value": "@variables('ADLSourceDirectory')", "type": "Expression"}, "Date": {"value": "@variables('Date')", "type": "Expression"}}}}, {"name": "FAILS if Not in PROD", "type": "IfCondition", "dependsOn": [], "userProperties": [], "typeProperties": {"expression": {"value": "@equals(pipeline().DataFactory,'AZ-PRD-UW-DF-SCD' )", "type": "Expression"}, "ifFalseActivities": [{"name": "Fail if Not Prod", "type": "Fail", "dependsOn": [], "userProperties": [], "typeProperties": {"message": "Fail for non-PROD envs", "errorCode": "500"}}]}}, {"name": "IFDS Load to ADX", "type": "ExecutePipeline", "dependsOn": [{"activity": "ADLRawFilesDirectory", "dependencyConditions": ["Succeeded"]}], "policy": {"secureInput": false}, "userProperties": [], "typeProperties": {"pipeline": {"referenceName": "IFDS Load to ADX", "type": "PipelineReference"}, "waitOnCompletion": true, "parameters": {"Date": {"value": "@variables('Date')", "type": "Expression"}}}}, {"name": "Send mail alert after completion", "type": "WebActivity", "dependsOn": [{"activity": "IFDS Trans ADX", "dependencyConditions": ["Succeeded"]}], "policy": {"timeout": "7.00:00:00", "retry": 0, "retryIntervalInSeconds": 30, "secureOutput": false, "secureInput": false}, "userProperties": [], "typeProperties": {"method": "POST", "headers": {"Content-Type": "application/json"}, "url": {"value": "@pipeline().globalParameters.SendEmailLogicFunctionUrl", "type": "Expression"}, "body": {"value": "@concat(\n    '{\n        \"BodyContents\":\"\",\n        \"SubjectContents\":\"IFDS TRN Load Completed\",\n        \"ToContents\"',':\"',pipeline().globalParameters.DDIMailAlerts,'\"',' \n    }'\n    )", "type": "Expression"}}}, {"name": "Send mail alert if it fails", "type": "WebActivity", "dependsOn": [{"activity": "IFDS Trans ADX", "dependencyConditions": ["Failed"]}], "policy": {"timeout": "7.00:00:00", "retry": 0, "retryIntervalInSeconds": 30, "secureOutput": false, "secureInput": false}, "userProperties": [], "typeProperties": {"method": "POST", "headers": {"Content-Type": "application/json"}, "url": {"value": "@pipeline().globalParameters.SendEmailLogicFunctionUrl", "type": "Expression"}, "body": {"value": "@concat(\n    '{\n        \"BodyContents\":\"\",\n        \"SubjectContents\":\"IFDS TRN Load Failed!\",\n        \"ToContents\"',':\"',pipeline().globalParameters.DDIMailAlerts,'\"',' \n    }'\n    )", "type": "Expression"}}}, {"name": "IFDS Trans ADX", "type": "ExecutePipeline", "dependsOn": [{"activity": "IFDS Load to ADX", "dependencyConditions": ["Succeeded"]}], "policy": {"secureInput": false}, "userProperties": [], "typeProperties": {"pipeline": {"referenceName": "IN-130 IFDS Trans", "type": "PipelineReference"}, "waitOnCompletion": true, "parameters": {"pDate": {"value": "@variables('Date')", "type": "Expression"}, "pADLPath": "IFDS/IFDS Transactions", "pADLContainer": "scdintegration", "pSendToSCD": true, "pSCDsftpPath": {"value": "@concat(pipeline().globalParameters.SCD_ENV,pipeline().globalParameters.SCD_SFTP_IFDS_TRN)", "type": "Expression"}, "pDataType": "X"}}}], "parameters": {"FileDate": {"type": "string"}}, "variables": {"ADLSourceDirectory": {"type": "String"}, "Date": {"type": "String"}}, "folder": {"name": "IFDS/IFDS Integration"}, "annotations": [], "lastPublishTime": "2024-04-03T18:21:41Z"}, "type": "Microsoft.DataFactory/factories/pipelines"}