{"name": "IMW-MI Parent Intraday", "properties": {"activities": [{"name": "CompleteLoad", "type": "ExecutePipeline", "dependsOn": [], "policy": {"secureInput": false}, "userProperties": [], "typeProperties": {"pipeline": {"referenceName": "DECOM- CompleteLoad", "type": "PipelineReference"}, "waitOnCompletion": true}}, {"name": "IncrementalLoad_Positions", "type": "ExecutePipeline", "dependsOn": [], "policy": {"secureInput": false}, "userProperties": [], "typeProperties": {"pipeline": {"referenceName": "IncrementalLoad_Positions", "type": "PipelineReference"}, "waitOnCompletion": true}}, {"name": "IncrementalLoad_Transactions", "type": "ExecutePipeline", "dependsOn": [], "policy": {"secureInput": false}, "userProperties": [], "typeProperties": {"pipeline": {"referenceName": "IncrementalLoad_Transactions", "type": "PipelineReference"}, "waitOnCompletion": true}}, {"name": "CompleteLoad_Part2", "type": "ExecutePipeline", "dependsOn": [], "policy": {"secureInput": false}, "userProperties": [], "typeProperties": {"pipeline": {"referenceName": "DECOM- CompleteLoad_Part2", "type": "PipelineReference"}, "waitOnCompletion": true}}, {"name": "Portfolio_Custodian_Complete_Load", "type": "ExecutePipeline", "dependsOn": [], "policy": {"secureInput": false}, "userProperties": [], "typeProperties": {"pipeline": {"referenceName": "Portfolio_Custodian_Complete_Load", "type": "PipelineReference"}, "waitOnCompletion": true}}, {"name": "IncrementalLoad_TRANSACTIONS_SUBLEDGER", "type": "ExecutePipeline", "dependsOn": [], "policy": {"secureInput": false}, "userProperties": [], "typeProperties": {"pipeline": {"referenceName": "IncrementalLoad_DM_TRANSACTIONS_SUBLEDGER", "type": "PipelineReference"}, "waitOnCompletion": true}}, {"name": "Exec SCDStaging SP", "type": "<PERSON><PERSON><PERSON>", "dependsOn": [{"activity": "CompleteLoad", "dependencyConditions": ["Succeeded"]}, {"activity": "IncrementalLoad_Positions", "dependencyConditions": ["Succeeded"]}, {"activity": "IncrementalLoad_Transactions", "dependencyConditions": ["Succeeded"]}, {"activity": "CompleteLoad_Part2", "dependencyConditions": ["Succeeded"]}, {"activity": "Portfolio_Custodian_Complete_Load", "dependencyConditions": ["Succeeded"]}, {"activity": "IncrementalLoad_TRANSACTIONS_SUBLEDGER", "dependencyConditions": ["Succeeded"]}], "policy": {"timeout": "0.12:00:00", "retry": 0, "retryIntervalInSeconds": 30, "secureOutput": false, "secureInput": false}, "userProperties": [], "linkedServiceName": {"referenceName": "AzureSQLManagedInstance_Prod", "type": "LinkedServiceReference", "parameters": {"dbName": "SCDStaging"}}, "typeProperties": {"scripts": [{"type": "Query", "text": "EXEC SCDStaging.dbo.SP_DM_POSITIONS_RECON_2BD;"}], "scriptBlockExecutionTimeout": "02:00:00"}}, {"name": "Exec IMW SP", "type": "<PERSON><PERSON><PERSON>", "dependsOn": [{"activity": "Exec SCDStaging SP", "dependencyConditions": ["Succeeded"]}], "policy": {"timeout": "0.12:00:00", "retry": 0, "retryIntervalInSeconds": 30, "secureOutput": false, "secureInput": false}, "userProperties": [], "linkedServiceName": {"referenceName": "MAWERP1D", "type": "LinkedServiceReference"}, "typeProperties": {"scripts": [{"type": "Query", "text": "BEGIN SCDWH.SP_DM_POSITIONS_RECON_2BD; END;"}], "scriptBlockExecutionTimeout": "02:00:00"}}, {"name": "Exec SCDStaging ReconLog Script", "type": "<PERSON><PERSON><PERSON>", "dependsOn": [{"activity": "Exec IMW SP", "dependencyConditions": ["Succeeded"]}], "policy": {"timeout": "0.12:00:00", "retry": 0, "retryIntervalInSeconds": 30, "secureOutput": false, "secureInput": false}, "userProperties": [], "linkedServiceName": {"referenceName": "AzureSQLManagedInstance_Prod", "type": "LinkedServiceReference", "parameters": {"dbName": "SCDStaging"}}, "typeProperties": {"scripts": [{"type": "Query", "text": "  insert into [SCDStaging].[dbo].[ReconLog]\n  select \n  [ProcessTS] = getdate(),\n  [TableName] = 'DM_POSITIONS (ORPHANS)',\n  [SourceCount] = (select top 1 [SourceCount] from [SCDStaging].[dbo].[ReconLog] where [TableName] = 'DM_POSITIONS' order by [ProcessTS] desc),\n  [TargetCount] = (select count(*) from [SCDStaging].[dbo].[dm_positions]),\n  [SourceSUM] = NULL,\n  [SourceSUM] = NULL,\n  [Status] = 'Success'\n \n"}], "scriptBlockExecutionTimeout": "02:00:00"}}, {"name": "Send mail alert after completion", "type": "WebActivity", "dependsOn": [{"activity": "Exec SCDStaging ReconLog Script", "dependencyConditions": ["Succeeded"]}], "policy": {"timeout": "7.00:00:00", "retry": 0, "retryIntervalInSeconds": 30, "secureOutput": false, "secureInput": false}, "userProperties": [], "typeProperties": {"method": "POST", "headers": {"Content-Type": "application/json"}, "url": {"value": "@pipeline().globalParameters.SendEmailLogicFunctionUrl", "type": "Expression"}, "body": {"value": "@concat(\n    '{\n        \"BodyContents\":\"\",\n        \"SubjectContents\":\"IMW MI Intraday Load Completed\",\n        \"ToContents\"',':\"',pipeline().globalParameters.DDIMailAlerts,'\"',' \n    }'\n    )", "type": "Expression"}}}, {"name": "Send mail alert for failure", "type": "WebActivity", "dependsOn": [{"activity": "Exec SCDStaging ReconLog Script", "dependencyConditions": ["Failed"]}], "policy": {"timeout": "7.00:00:00", "retry": 0, "retryIntervalInSeconds": 30, "secureOutput": false, "secureInput": false}, "userProperties": [], "typeProperties": {"method": "POST", "headers": {"Content-Type": "application/json"}, "url": {"value": "@pipeline().globalParameters.SendEmailLogicFunctionUrl", "type": "Expression"}, "body": {"value": "@concat(\n    '{\n        \"BodyContents\":\"\",\n        \"SubjectContents\":\"IMW MI Intra Day Load Failed!\",\n        \"ToContents\"',':\"',pipeline().globalParameters.DDIMailAlerts,'\"',' \n    }'\n    )", "type": "Expression"}}}], "folder": {"name": "Internal/IMW/IWM-MI CS CP"}, "annotations": [], "lastPublishTime": "2023-12-01T04:12:39Z"}, "type": "Microsoft.DataFactory/factories/pipelines"}