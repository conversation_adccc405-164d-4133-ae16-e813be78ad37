{"name": "DECOM- IN-384 Mawer IHSMarkit TransactionCostAnalysis - Workflow", "properties": {"activities": [{"name": "Generate mawer Broker source file", "type": "Copy", "dependsOn": [], "policy": {"timeout": "7.00:00:00", "retry": 0, "retryIntervalInSeconds": 30, "secureOutput": false, "secureInput": false}, "userProperties": [], "typeProperties": {"source": {"type": "SqlServerSource", "sqlReaderQuery": "SELECT\nBKR_CD brokercode,\nBKR_NAME brokername,\nBKR_TYP_CD brokertype,\nBKR_REASON_CD brokerreasoncode\nFROM\n    [MWR-SRV-crSQL1A].[CRDMAINMAWIM].[dbo].[CS_BROKER]", "queryTimeout": "02:00:00", "partitionOption": "None"}, "sink": {"type": "DelimitedTextSink", "storeSettings": {"type": "AzureBlobFSWriteSettings"}, "formatSettings": {"type": "DelimitedTextWriteSettings", "quoteAllText": true, "fileExtension": ".txt"}}, "enableStaging": false, "translator": {"type": "TabularTranslator", "typeConversion": true, "typeConversionSettings": {"allowDataTruncation": true, "treatBooleanAsNumber": false}}}, "inputs": [{"referenceName": "Mawer_IHSMarkit_SQL", "type": "DatasetReference"}], "outputs": [{"referenceName": "Mawer_broker_sink", "type": "DatasetReference"}]}, {"name": "Generate mawer FIxincoming source file", "type": "Copy", "dependsOn": [], "policy": {"timeout": "7.00:00:00", "retry": 0, "retryIntervalInSeconds": 30, "secureOutput": false, "secureInput": false}, "userProperties": [], "typeProperties": {"source": {"type": "SqlServerSource", "sqlReaderQuery": "SELECT        o.ORDER_ID AS orderid, fih.FIX_INCOMING_ID AS fixincomingid, fih.RECV_TIME AS fixreceivetime, fih.LAST_UPD_DATE AS fixlastupdatedate, fih.FIX_MSG_TYPE AS fixmessagetype,\n                         fih.PRICE_TYPE AS fixpricetype, fih.FACTOR AS fixfactor, fih.USER_DEFINED AS fixuserdefined, fih.PLACE_ID AS fixplaceid, fih.MSGSEQNUM AS fixmessageseqnumber, fih.LEAVESQTY AS fixleavesqty,\n                         fih.CASHORDERQTY AS fixcashorderqty, fih.EXECTYPE AS fixexecutiontype, fih.AVGPX AS fixaverageprice, fih.SID<PERSON> AS fixside, fih.SYMBOL AS fixsymbol, fih.LASTPX AS fixlastprice,\n                         fih.LASTSHARES AS fixlastshares, fih.CUMQTY AS fixcumulativequantity, fih.ORDERQTY AS fixorderqty, fih.SENDER_SUB_ID AS fixsendersubid, fih.SENDER_ID AS fixsenderid,\n                         fih.TARGET_SUB_ID AS fixtargetsubid, fih.FIX_BROKER_ID AS fixbrokerid, fih.SENDING_TIME AS fixsendingtime, fih.FIX_SUPP_ID AS fixsuppid, fih.FIX_ORIG_ID AS fixorigid, fih.FIX_ID AS fixid,\n                         fih.STATUS AS fixstatus, fih.TM_SUPP_ID AS fixsuppid2, fih.FIX_ORD_STATUS AS fixorderstatus, fih.FIX_TRANS_TYPE AS fixtranstype, fih.LAST_MKT, fih.LAST_CAPACITY, fih.LAST_LIQUIDITY_IND,\n                         o.TRADE_DATE AS tradedate\nFROM            [MWR-SRV-crSQL1A].[CRDMAINMAWIM].[dbo].[TS_ORDER] AS o\n INNER JOIN\n                         [MWR-SRV-crSQL1A].[CRDMAINMAWIM].[dbo].[FIX_INCOMING_HIST] AS fih \nON o.ORDER_ID = fih.TM_ORIG_ID", "queryTimeout": "02:00:00", "partitionOption": "None"}, "sink": {"type": "DelimitedTextSink", "storeSettings": {"type": "AzureBlobFSWriteSettings"}, "formatSettings": {"type": "DelimitedTextWriteSettings", "quoteAllText": true, "fileExtension": ".txt"}}, "enableStaging": false, "translator": {"type": "TabularTranslator", "typeConversion": true, "typeConversionSettings": {"allowDataTruncation": true, "treatBooleanAsNumber": false}}}, "inputs": [{"referenceName": "Mawer_IHSMarkit_SQL", "type": "DatasetReference"}], "outputs": [{"referenceName": "Mawer_IHStoADL", "type": "DatasetReference"}]}, {"name": "Generate mawer Fills source file", "type": "Copy", "dependsOn": [], "policy": {"timeout": "7.00:00:00", "retry": 0, "retryIntervalInSeconds": 30, "secureOutput": false, "secureInput": false}, "userProperties": [], "typeProperties": {"source": {"type": "SqlServerSource", "sqlReaderQuery": "SELECT        f.ORDER_ID AS orderid, f.<PERSON>LACEMENT_ID AS placementid, f.CREATE_DATE AS fillcreatedate, f.FILL_DATE AS filldate, f.FILL_BROKER AS fillbroker, f.FILL_QTY AS fillqty, f.FILL_PRICE AS fillprice,\n                         f.FILL_AMT AS fillamount, f.FIX_EXEC_ID AS fixexecutionid, f.FIX_INCOMING_ID AS fixincomingid, f.FIX_PLACE_DATE AS fixplacedate, f.FIX_FILL_DATE AS fixfilldate, f.ALGO_STRATEGY AS algostrategy,\n                         o.TRADE_DATE AS tradedate\nFROM            [MWR-SRV-crSQL1A].[CRDMAINMAWIM].[dbo].[TS_FILL] AS f\n INNER JOIN\n                         [MWR-SRV-crSQL1A].[CRDMAINMAWIM].[dbo].[TS_ORDER] AS o \nON f.ORDER_ID = o.ORDER_ID", "queryTimeout": "02:00:00", "partitionOption": "None"}, "sink": {"type": "DelimitedTextSink", "storeSettings": {"type": "AzureBlobFSWriteSettings"}, "formatSettings": {"type": "DelimitedTextWriteSettings", "quoteAllText": true, "fileExtension": ".txt"}}, "enableStaging": false, "translator": {"type": "TabularTranslator", "typeConversion": true, "typeConversionSettings": {"allowDataTruncation": true, "treatBooleanAsNumber": false}}}, "inputs": [{"referenceName": "Mawer_IHSMarkit_SQL", "type": "DatasetReference"}], "outputs": [{"referenceName": "Mawer_IHStoADL", "type": "DatasetReference"}]}, {"name": "Generate Mawer Exchange and timezone source file", "type": "Copy", "dependsOn": [], "policy": {"timeout": "7.00:00:00", "retry": 0, "retryIntervalInSeconds": 30, "secureOutput": false, "secureInput": false}, "userProperties": [], "typeProperties": {"source": {"type": "SqlServerSource", "sqlReaderQuery": "SELECT        e.<PERSON><PERSON><PERSON>_CD AS exchangeid, e.<PERSON><PERSON><PERSON>_NAME AS exchangename, e.<PERSON><PERSON><PERSON>_OTC AS exchangeorotc, e.CNTRY_CD AS exchangecountrycode, e.TIME_ZONE_RGN_CD AS exchangetimezoneregioncode,\n                         t.TIME_ZONE_NAME AS timezonename, t.TIME_ZONE_RGN_NAME AS timezoneregionname\nFROM            [MWR-SRV-crSQL1A].[CRDMAINMAWIM].[dbo].[CSM_EXCHANGE] AS e\n INNER JOIN\n                         [MWR-SRV-crSQL1A].[CRDMAINMAWIM].[dbo].[CSM_TIME_ZONES] AS t \nON e.TIME_ZONE_RGN_CD = t.TIME_ZONE_RGN_CD", "queryTimeout": "02:00:00", "partitionOption": "None"}, "sink": {"type": "DelimitedTextSink", "storeSettings": {"type": "AzureBlobFSWriteSettings"}, "formatSettings": {"type": "DelimitedTextWriteSettings", "quoteAllText": true, "fileExtension": ".txt"}}, "enableStaging": false, "translator": {"type": "TabularTranslator", "typeConversion": true, "typeConversionSettings": {"allowDataTruncation": true, "treatBooleanAsNumber": false}}}, "inputs": [{"referenceName": "Mawer_IHSMarkit_SQL", "type": "DatasetReference"}], "outputs": [{"referenceName": "Mawer_IHStoADL", "type": "DatasetReference"}]}, {"name": "Generate Mawer Fixoutgoing source file", "type": "Copy", "dependsOn": [], "policy": {"timeout": "7.00:00:00", "retry": 0, "retryIntervalInSeconds": 30, "secureOutput": false, "secureInput": false}, "userProperties": [], "typeProperties": {"source": {"type": "SqlServerSource", "sqlReaderQuery": "SELECT        o.ORDER_ID AS orderid, foh.FIX_OUTGOING_ID AS fixoutgoingid, foh.QUEUE_TIME AS fixqueuetime, foh.LAST_UPD_DATE AS fixlastupdatedate, foh.FIX_MSG_TYPE AS fixmessagetype,\n                         foh.STATUS AS fixmessagestatus, foh.SEND_TIME AS fixsendtime, foh.FIX_ORIG_ID AS fixorigid, foh.FIX_ORIG_BROKER_ID AS fixorigbrokerid, foh.FIX_CHAR_ID AS fixcharid, foh.TARGET_ID AS fixtargetid,\n                         foh.TARGET_SUB_ID AS fixtargetsubid, foh.SENDER_SUB_ID AS fixsendersubid, foh.ORDERQTY AS fixorderqty, foh.PRICE AS fixlimitprice, foh.STOPPX AS fixstopprice, foh.ORDTYPE AS fixordertype,\n                         foh.EXECINST AS fixexecutioninstruction, foh.HANDINST AS fixhandlinginstruction, foh.TIMEINFORCE AS fixtimeinforce, foh.EXPIRETIME AS fixexpiretime, foh.MINQTY AS fixminquantity,\n                         foh.MAXFLOOR AS fixmaxfloor, foh.SIDE AS fixorderside, foh.FIX_ORD_STATUS AS fixorderstatus, foh.SYMBOL AS fixsymbol, foh.FIX_SETTLMNTTYP AS fixsettlementtype,\n                         foh.FIX_EXDESTINATION AS fixexchangedestination, foh.SECURITYID AS fixsecurityid, foh.IDSOURCE AS fixsecurityidtype, foh.CURRENCY AS fixcurrency, foh.PLACE_ID AS placementid,\n                         foh.USER_DEFINED AS fixuserdefinedtags, foh.LAST_CUM_QTY AS fixlastcumulativeqty, o.TRADE_DATE AS tradedate\nFROM            [MWR-SRV-crSQL1A].[CRDMAINMAWIM].[dbo].[TS_ORDER] AS o \nINNER JOIN\n                         [MWR-SRV-crSQL1A].[CRDMAINMAWIM].[dbo].[FIX_OUTGOING_HIST] AS foh\n ON o.ORDER_ID = foh.ORDER_ID\n", "queryTimeout": "02:00:00", "partitionOption": "None"}, "sink": {"type": "DelimitedTextSink", "storeSettings": {"type": "AzureBlobFSWriteSettings"}, "formatSettings": {"type": "DelimitedTextWriteSettings", "quoteAllText": true, "fileExtension": ".txt"}}, "enableStaging": false, "translator": {"type": "TabularTranslator", "typeConversion": true, "typeConversionSettings": {"allowDataTruncation": true, "treatBooleanAsNumber": false}}}, "inputs": [{"referenceName": "Mawer_IHSMarkit_SQL", "type": "DatasetReference"}], "outputs": [{"referenceName": "Mawer_IHStoADL", "type": "DatasetReference"}]}, {"name": "Generate Mawer order history source file", "type": "Copy", "dependsOn": [], "policy": {"timeout": "7.00:00:00", "retry": 0, "retryIntervalInSeconds": 30, "secureOutput": false, "secureInput": false}, "userProperties": [], "typeProperties": {"source": {"type": "SqlServerSource", "sqlReaderQuery": "SELECT        o.ORDER_ID AS orderid, oh.ACT_TIMESTAMP AS actiondate, oh.PARENT_ORDER_ID AS parentorderid, oh.ACTION_TYPE AS actiontype, oh.USER_ID AS actionuserid, REPLACE(REPLACE(oh.MSG_TEXT, CHAR(10),\n                          ' '), <PERSON>AR(13), '') AS messagetext, oh.SHARES_OPEN AS sharesopen, oh.SHARES_ACTION AS sharesaction, oh.FUNC_FROM AS functionid, oh.SEQ AS seqid, oh.SCNDRY_ID AS secondaryid,\n                         oh.SCNDRY_ID_TYPE AS seconaryidtype, o.TRADE_DATE AS tradedate\nFROM            [MWR-SRV-crSQL1A].[CRDMAINMAWIM].[dbo].[TS_ORDER] AS o\n INNER JOIN\n                         [MWR-SRV-crSQL1A].[CRDMAINMAWIM].[dbo].[TS_ORDER_HISTORY] AS oh \nON o.ORDER_ID = oh.ORDER_ID", "queryTimeout": "02:00:00", "partitionOption": "None"}, "sink": {"type": "DelimitedTextSink", "storeSettings": {"type": "AzureBlobFSWriteSettings"}, "formatSettings": {"type": "DelimitedTextWriteSettings", "quoteAllText": true, "fileExtension": ".txt"}}, "enableStaging": false, "translator": {"type": "TabularTranslator", "typeConversion": true, "typeConversionSettings": {"allowDataTruncation": true, "treatBooleanAsNumber": false}}}, "inputs": [{"referenceName": "Mawer_IHSMarkit_SQL", "type": "DatasetReference"}], "outputs": [{"referenceName": "Mawer_IHStoADL", "type": "DatasetReference"}]}, {"name": "Generate Mawer order source file", "type": "Copy", "dependsOn": [], "policy": {"timeout": "7.00:00:00", "retry": 0, "retryIntervalInSeconds": 30, "secureOutput": false, "secureInput": false}, "userProperties": [], "typeProperties": {"source": {"type": "SqlServerSource", "sqlReaderQuery": "SELECT        o.ORDER_ID AS orderid, o.CREATE_DATE AS ordercreatedate, o.TO_TRADER_DATE AS orderreleasedate, o.TRANS_TYPE AS side, o.INSTRUCTION AS orderinstruction, o.LIMIT_PRICE AS managerlimit,\n                         o.TDR_LIMIT_PRICE AS traderlimit, o.FIX_STOPPX AS stopprice, o.EXEC_BROKER AS orderexecutingbroker, o.BROKER_REASON AS orderbrokerreason, o.IP<PERSON>, o.USR_CLASS_CD_3 AS secondary,\n                         o.STATUS AS orderstatus, o.ORDER_DURATION AS timeinforce, o.NET_TRADE_IND AS nettradeind, o.CREATE_USER AS createuser, o.MANAGER AS ordermanagerid, o.TRADER AS ordertraderid,\n                         o.TRADE_DATE AS tradedate, o.SETTLE_DATE AS settledate, o.SEC_ID AS crdinternalsecurityid, s.SEC_NAME AS securityname, s.CUSIP AS cusip, s.TICKER AS ticker, s.<PERSON><PERSON>OL AS sedol, s.ISIN_NO AS isin,\n                         s.<PERSON>L<PERSON>AN AS valoran, sc.UDF_CHAR8 AS bloombergid, s.SEC_TYP_CD AS securitytype, o.TARGET_QTY AS ordertargetqty, o.EXEC_QTY AS orderexecqty, o.EXEC_PRICE AS orderaverageprice,\n                         o.EXEC_AMT AS orderprinamount, o.COMMISION_AMT AS ordercommissionamt, o.FEE_1 + o.FEE_2 + o.FEE_3 + o.FEE_4 + o.FEE_5 + o.FEE_6 AS ordertotalfees, o.NET_PRIN_AMT AS ordernetamount,\n                         o.REASON_CD AS orderreason, s.ISSUE_CNTRY_CD AS countryofissue, s.CNTRY_OF_RISK AS countryofrisk, o.EXCH_CD AS exchangecode, o.PRIN_LOCAL_CRRNCY AS currencycode, oss.PROG_TRD_ID,\n                         CASE WHEN ISNULL(oss.PROG_TRD_ID, 0) = 0 THEN 'Regular' ELSE 'Program' END AS Program\nFROM                     [MWR-SRV-crSQL1A].[CRDMAINMAWIM].[dbo].[TS_ORDER] AS o\n                         INNER JOIN\n                         [MWR-SRV-crSQL1A].[CRDMAINMAWIM].[dbo].[CSM_SECURITY] AS s ON o.SEC_ID = s.SEC_ID\n\t\t\t\t\t\t INNER JOIN\n                         [MWR-SRV-crSQL1A].[CRDMAINMAWIM].[dbo].[TS_ORDER_SEC_SPEC] AS oss ON o.ORDER_ID = oss.ORDER_ID\n\t\t\t\t\t\t INNER JOIN\n                         [MWR-SRV-crSQL1A].[CRDMAINMAWIM].[dbo].[CSM_SECURITY_CUST] AS sc ON s.SEC_ID = sc.SEC_ID", "queryTimeout": "02:00:00", "partitionOption": "None"}, "sink": {"type": "DelimitedTextSink", "storeSettings": {"type": "AzureBlobFSWriteSettings"}, "formatSettings": {"type": "DelimitedTextWriteSettings", "quoteAllText": true, "fileExtension": ".txt"}}, "enableStaging": false, "translator": {"type": "TabularTranslator", "typeConversion": true, "typeConversionSettings": {"allowDataTruncation": true, "treatBooleanAsNumber": false}}}, "inputs": [{"referenceName": "Mawer_IHSMarkit_SQL", "type": "DatasetReference"}], "outputs": [{"referenceName": "Mawer_IHStoADL", "type": "DatasetReference"}]}, {"name": "Generate Mawer Security asset class source file", "type": "Copy", "dependsOn": [], "policy": {"timeout": "7.00:00:00", "retry": 0, "retryIntervalInSeconds": 30, "secureOutput": false, "secureInput": false}, "userProperties": [], "typeProperties": {"source": {"type": "SqlServerSource", "sqlReaderQuery": "SELECT        s.SEC_ID, s.SEC_NAME, t1.COMMENTS AS 'SECURITY_ASSET_CLASS'\nFROM            [MWR-SRV-crSQL1A].[CRDMAINMAWIM].[dbo].[CSM_SECURITY] AS s LEFT OUTER JOIN\n                         [MWR-SRV-crSQL1A].[CRDMAINMAWIM].[dbo].[CSM_TRANSLATION] AS t1\n\t\t\t\t\t\t ON s.UDF_CHAR3 = t1.TRADE_FLD_CD\n\t\t\t\t\t\t AND t1.CS_FLD_CD = 'DESC'\n\t\t\t\t\t\t AND t1.TRADE_SYST_CD = 'MIMEDW' \n\t\t\t\t\t\t AND t1.DATA_TYP = 'LZMAT3' \n\t\t\t\t\t\t AND t1.DIRECTION = 'E'", "queryTimeout": "02:00:00", "partitionOption": "None"}, "sink": {"type": "DelimitedTextSink", "storeSettings": {"type": "AzureBlobFSWriteSettings"}, "formatSettings": {"type": "DelimitedTextWriteSettings", "quoteAllText": true, "fileExtension": ".txt"}}, "enableStaging": false, "translator": {"type": "TabularTranslator", "typeConversion": true, "typeConversionSettings": {"allowDataTruncation": true, "treatBooleanAsNumber": false}}}, "inputs": [{"referenceName": "Mawer_IHSMarkit_SQL", "type": "DatasetReference"}], "outputs": [{"referenceName": "Mawer_IHStoADL", "type": "DatasetReference"}]}, {"name": "Generate Mawer user and usergroup source file", "type": "Copy", "dependsOn": [], "policy": {"timeout": "7.00:00:00", "retry": 0, "retryIntervalInSeconds": 30, "secureOutput": false, "secureInput": false}, "userProperties": [], "typeProperties": {"source": {"type": "SqlServerSource", "sqlReaderQuery": "SELECT        u.USER_CD AS userid, u.INIT AS userinitials, ug.GRP_CD AS usergroup\nFROM            [MWR-SRV-crSQL1A].[CRDMAINMAWIM].[dbo].[PDF_USER] AS u \nINNER JOIN\n                         [MWR-SRV-crSQL1A].[CRDMAINMAWIM].[dbo].[PDF_USER_GROUP] AS ug \n\t\t\t\t\t\t ON u.USER_CD = ug.USER_CD", "queryTimeout": "02:00:00", "partitionOption": "None"}, "sink": {"type": "DelimitedTextSink", "storeSettings": {"type": "AzureBlobFSWriteSettings"}, "formatSettings": {"type": "DelimitedTextWriteSettings", "quoteAllText": true, "fileExtension": ".txt"}}, "enableStaging": false, "translator": {"type": "TabularTranslator", "typeConversion": true, "typeConversionSettings": {"allowDataTruncation": true, "treatBooleanAsNumber": false}}}, "inputs": [{"referenceName": "Mawer_IHSMarkit_SQL", "type": "DatasetReference"}], "outputs": [{"referenceName": "Mawer_IHStoADL", "type": "DatasetReference"}]}, {"name": "Generate Mawer placement source file", "type": "Copy", "dependsOn": [], "policy": {"timeout": "7.00:00:00", "retry": 0, "retryIntervalInSeconds": 30, "secureOutput": false, "secureInput": false}, "userProperties": [], "typeProperties": {"source": {"type": "SqlServerSource", "sqlReaderQuery": "SELECT        o.ORDER_ID AS orderid, op.PLACE_ID AS placeid, op.EXEC_BROKER AS placebrokerid, op.PLACE_DATE AS placedate, op.BROKER_REASON AS placebrokerreason, op.CREATE_DATE AS placecreatedate,\n                         op.CREATE_USER AS placecreateuser, op.PLACE_QTY AS placeqty, op.EXEC_QTY AS placeexecamt, op.EXEC_PRICE AS placeexecprice, op.EXEC_AMT AS placeexecamt2, op.INSTRUCTION AS placeinstruction,\n                         op.LIMIT_PRICE AS placelimitprice, op.PLACE_DURATION AS placetimeinforce, op.COMMENTS AS placecomments, op.NET_TRADE_IND AS placenettradeindicator, op.FIX_ORDER_ID AS placefixorderid,\n                         op.FIX_CLORDID AS placefixclientorderid, o.TRADE_DATE AS tradedate\nFROM            [MWR-SRV-crSQL1A].[CRDMAINMAWIM].[dbo].[TS_ORDER] AS o INNER JOIN\n                         [MWR-SRV-crSQL1A].[CRDMAINMAWIM].[dbo].[TS_ORDER_PLACEMENT] AS op ON o.ORDER_ID = op.ORDER_ID", "queryTimeout": "02:00:00", "partitionOption": "None"}, "sink": {"type": "SqlServerSink"}, "enableStaging": false, "translator": {"type": "TabularTranslator", "typeConversion": true, "typeConversionSettings": {"allowDataTruncation": true, "treatBooleanAsNumber": false}}}, "inputs": [{"referenceName": "Mawer_IHSMarkit_SQL", "type": "DatasetReference"}], "outputs": [{"referenceName": "Mawer_IHSMarkit_SQL", "type": "DatasetReference"}]}, {"name": "Generate Mawer gics file", "type": "Copy", "dependsOn": [], "policy": {"timeout": "7.00:00:00", "retry": 0, "retryIntervalInSeconds": 30, "secureOutput": false, "secureInput": false}, "userProperties": [], "typeProperties": {"source": {"type": "SqlServerSource", "sqlReaderQuery": "SELECT        s.SEC_ID, s.SEC_NAME, a.GicsSector_ID, a.GicsIndustryGroup_ID, a.GicsIndustry_ID, a.GicsSubIndustry_ID, a.GicsSector_Name, a.GicsIndustryGroup_Name, a.GicsIndustry_Name,\n                         a.GicsSubIndustry_Name\nFROM            [MWR-SRV-crSQL1A].[CRDMAINMAWIM].[dbo].[CSM_SECURITY] AS s INNER JOIN\n                             (SELECT DISTINCT\n                                                         i.SEC_ID, i1.INDUST_CD AS 'GicsSector_ID', i2.INDUST_CD AS 'GicsIndustryGroup_ID', i3.INDUST_CD AS 'GicsIndustry_ID', i4.INDUST_CD AS 'GicsSubIndustry_ID',\n                                                         g1.INDUST_NAME AS 'GicsSector_Name', g2.INDUST_NAME AS 'GicsIndustryGroup_Name', g3.INDUST_NAME AS 'GicsIndustry_Name', g4.INDUST_NAME AS 'GicsSubIndustry_Name'\n                               FROM            [MWR-SRV-crSQL1A].[CRDMAINMAWIM].[dbo].[CSM_SECURITY_INDUSTRY] AS i \n\t\t\t\t\t\t\t   INNER JOIN\n                                                         [MWR-SRV-crSQL1A].[CRDMAINMAWIM].[dbo].[CSM_SECURITY_INDUSTRY] AS i1 ON i.SEC_ID = i1.SEC_ID AND i1.INDUST_CLASS_CD = 'GSECT' INNER JOIN\n                                                         [MWR-SRV-crSQL1A].[CRDMAINMAWIM].[dbo].[CSM_SECURITY_INDUSTRY] AS i2 ON i.SEC_ID = i2.SEC_ID AND i2.INDUST_CLASS_CD = 'GINDG' INNER JOIN\n                                                         [MWR-SRV-crSQL1A].[CRDMAINMAWIM].[dbo].[CSM_SECURITY_INDUSTRY] AS i3 ON i.SEC_ID = i3.SEC_ID AND i3.INDUST_CLASS_CD = 'GIND' INNER JOIN\n                                                         [MWR-SRV-crSQL1A].[CRDMAINMAWIM].[dbo].[CSM_SECURITY_INDUSTRY] AS i4 ON i.SEC_ID = i4.SEC_ID AND i4.INDUST_CLASS_CD = 'GSIND' INNER JOIN\n                                                         [MWR-SRV-crSQL1A].[CRDMAINMAWIM].[dbo].[CSM_INDUSTRY] AS g1 ON i1.INDUST_CD = g1.INDUST_CD AND g1.INDUST_CLASS_CD = 'GSECT' INNER JOIN\n                                                         [MWR-SRV-crSQL1A].[CRDMAINMAWIM].[dbo].[CSM_INDUSTRY] AS g2 ON i2.INDUST_CD = g2.INDUST_CD AND g2.INDUST_CLASS_CD = 'GINDG' INNER JOIN\n                                                         [MWR-SRV-crSQL1A].[CRDMAINMAWIM].[dbo].[CSM_INDUSTRY] AS g3 ON i3.INDUST_CD = g3.INDUST_CD AND g3.INDUST_CLASS_CD = 'GIND' INNER JOIN\n                                                         [MWR-SRV-crSQL1A].[CRDMAINMAWIM].[dbo].[CSM_INDUSTRY] AS g4 ON i4.INDUST_CD = g4.INDUST_CD AND g4.INDUST_CLASS_CD = 'GSIND') AS a\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t ON s.SEC_ID = a.SEC_ID\nORDER BY s.SEC_NAME", "queryTimeout": "02:00:00", "partitionOption": "None"}, "sink": {"type": "DelimitedTextSink", "storeSettings": {"type": "AzureBlobFSWriteSettings"}, "formatSettings": {"type": "DelimitedTextWriteSettings", "quoteAllText": true, "fileExtension": ".txt"}}, "enableStaging": false, "translator": {"type": "TabularTranslator", "typeConversion": true, "typeConversionSettings": {"allowDataTruncation": true, "treatBooleanAsNumber": false}}}, "inputs": [{"referenceName": "Mawer_IHSMarkit_SQL", "type": "DatasetReference"}], "outputs": [{"referenceName": "Mawer_IHStoADL", "type": "DatasetReference"}]}], "folder": {"name": "Internal/DDI/IN-329 DDI Migration/IN-384 Mawer IHSMarkit - TransactionCostAnalysis - Workflow"}, "annotations": [], "lastPublishTime": "2023-12-01T04:11:07Z"}, "type": "Microsoft.DataFactory/factories/pipelines"}