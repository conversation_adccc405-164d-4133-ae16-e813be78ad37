{"name": "DEPRECATED_SR-87 - BMO Position", "properties": {"folder": {"name": "Inbound"}, "type": "MappingDataFlow", "typeProperties": {"sources": [{"dataset": {"referenceName": "ADL_BMO_IN", "type": "DatasetReference"}, "name": "BMOSource"}, {"dataset": {"referenceName": "ADL_BMO_IN", "type": "DatasetReference"}, "name": "BMOSourceHeader"}, {"dataset": {"referenceName": "ADL_BMO_TEMP", "type": "DatasetReference"}, "name": "PortfolioCodeCustodianData"}], "sinks": [{"dataset": {"referenceName": "ADL_BMO_TEMP", "type": "DatasetReference"}, "name": "OutputToCSV"}], "transformations": [{"name": "ParseOutColumns"}, {"name": "SelectFields"}, {"name": "FilterBlankPositionDate"}, {"name": "DerivedHeaderColumns"}, {"name": "FilterToGetFirstRow"}, {"name": "JoinOnFileName"}, {"name": "CastColumn3AsBusinessDate"}, {"name": "JoinWithCustodianData"}, {"name": "PortfolioCodeCustodianMapping", "description": "Creates an explicit mapping for each drifted column"}], "script": "parameters{\n\tFileName as string\n}\nsource(output(\n\t\t{_col0_} as string,\n\t\t{_col1_} as string,\n\t\t{_col2_} as string,\n\t\t{_col3_} as string,\n\t\t{_col4_} as string,\n\t\t{_col5_} as string,\n\t\t{_col6_} as string,\n\t\t{_col7_} as string,\n\t\t{_col8_} as string,\n\t\t{_col9_} as string,\n\t\t{_col10_} as string,\n\t\t{_col11_} as string,\n\t\t{_col12_} as string,\n\t\t{_col13_} as string,\n\t\t{_col14_} as string,\n\t\t{_col15_} as string,\n\t\t{_col16_} as string,\n\t\t{_col17_} as string,\n\t\t{_col18_} as string,\n\t\t{_col19_} as string,\n\t\t{_col20_} as string,\n\t\t{_col21_} as string,\n\t\t{_col22_} as short,\n\t\t{_col23_} as string,\n\t\t{_col24_} as string,\n\t\t{_col25_} as string,\n\t\t{_col26_} as string,\n\t\t{_col27_} as double,\n\t\t{_col28_} as double\n\t),\n\tallowSchemaDrift: true,\n\tvalidateSchema: false,\n\tignoreNoFilesFound: false,\n\trowUrlColumn: 'FileNameDirectory',\n\tskipLines: 1,\n\twildcardPaths:[(concat('Custodian Data/BMO/IN/', $FileName))]) ~> BMOSource\nsource(output(\n\t\tColumn_1 as string,\n\t\tColumn_2 as string,\n\t\tColumn_3 as string\n\t),\n\tallowSchemaDrift: true,\n\tvalidateSchema: false,\n\tignoreNoFilesFound: false,\n\twildcardPaths:[(concat('Custodian Data/BMO/IN/', $FileName))]) ~> BMOSourceHeader\nsource(output(\n\t\tColumn_1 as string,\n\t\tColumn_2 as string,\n\t\tColumn_3 as string\n\t),\n\tallowSchemaDrift: true,\n\tvalidateSchema: false,\n\tignoreNoFilesFound: false,\n\twildcardPaths:[('Custodian Data/Static/PortfolioCodeCustodianMapping.csv')]) ~> PortfolioCodeCustodianData\nBMOSource derive({Record Type} = {_col0_},\n\t\t{Account Number} = iif({_col21_} == 'Y', {_col4_}, {_col1_}),\n\t\t{Account Check Digit} = {_col2_},\n\t\t{Account Type Code} = {_col3_},\n\t\t{Portfolio Number} = {_col4_},\n\t\t{Account Fund / Portfolio Currency} = {_col5_},\n\t\t{Internal Security Identifier} = {_col6_},\n\t\t{Security ID} = {_col7_},\n\t\t{Sec ID} = case(length(trim({_col7_})) > 0, {_col7_}, length(trim({_col6_})) > 0, {_col6_}),\n\t\t{Security ID Type} = {_col8_},\n\t\t{Security Symbol} = {_col9_},\n\t\t{Security Currency} = {_col10_},\n\t\t{Cost of the investments (Invested Amount)} = {_col11_},\n\t\t{Currency of Invested Amount} = {_col12_},\n\t\t{Number Of Units`} = {_col13_},\n\t\t{Price Per Unit} = {_col14_},\n\t\t{Market Value} = {_col15_},\n\t\tSign = iif(toDouble({_col15_}) > 0, 'C', 'D'),\n\t\t{Position Date} = concat(split({_col16_},'-')[2], '/', split({_col16_},'-')[3], '/',split({_col16_},'-')[1]),\n\t\t{Margin Rate} = {_col17_},\n\t\t{Market Value in Account Base Currency} = {_col18_},\n\t\t{Settlement Date Cash Balance} = {_col19_},\n\t\t{Buying Power} = {_col20_},\n\t\t{Is Currency Flag} = {_col21_},\n\t\t{Master Account/Client Number} = {_col22_},\n\t\t{Back Office Account Number} = {_col23_},\n\t\t{Internal Position Identifier} = {_col24_},\n\t\t{BOR System} = {_col25_},\n\t\t{Line of Business} = {_col26_},\n\t\t{Accrued Interest} = {_col27_},\n\t\t{Accrued Interest Base Currency} = {_col28_},\n\t\tFileNameDirectory = FileNameDirectory,\n\t\tFileName = replace($FileName, '/Custodian%20Data/BMO/IN/', '')) ~> ParseOutColumns\nCastColumn3AsBusinessDate select(mapColumn(\n\t\tFileName = FileNameDirectory,\n\t\t{Record Type},\n\t\t{Account Number},\n\t\t{Account Check Digit},\n\t\t{Account Type Code},\n\t\t{Portfolio Number},\n\t\t{Account Fund / Portfolio Currency},\n\t\t{Internal Security Identifier},\n\t\t{Security ID},\n\t\t{Security ID Type},\n\t\t{Security Symbol},\n\t\t{Security Currency},\n\t\t{Cost of the investments (Invested Amount)},\n\t\t{Currency of Invested Amount},\n\t\t{Number Of Units} = {Number Of Units`},\n\t\t{Price Per Unit},\n\t\t{Market Value},\n\t\t{Position Date},\n\t\t{Margin Rate},\n\t\t{Market Value in Account Base Currency},\n\t\t{Settlement Date Cash Balance},\n\t\t{Buying Power},\n\t\t{Is Currency Flag},\n\t\t{Master Account/Client Number},\n\t\t{Back Office Account Number},\n\t\t{Internal Position Identifier},\n\t\t{BOR System},\n\t\t{Line of Business},\n\t\t{Accrued Interest},\n\t\t{Accrued Interest Base Currency},\n\t\t{Sec ID},\n\t\tSign,\n\t\t{Business Date},\n\t\tSSID\n\t),\n\tskipDuplicateMapInputs: true,\n\tskipDuplicateMapOutputs: true) ~> SelectFields\nParseOutColumns filter(toString({Position Date}) != '') ~> FilterBlankPositionDate\nBMOSourceHeader derive(column1 = Column_1,\n\t\tColumn_3 = Column_3,\n\t\tColumn_2 = $FileName) ~> DerivedHeaderColumns\nDerivedHeaderColumns filter(Column_1 == 'HDR') ~> FilterToGetFirstRow\nFilterBlankPositionDate, FilterToGetFirstRow join(FileName == Column_2,\n\tjoinType:'inner',\n\tmatchType:'exact',\n\tignoreSpaces: false,\n\tbroadcast: 'auto')~> JoinOnFileName\nJoinWithCustodianData derive({Business Date} = replace(DerivedHeaderColumns@Column_3, '-','')) ~> CastColumn3AsBusinessDate\nJoinOnFileName, PortfolioCodeCustodianMapping join(trim({Portfolio Number}) == trim(CustodianAccount),\n\tjoinType:'left',\n\tmatchType:'exact',\n\tignoreSpaces: false,\n\tbroadcast: 'auto')~> JoinWithCustodianData\nPortfolioCodeCustodianData derive(SSID = toString(byName('SSID')),\n\t\tCustodianAccount = toString(byName('CustodianAccount'))) ~> PortfolioCodeCustodianMapping\nSelectFields sink(allowSchemaDrift: true,\n\tvalidateSchema: false,\n\tinput(\n\t\tColumn_1 as string,\n\t\tColumn_2 as string,\n\t\tColumn_3 as string\n\t),\n\tpartitionFileNames:['tmpBMOPosition.csv'],\n\tumask: 0022,\n\tpreCommands: [],\n\tpostCommands: [],\n\tskipDuplicateMapInputs: true,\n\tskipDuplicateMapOutputs: true,\n\tpartitionBy('hash', 1)) ~> OutputToCSV"}}}