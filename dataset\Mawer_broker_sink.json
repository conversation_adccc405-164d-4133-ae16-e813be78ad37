{"name": "Mawer_broker_sink", "properties": {"linkedServiceName": {"referenceName": "AzureDataLakeStorage", "type": "LinkedServiceReference"}, "folder": {"name": "Inbound/DDI/IN-329 DDI Migration/IN-384 Mawer IHSMarkit - TransactionCostAnalysis - Workflow"}, "annotations": [], "type": "DelimitedText", "typeProperties": {"location": {"type": "AzureBlobFSLocation", "folderPath": "DDI Migration/Mawer IHSMarkit - workflow/Outputfiles", "fileSystem": "scdintegration"}, "columnDelimiter": "|", "escapeChar": "\\", "firstRowAsHeader": true, "quoteChar": "\""}, "schema": [{"name": "brokercode", "type": "String"}, {"name": "brokername", "type": "String"}, {"name": "brokertype", "type": "String"}, {"name": "brokerreasoncode", "type": "String"}]}, "type": "Microsoft.DataFactory/factories/datasets"}