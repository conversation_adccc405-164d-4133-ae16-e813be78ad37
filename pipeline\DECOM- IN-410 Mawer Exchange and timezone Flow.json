{"name": "DECOM- IN-410 Mawer Exchange and timezone Flow", "properties": {"activities": [{"name": "Set FileName", "type": "SetVariable", "dependsOn": [], "policy": {"secureOutput": false, "secureInput": false}, "userProperties": [], "typeProperties": {"variableName": "FileName", "value": {"value": "@concat('Mawer Exchange and Timezone_',formatDateTime(convertFromUtc(adddays(utcnow(),-1),'Mountain Standard Time'),'MMddyy'),'.txt')", "type": "Expression"}}}, {"name": "Generate Mawer Exchange and timezone source file", "type": "Copy", "dependsOn": [{"activity": "Set FileName", "dependencyConditions": ["Succeeded"]}], "policy": {"timeout": "7.00:00:00", "retry": 0, "retryIntervalInSeconds": 30, "secureOutput": false, "secureInput": false}, "userProperties": [], "typeProperties": {"source": {"type": "SqlServerSource", "sqlReaderQuery": "SELECT\ne.<PERSON><PERSON>CH_CD exchangeid,\ne.<PERSON><PERSON><PERSON>_NAME exchangename,\ne.<PERSON><PERSON><PERSON>_OTC exchangeorotc,\ne.CNTRY_CD exchangecountrycode,\n--exchangemiccode,\ne.TIME_ZONE_RGN_CD exchangetimezoneregioncode,\nt.TIME_ZONE_NAME timezonename,\nt.TIME_ZONE_RGN_NAME timezoneregionname\nFROM\n     [MWR-SRV-crSQL1A].[CRDMAINMAWIM].[dbo].[CSM_EXCHANGE] e,\n     [MWR-SRV-crSQL1A].[CRDMAINMAWIM].[dbo].[CSM_TIME_ZONES] t\n\t \nWHERE\ne.TIME_ZONE_RGN_CD = t.TIME_ZONE_RGN_CD", "queryTimeout": "02:00:00", "partitionOption": "None"}, "sink": {"type": "DelimitedTextSink", "storeSettings": {"type": "AzureBlobFSWriteSettings"}, "formatSettings": {"type": "DelimitedTextWriteSettings", "quoteAllText": true, "fileExtension": ".txt"}}, "enableStaging": false, "translator": {"type": "TabularTranslator", "typeConversion": true, "typeConversionSettings": {"allowDataTruncation": true, "treatBooleanAsNumber": false}}}, "inputs": [{"referenceName": "ODSExternalData_MawerIHSMarkit_sql", "type": "DatasetReference"}], "outputs": [{"referenceName": "AzureDataLakeStorage_MawerIHSmarkitsink_csv", "type": "DatasetReference", "parameters": {"FileName": {"value": "@variables('FileName')", "type": "Expression"}}}]}, {"name": "Archive", "type": "Copy", "dependsOn": [{"activity": "Generate Mawer Exchange and timezone source file", "dependencyConditions": ["Succeeded"]}], "policy": {"timeout": "7.00:00:00", "retry": 0, "retryIntervalInSeconds": 30, "secureOutput": false, "secureInput": false}, "userProperties": [], "typeProperties": {"source": {"type": "BinarySource", "storeSettings": {"type": "AzureBlobFSReadSettings", "recursive": false, "wildcardFolderPath": "DDI Migration/Mawer IHSMarkit - workflow/Outputfiles", "wildcardFileName": {"value": "@variables('FileName')", "type": "Expression"}, "deleteFilesAfterCompletion": true}, "formatSettings": {"type": "BinaryReadSettings"}}, "sink": {"type": "BinarySink", "storeSettings": {"type": "AzureBlobFSWriteSettings"}}, "enableStaging": false}, "inputs": [{"referenceName": "AzureDataLakeStorageMawerIHSMarkit_Binary_Source", "type": "DatasetReference"}], "outputs": [{"referenceName": "AzureDataLakeStorage_MawerIHSMarkit_Binary_Sink", "type": "DatasetReference"}]}], "variables": {"FileName": {"type": "String"}}, "folder": {"name": "Internal/DDI/IN-329 DDI Migration/IN-384 Mawer IHSMarkit - TransactionCostAnalysis - Workflow"}, "annotations": [], "lastPublishTime": "2023-12-01T04:11:29Z"}, "type": "Microsoft.DataFactory/factories/pipelines"}