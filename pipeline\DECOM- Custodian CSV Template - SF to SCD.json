{"name": "DECOM- Custodian CSV Template - SF to SCD", "properties": {"description": "Integrate Custodies data from salesforce to SCD\nhttps://beboringmakedata.atlassian.net/browse/IN-395", "activities": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "type": "Switch", "dependsOn": [{"activity": "Set File Name", "dependencyConditions": ["Succeeded"]}], "userProperties": [], "typeProperties": {"on": {"value": "@pipeline().parameters.ObjectName", "type": "Expression"}, "cases": [{"value": "FinancialAccount", "activities": [{"name": "Copy custodies data", "type": "Copy", "dependsOn": [], "policy": {"timeout": "7.00:00:00", "retry": 0, "retryIntervalInSeconds": 30, "secureOutput": false, "secureInput": false}, "userProperties": [], "typeProperties": {"source": {"type": "SqlMISource", "sqlReaderQuery": {"value": "@concat(\n'Select [PortfolioCode] ,[Party] ,[Party Name] ,[LEI] ,[Issuer identification (Issuer Identifications)] ,[Issuer identification name (Issuer Identifications)] ,[Country] ,[Risk exposure country] ,[Region (IK)] ,[Business classification (Business Class Time Series)] ,[From date (Business Class Time Series)] ,[Business class level 5 (Business Class Time Series)] ,[Rating agency (Ratings)] ,[From date (Ratings)] ,[Short rating (Ratings)] ,[Short term outlook date (Ratings)] ,[Short term outlook (Ratings)] ,[Long rating (Ratings)] ,[Long term outlook date (Ratings)] ,[Long term outlook (Ratings)] ,[Endorsement indicator (Ratings)] ,[Remark 1 (Remarks)] ,[Remark 2 (Remarks)] ,[Remark 3 (Remarks)] ,[Remark 4 (Remarks)] ,[Remark 5 (Remarks)] ,[Approved for securities lending transactions] ,[Approved for buy/sellback transactions] ,[Approved for straight repo transactions] ,[Agency] ,[DTC (#1540)] ,[ISO10383 - MIC] ,[SWIFT address (SWIFT)] ,[<PERSON>ustod<PERSON>] ,[Counterparty] ,[Bank] ,[Broker] ,[Mar<PERSON> clearer] ,[Client] ,[Clearinghouse] ,[Portfolio manager] ,[Guarantor] ,[Lender] ,[Syndicate member] ,[Borrower] ,[Lead manager] ,[Property owner] ,[Account manager] ,[Tax office] ,[Central government] ,[Fire insurance company] ,[Payment place] ,[Index provider] ,[Delivery place] ,[Paying agent] ,[Data provider] ,[Non-bank counterparty] ,[Trading platform] ,[CCP] ,[Agent] ,[Sponsor] ,[Internal legal entity] ,[Regulatory service provider] ,[Tri-party agent] ,[Legal owner] ,[Issuer] ,[CTM Broker] ,[CTM Alloc Matching] ,[Settle. Instr. Route] ,[CTM ID] ,[Valid for trading] ,[Asset class (Broker)] ,[Generic Cash Brokers] ,[Generic Algo Brokers] ,[Parent Party] ,[Sub-acronym (OASYS/CTM)] from  [scd].[vCustodianChange] where FinancialAccountId in (',pipeline().parameters.ObjectId,')')", "type": "Expression"}, "partitionOption": "None"}, "sink": {"type": "DelimitedTextSink", "storeSettings": {"type": "AzureBlobFSWriteSettings"}, "formatSettings": {"type": "DelimitedTextWriteSettings", "quoteAllText": true, "fileExtension": ".txt"}}, "enableStaging": false, "translator": {"type": "TabularTranslator", "typeConversion": true, "typeConversionSettings": {"allowDataTruncation": true, "treatBooleanAsNumber": false}}}, "inputs": [{"referenceName": "SalesforceViews", "type": "DatasetReference"}], "outputs": [{"referenceName": "AzureDataLakeStorage_SalesforceToScd_PortfolioFile", "type": "DatasetReference", "parameters": {"FileName": {"value": "@variables('FileName')", "type": "Expression"}, "FolderName": {"value": "@concat('Salesforce To SCD/Daily Loads/',formatDateTime(convertFromUtc(utcnow(),'Mountain Standard Time'),'yyyyMMdd'))", "type": "Expression"}}}]}]}]}}, {"name": "Set File Name", "type": "SetVariable", "dependsOn": [], "policy": {"secureOutput": false, "secureInput": false}, "userProperties": [], "typeProperties": {"variableName": "FileName", "value": {"value": "@concat('Custodian_',formatDateTime(convertFromUtc(utcnow(),'Mountain Standard Time'),'MMddyyyy'),'_',formatDateTime(convertFromUtc(utcnow(),'Mountain Standard Time'),'hhmmss'),'.csv')", "type": "Expression"}}}, {"name": "Copy File to SCD SFTP", "type": "Copy", "dependsOn": [{"activity": "<PERSON><PERSON><PERSON><PERSON>", "dependencyConditions": ["Succeeded"]}], "policy": {"timeout": "7.00:00:00", "retry": 0, "retryIntervalInSeconds": 30, "secureOutput": false, "secureInput": false}, "userProperties": [], "typeProperties": {"source": {"type": "BinarySource", "storeSettings": {"type": "AzureBlobFSReadSettings", "recursive": true}, "formatSettings": {"type": "BinaryReadSettings"}}, "sink": {"type": "BinarySink", "storeSettings": {"type": "SftpWriteSettings", "operationTimeout": "01:00:00", "useTempFileRename": true}}, "enableStaging": false}, "inputs": [{"referenceName": "AzureDataLakeStorage_Portfolio_Binary", "type": "DatasetReference", "parameters": {"SourceDirectory": {"value": "@concat('Salesforce To SCD/Daily Loads/',formatDateTime(convertFromUtc(utcnow(),'Mountain Standard Time'),'yyyyMMdd'))", "type": "Expression"}, "FileName": {"value": "@variables('FileName')", "type": "Expression"}}}], "outputs": [{"referenceName": "SFTP_SCD_DIR_BINARY", "type": "DatasetReference", "parameters": {"Path": {"value": "@concat(pipeline().globalParameters.SCD_ENV,pipeline().globalParameters.SCD_SFTP_Portfolio)", "type": "Expression"}}}]}], "parameters": {"ObjectId": {"type": "string"}, "ObjectName": {"type": "string"}}, "variables": {"FileName": {"type": "String"}}, "folder": {"name": "Internal/SFtoSCD/SF-SCD Real Time"}, "annotations": [], "lastPublishTime": "2023-12-01T04:11:48Z"}, "type": "Microsoft.DataFactory/factories/pipelines"}