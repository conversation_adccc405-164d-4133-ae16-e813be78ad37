{"name": "IN-106 Pipeline_FNDTRNF0", "properties": {"activities": [{"name": "FNDTRNF0", "type": "GetMetadata", "dependsOn": [], "policy": {"timeout": "7.00:00:00", "retry": 0, "retryIntervalInSeconds": 30, "secureOutput": false, "secureInput": false}, "userProperties": [], "typeProperties": {"dataset": {"referenceName": "AzureDataLakeStorage_IFDS_RawFiles_IN_FNDTRNF0", "type": "DatasetReference", "parameters": {"ADLRawFileDirectory": {"value": "@pipeline().parameters.ADLRawFileDirectory", "type": "Expression"}}}, "fieldList": ["exists"], "storeSettings": {"type": "AzureBlobFSReadSettings", "recursive": true, "enablePartitionDiscovery": false}, "formatSettings": {"type": "DelimitedTextReadSettings"}}}, {"name": "If Condition_FNDTRNF0", "type": "IfCondition", "dependsOn": [{"activity": "FNDTRNF0", "dependencyConditions": ["Succeeded"]}], "userProperties": [], "typeProperties": {"expression": {"value": "@activity('FNDTRNF0').output.exists", "type": "Expression"}, "ifTrueActivities": [{"name": "Execute Pipeline_FNDTRNF0", "type": "ExecutePipeline", "dependsOn": [], "policy": {"secureInput": false}, "userProperties": [], "typeProperties": {"waitOnCompletion": true, "parameters": {"FunctionBody": "{\"FileName\":\"FNDTRNF0\"}"}}}, {"name": "Copy data from External Files", "type": "Copy", "dependsOn": [{"activity": "Set Date", "dependencyConditions": ["Succeeded"]}], "policy": {"timeout": "7.00:00:00", "retry": 0, "retryIntervalInSeconds": 30, "secureOutput": false, "secureInput": false}, "userProperties": [], "typeProperties": {"source": {"type": "DelimitedTextSource", "storeSettings": {"type": "AzureBlobFSReadSettings", "recursive": true, "enablePartitionDiscovery": false}, "formatSettings": {"type": "DelimitedTextReadSettings"}}, "sink": {"type": "AzureDataExplorerSink"}, "enableStaging": false, "translator": {"type": "TabularTranslator", "mappings": [{"source": {"name": "type", "type": "String"}, "sink": {"name": "type", "type": "String"}}, {"source": {"name": "grossamount", "type": "String"}, "sink": {"name": "grossamount", "type": "String"}}, {"source": {"name": "net", "type": "String"}, "sink": {"name": "net", "type": "String"}}, {"source": {"name": "commission", "type": "String"}, "sink": {"name": "commission", "type": "String"}}, {"source": {"name": "count", "type": "String"}, "sink": {"name": "count", "type": "String"}}, {"source": {"name": "Report_date", "type": "String"}, "sink": {"name": "Report_date", "type": "String"}}, {"source": {"name": "Loaded", "type": "String"}, "sink": {"name": "Loaded", "type": "DateTime"}}, {"source": {"name": "RowId", "type": "String"}, "sink": {"name": "RowId", "type": "String"}}]}}, "inputs": [{"referenceName": "AzureDataLakeStorage_IFDS_ExternalFiles_FNDTRN0", "type": "DatasetReference", "parameters": {"fileName": {"value": "@concat('FNDTRNF0-',variables('vDate'),'.csv')", "type": "Expression"}}}], "outputs": [{"referenceName": "ADX_ExternalIFDS", "type": "DatasetReference", "parameters": {"Table": "FNDTRNF0"}}]}, {"name": "Set Date", "type": "SetVariable", "dependsOn": [{"activity": "Execute Pipeline_FNDTRNF0", "dependencyConditions": ["Succeeded"]}], "policy": {"secureOutput": false, "secureInput": false}, "userProperties": [], "typeProperties": {"variableName": "vDate", "value": {"value": "@concat(substring(pipeline().parameters.Date,6,2),'-',\nsubstring(pipeline().parameters.Date,4,2),'-',\nsubstring(pipeline().parameters.Date,0,4) )\n", "type": "Expression"}}}]}}], "parameters": {"ADLRawFileDirectory": {"type": "string"}, "Date": {"type": "string"}}, "variables": {"vDate": {"type": "String"}}, "folder": {"name": "IFDS/IFDS Integration/IN-101-ExternalFIles"}, "annotations": [], "lastPublishTime": "2023-12-01T04:11:17Z"}, "type": "Microsoft.DataFactory/factories/pipelines"}