{"name": "SISBallot_SINK", "properties": {"linkedServiceName": {"referenceName": "AzureDataLakeStorage", "type": "LinkedServiceReference"}, "folder": {"name": "Inbound/SIS Report - Ballot Level Detail"}, "annotations": [], "type": "DelimitedText", "typeProperties": {"location": {"type": "AzureBlobFSLocation", "folderPath": {"value": "@concat('SIS Report Ballot Level Detail/OutFile/')", "type": "Expression"}, "fileSystem": "scdintegration"}, "columnDelimiter": ",", "escapeChar": "\\", "firstRowAsHeader": true, "quoteChar": "\""}, "schema": []}, "type": "Microsoft.DataFactory/factories/datasets"}