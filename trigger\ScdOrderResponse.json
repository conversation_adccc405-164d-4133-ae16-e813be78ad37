{"name": "ScdOrderResponse", "properties": {"annotations": [], "runtimeState": "Stopped", "pipeline": {"pipelineReference": {"referenceName": "DECOM- Transfer-Order-SCD", "type": "PipelineReference"}}, "type": "TumblingWindowTrigger", "typeProperties": {"frequency": "Minute", "interval": 5, "startTime": "2021-10-12T18:00:00Z", "delay": "00:00:00", "maxConcurrency": 50, "retryPolicy": {"intervalInSeconds": 30}, "dependsOn": []}}}