{"name": "OrderResponseTrigger", "properties": {"annotations": [], "runtimeState": "Stopped", "pipelines": [{"pipelineReference": {"referenceName": "DECOM- Transfer-Order-SCD", "type": "PipelineReference"}}], "type": "BlobEventsTrigger", "typeProperties": {"blobPathBeginsWith": "/fsintegration/blobs/TFS/OutgoingToSCD/IN/", "ignoreEmptyBlobs": true, "scope": "/subscriptions/7425054e-704e-4fc4-a7a4-f85852178405/resourceGroups/AZ-DEV-UW-RG-DataLake/providers/Microsoft.Storage/storageAccounts/azdevuwstodatalake", "events": ["Microsoft.Storage.BlobCreated"]}}}