{"name": "DECOM- IN-414 Mawer GICS Flow", "properties": {"activities": [{"name": "Generate Mawer gics file", "type": "Copy", "dependsOn": [{"activity": "Set variable1", "dependencyConditions": ["Succeeded"]}], "policy": {"timeout": "7.00:00:00", "retry": 0, "retryIntervalInSeconds": 30, "secureOutput": false, "secureInput": false}, "userProperties": [], "typeProperties": {"source": {"type": "SqlServerSource", "sqlReaderQuery": "SELECT        s.SEC_ID, s.SEC_NAME, a.GicsSector_ID, a.GicsIndustryGroup_ID, a.GicsIndustry_ID, a.GicsSubIndustry_ID, a.GicsSector_Name, a.GicsIndustryGroup_Name, a.GicsIndustry_Name,\n                         a.GicsSubIndustry_Name\nFROM            [MWR-SRV-crSQL1A].[CRDMAINMAWIM].[dbo].[CSM_SECURITY] AS s INNER JOIN\n                             (SELECT DISTINCT\n                                                         i.SEC_ID, i1.INDUST_CD AS 'GicsSector_ID', i2.INDUST_CD AS 'GicsIndustryGroup_ID', i3.INDUST_CD AS 'GicsIndustry_ID', i4.INDUST_CD AS 'GicsSubIndustry_ID',\n                                                         g1.INDUST_NAME AS 'GicsSector_Name', g2.INDUST_NAME AS 'GicsIndustryGroup_Name', g3.INDUST_NAME AS 'GicsIndustry_Name', g4.INDUST_NAME AS 'GicsSubIndustry_Name'\n                               FROM            [MWR-SRV-crSQL1A].[CRDMAINMAWIM].[dbo].[CSM_SECURITY_INDUSTRY] AS i \n\t\t\t\t\t\t\t   INNER JOIN\n                                                         [MWR-SRV-crSQL1A].[CRDMAINMAWIM].[dbo].[CSM_SECURITY_INDUSTRY] AS i1 ON i.SEC_ID = i1.SEC_ID AND i1.INDUST_CLASS_CD = 'GSECT' INNER JOIN\n                                                         [MWR-SRV-crSQL1A].[CRDMAINMAWIM].[dbo].[CSM_SECURITY_INDUSTRY] AS i2 ON i.SEC_ID = i2.SEC_ID AND i2.INDUST_CLASS_CD = 'GINDG' INNER JOIN\n                                                         [MWR-SRV-crSQL1A].[CRDMAINMAWIM].[dbo].[CSM_SECURITY_INDUSTRY] AS i3 ON i.SEC_ID = i3.SEC_ID AND i3.INDUST_CLASS_CD = 'GIND' INNER JOIN\n                                                         [MWR-SRV-crSQL1A].[CRDMAINMAWIM].[dbo].[CSM_SECURITY_INDUSTRY] AS i4 ON i.SEC_ID = i4.SEC_ID AND i4.INDUST_CLASS_CD = 'GSIND' INNER JOIN\n                                                         [MWR-SRV-crSQL1A].[CRDMAINMAWIM].[dbo].[CSM_INDUSTRY] AS g1 ON i1.INDUST_CD = g1.INDUST_CD AND g1.INDUST_CLASS_CD = 'GSECT' INNER JOIN\n                                                         [MWR-SRV-crSQL1A].[CRDMAINMAWIM].[dbo].[CSM_INDUSTRY] AS g2 ON i2.INDUST_CD = g2.INDUST_CD AND g2.INDUST_CLASS_CD = 'GINDG' INNER JOIN\n                                                         [MWR-SRV-crSQL1A].[CRDMAINMAWIM].[dbo].[CSM_INDUSTRY] AS g3 ON i3.INDUST_CD = g3.INDUST_CD AND g3.INDUST_CLASS_CD = 'GIND' INNER JOIN\n                                                         [MWR-SRV-crSQL1A].[CRDMAINMAWIM].[dbo].[CSM_INDUSTRY] AS g4 ON i4.INDUST_CD = g4.INDUST_CD AND g4.INDUST_CLASS_CD = 'GSIND') AS a\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t ON s.SEC_ID = a.SEC_ID\nORDER BY s.SEC_NAME", "queryTimeout": "02:00:00", "partitionOption": "None"}, "sink": {"type": "DelimitedTextSink", "storeSettings": {"type": "AzureBlobFSWriteSettings"}, "formatSettings": {"type": "DelimitedTextWriteSettings", "quoteAllText": true, "fileExtension": ".txt"}}, "enableStaging": false, "translator": {"type": "TabularTranslator", "typeConversion": true, "typeConversionSettings": {"allowDataTruncation": true, "treatBooleanAsNumber": false}}}, "inputs": [{"referenceName": "ODSExternalData_MawerIHSMarkit_sql", "type": "DatasetReference"}], "outputs": [{"referenceName": "AzureDataLakeStorage_MawerIHSmarkitsink_csv", "type": "DatasetReference", "parameters": {"FileName": {"value": "@variables('FileName')", "type": "Expression"}}}]}, {"name": "Set variable1", "type": "SetVariable", "dependsOn": [], "policy": {"secureOutput": false, "secureInput": false}, "userProperties": [], "typeProperties": {"variableName": "FileName", "value": {"value": "@concat('Mawer GICS_',formatDateTime(convertFromUtc(adddays(utcnow(),-1),'Mountain Standard Time'),'MMddyy'),'.txt')", "type": "Expression"}}}, {"name": "Archive", "type": "Copy", "dependsOn": [{"activity": "Generate Mawer gics file", "dependencyConditions": ["Succeeded"]}], "policy": {"timeout": "7.00:00:00", "retry": 0, "retryIntervalInSeconds": 30, "secureOutput": false, "secureInput": false}, "userProperties": [], "typeProperties": {"source": {"type": "BinarySource", "storeSettings": {"type": "AzureBlobFSReadSettings", "recursive": false, "wildcardFolderPath": "DDI Migration/Mawer IHSMarkit - workflow/Outputfiles", "wildcardFileName": {"value": "@variables('FileName')", "type": "Expression"}, "deleteFilesAfterCompletion": true}, "formatSettings": {"type": "BinaryReadSettings"}}, "sink": {"type": "BinarySink", "storeSettings": {"type": "AzureBlobFSWriteSettings"}}, "enableStaging": false}, "inputs": [{"referenceName": "AzureDataLakeStorageMawerIHSMarkit_Binary_Source", "type": "DatasetReference"}], "outputs": [{"referenceName": "AzureDataLakeStorage_MawerIHSMarkit_Binary_Sink", "type": "DatasetReference"}]}], "variables": {"FileName": {"type": "String"}}, "folder": {"name": "Internal/DDI/IN-329 DDI Migration/IN-384 Mawer IHSMarkit - TransactionCostAnalysis - Workflow"}, "annotations": [], "lastPublishTime": "2023-12-01T04:11:07Z"}, "type": "Microsoft.DataFactory/factories/pipelines"}