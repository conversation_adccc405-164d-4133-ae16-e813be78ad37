{"name": "DECOM- IN-632 Sharepoint data push into db", "properties": {"activities": [{"name": "Update Sharepoint List Data in Azure SQL", "type": "Copy", "dependsOn": [], "policy": {"timeout": "7.00:00:00", "retry": 0, "retryIntervalInSeconds": 30, "secureOutput": false, "secureInput": false}, "userProperties": [], "typeProperties": {"source": {"type": "SqlMISource", "sqlReaderQuery": {"value": "select\n '@{pipeline().parameters.ID}' as ID,\n '@{pipeline().parameters.Language}' as Language,\n '@{pipeline().parameters.LanguageName}' as LanguageName,\n '@{pipeline().parameters.Description}' as Description", "type": "Expression"}, "partitionOption": "None"}, "sink": {"type": "SqlMISink", "writeBehavior": "upsert", "upsertSettings": {"useTempDB": true, "keys": ["ID", "Language", "LanguageName"]}, "sqlWriterUseTableLock": false}, "enableStaging": false, "translator": {"type": "TabularTranslator", "mappings": [{"source": {"name": "ID", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}, "sink": {"name": "ID", "type": "String", "physicalType": "n<PERSON><PERSON><PERSON>"}}, {"source": {"name": "Language", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}, "sink": {"name": "Language", "type": "String", "physicalType": "n<PERSON><PERSON><PERSON>"}}, {"source": {"name": "LanguageName", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}, "sink": {"name": "LanguageName", "type": "String", "physicalType": "n<PERSON><PERSON><PERSON>"}}, {"source": {"name": "Description", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}, "sink": {"name": "Description", "type": "String", "physicalType": "n<PERSON><PERSON><PERSON>"}}], "typeConversion": true, "typeConversionSettings": {"allowDataTruncation": true, "treatBooleanAsNumber": false}}}, "inputs": [{"referenceName": "AzureSqlMI_ReportsLogic_ReportingTranslation", "type": "DatasetReference"}], "outputs": [{"referenceName": "AzureSqlMI_ReportsLogic_ReportingTranslation", "type": "DatasetReference"}]}], "parameters": {"ID": {"type": "string", "defaultValue": "Q1_2022_Canadian Clients_English_USD_1"}, "Language": {"type": "string", "defaultValue": "en-US"}, "LanguageName": {"type": "string", "defaultValue": "English"}, "Description": {"type": "string", "defaultValue": "Cover letters description for Q1 ENGLISH USD"}}, "folder": {"name": "Internal/DDI/IN-329 DDI Migration/IN-632 Sharepoint data push into DB"}, "annotations": [], "lastPublishTime": "2023-12-01T04:11:32Z"}, "type": "Microsoft.DataFactory/factories/pipelines"}