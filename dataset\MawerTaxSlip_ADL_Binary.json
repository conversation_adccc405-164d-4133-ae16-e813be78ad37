{"name": "MawerTaxSlip_ADL_Binary", "properties": {"linkedServiceName": {"referenceName": "AzureDataLakeStorage", "type": "LinkedServiceReference"}, "parameters": {"Directory": {"type": "string"}, "FileName": {"type": "string"}}, "folder": {"name": "Inbound/DDI/IN-329 DDI Migration/IN-508 Mawer Tax Slip"}, "annotations": [], "type": "Binary", "typeProperties": {"location": {"type": "AzureBlobFSLocation", "fileName": {"value": "@dataset().FileName", "type": "Expression"}, "folderPath": {"value": "@dataset().Directory", "type": "Expression"}, "fileSystem": "scdintegration"}}}, "type": "Microsoft.DataFactory/factories/datasets"}