{"name": "IN92_IFDS_DEALER_DF", "properties": {"folder": {"name": "Inbound"}, "type": "MappingDataFlow", "typeProperties": {"sources": [{"dataset": {"referenceName": "AzurDataLakeStorage_IFDS_DEALER_RAW", "type": "DatasetReference"}, "name": "RawFile"}], "sinks": [{"dataset": {"referenceName": "AzurDataLakeStorage_IFDS_DEALER_TEMP", "type": "DatasetReference"}, "name": "TempFile"}], "transformations": [{"name": "AddedTwoColumns"}, {"name": "SelecteRequiredColumn"}], "script": "parameters{\n\toutFileName as string,\n\trawFileName as string,\n\tADLFilesDirectory as string,\n\tDate as string\n}\nsource(output(\n\t\t{Record Type} as string,\n\t\t{Dealer Code} as string,\n\t\t{Dealer Name1} as string,\n\t\t{Dealer Name2} as string,\n\t\t{Dealer Address Line 1} as string,\n\t\t{Dealer Address Line 2} as string,\n\t\t{Dealer Address Line 3} as string,\n\t\t{Dealer ProvinceOrState} as string,\n\t\t{Dealer Postal Code} as string,\n\t\t{Dealer Country} as string,\n\t\t{Dealer Phone Number} as string,\n\t\t{User Id} as string,\n\t\t{Add Date} as string,\n\t\t{Change Date} as string,\n\t\t{Company Code} as string,\n\t\t{Dealer Address Line 4} as string,\n\t\t{Dealer Address Line 5} as string,\n\t\tDeff as string,\n\t\t{Stop Date} as string,\n\t\tContact as string,\n\t\t{Contact Fax} as string,\n\t\t{contact Email} as string,\n\t\tPhone as string,\n\t\t{Dealer Otherids} as string,\n\t\t{Fax Agreement} as string,\n\t\t{Fund Rep} as string,\n\t\t{Dealer error account number} as string,\n\t\tCompetitor as string,\n\t\t{System contact} as string,\n\t\t{Service fee contact} as string,\n\t\t{Nominee Name} as string,\n\t\t{Second Fax number} as string,\n\t\t{Fax contact person} as string,\n\t\t{Agency Agreement} as string,\n\t\t{Non-Res Agreement} as string,\n\t\t{Electronic Switch Agreement} as string,\n\t\t{Fax Agreement 2} as string,\n\t\t{Net Settlement Power of Attorney} as string,\n\t\t{Software provider} as string,\n\t\t{Wire Order Redemption Agreement} as string,\n\t\t{National Account Code} as string,\n\t\t{Top Firm} as string,\n\t\t{Trailer CSS Rpt Option} as string,\n\t\tCorrespondent as string,\n\t\t{Flag for F File} as string,\n\t\t{Flag for T File} as string,\n\t\t{Rpt Trail FOF} as string,\n\t\t{Cheques To this dealer} as string,\n\t\tCity as string,\n\t\t{Outstanding Commission Currency} as string,\n\t\t{Outstanding Commission CAD} as string,\n\t\t{Outstanding Commission Currency 2} as string,\n\t\t{Outstanding Commission USD} as string,\n\t\t{Flag Generation W File} as string,\n\t\t{Confirm Broker Copy} as string,\n\t\t{Broker Type} as string,\n\t\t{CSS Status} as string,\n\t\t{Flag Generation M File} as string,\n\t\t{Volume Settlement Type} as string,\n\t\t{AMS Eligible} as string,\n\t\t{Flag Generation S File} as string,\n\t\t{Days to Settle for WO Red} as string,\n\t\t{CRA Business } as string,\n\t\t{Client Account Transfer Agreement} as string,\n\t\t{Phone Toll Free} as string,\n\t\t{Trailer Payment Frequency} as string,\n\t\t{Dealer Admin Designation} as string,\n\t\t{Dealer NAT Account Indicator} as string,\n\t\t{ProvinceOrState Alpha Code} as string,\n\t\t{Numeric Country Code} as string,\n\t\t{Ownership ID} as string,\n\t\t{KYC Regulation} as string,\n\t\t{KYC Mandate} as string,\n\t\t{KYCSignture-StoreReq} as string,\n\t\t{KYC Status} as string,\n\t\tEPA as string,\n\t\t{Territory Code} as string,\n\t\tUUID as string,\n\t\tGUID as string,\n\t\tCM as string,\n\t\t{NM Add Money Move} as string,\n\t\t{NM Nom Error Corr} as string,\n\t\t{Tplus1 MM Wire} as string,\n\t\t{NM Int Client to Nom} as string,\n\t\t{NM Int Nom to Client} as string,\n\t\t{NM Exchanges} as string,\n\t\t{NM Ext Client to Nom} as string,\n\t\t{NM Ext Nom to Nom} as string,\n\t\t{Accept XML PAC} as string,\n\t\t{NM Cancel Unsettled} as string,\n\t\t{NM Client Error Corr} as string,\n\t\t{NFU Update Preference} as string,\n\t\t{NI 31minus103 Stmt} as string,\n\t\t{Trailer Fee Eligible} as string,\n\t\t{Payment Type} as string,\n\t\t{Comm Pay Frequency} as string,\n\t\t{Days Aged} as string,\n\t\t{Pay Currency} as string,\n\t\t{Rep Currency} as string,\n\t\t{Extra Commission} as string,\n\t\t{Rep Supp Rep Stmt Copy} as string,\n\t\t{Rep Supp Nom Cli Stmt Copy} as string,\n\t\t{Hold Shareholder Statements} as string,\n\t\t{Intermediary Tax Forms} as string,\n\t\t{Statement - Nom To Broker} as string,\n\t\t{Statement - Nom To Rep} as string,\n\t\t{Statement - Nom To Client} as string,\n\t\t{Statement - Int To Broker} as string,\n\t\t{Statement - Int To Rep} as string,\n\t\t{Statement - Client To Broker} as string,\n\t\t{Statement - Client To Rep} as string,\n\t\t{Statement - Client To Client} as string,\n\t\t{Confirm - Nom To Broker} as string,\n\t\t{Confirm - Nom To Rep} as string,\n\t\t{Confirm - Nom To Client} as string,\n\t\t{Confirm - Int To Broker} as string,\n\t\t{Confirm - Int To Rep} as string,\n\t\t{Confirm - Client To Broker} as string,\n\t\t{Confirm - Client To Rep} as string,\n\t\t{Confirm - Client To Client} as string,\n\t\t{T3 Releve16 NR4 - Client} as string,\n\t\t{T3 Releve16 NR4 - Nominee} as string,\n\t\t{T4RSP T4RIF Releve2 NR4 - Client} as string,\n\t\t{T4RSP T4RIF Releve2 NR4 - Nominee} as string,\n\t\t{T5008 Releve18 - Client} as string,\n\t\t{T5008 Releve18 - Nominee} as string,\n\t\t{T5 Releve3 NR4 - Client} as string,\n\t\t{T5 Releve3 NR4 - Nominee} as string,\n\t\t{T4A R1 NR4 RESP - Client} as string,\n\t\t{T4A R1 NR4 RESP - Nominee} as string,\n\t\t{Flag for generation of A file} as string,\n\t\t{Flag for generation of H file} as string,\n\t\t{Flag for generation of NS file} as string,\n\t\t{Flag for generation of PS file} as string,\n\t\t{Default Qtr Stmt - Nom - Broker} as string,\n\t\t{Default Qtr Stmt - Nom - Rep} as string,\n\t\t{Default Qtr Stmt - Nom - Client} as string,\n\t\t{Default Qtr Stmt - Intr - Broker} as string,\n\t\t{Default Qtr Stmt - Intr - Rep} as string,\n\t\t{Default Qtr Stmt - Client - Broker} as string,\n\t\t{Default Qtr Stmt - Client - Rep} as string,\n\t\t{Default Qtr Stmt - Client - Client} as string,\n\t\t{Default Semi Stmt - Nom - Broker} as string,\n\t\t{Default Semi Stmt - Nom - Rep} as string,\n\t\t{Default Semi Stmt - Nom - Client} as string,\n\t\t{Default Semi Stmt - Intr - Broker} as string,\n\t\t{Default Semi Stmt - Intr - Rep} as string,\n\t\t{Default Semi Stmt - Client - Broker} as string,\n\t\t{Default Semi Stmt - Client - Rep} as string,\n\t\t{Default Semi Stmt - Client - Client} as string,\n\t\t{Default Yrly Stmt - Nom - Broker} as string,\n\t\t{Default Yrly Stmt - Nom - Rep} as string,\n\t\t{Default Yrly Stmt - Nom - Client} as string,\n\t\t{Default Yrly Stmt - Intr - Broker} as string,\n\t\t{Default Yrly Stmt - Intr - Rep} as string,\n\t\t{Default Yrly Stmt - Client - Broker} as string,\n\t\t{Default Yrly Stmt - Client - Rep} as string,\n\t\t{Default Yrly Stmt - Client - Client} as string,\n\t\t{Dialogue - Report} as string,\n\t\t{Dialogue - Client Copy Nominee} as string,\n\t\t{Regular - Report} as string,\n\t\t{Regular - Client Copy Nominee} as string,\n\t\t{Flag for movement type CID} as string,\n\t\t{Flag for movement type CNBR} as string,\n\t\t{Flag for movement type COMC} as string,\n\t\t{Flag for movement type COMU} as string,\n\t\t{Flag for movement type FIR} as string,\n\t\t{Flag for movement type ICTC} as string,\n\t\t{Flag for movement type ICTP} as string,\n\t\t{Flag for movement type ICTR} as string,\n\t\t{Flag for movement type SWP} as string,\n\t\t{Flag for movement type TRLC} as string,\n\t\t{Flag for movement type TRLU} as string,\n\t\t{RDR Advice} as string,\n\t\t{REGD Number at Broker level} as string,\n\t\tChannelCode as string,\n\t\t{NR301 Flag} as string,\n\t\t{NR301 Created User} as string,\n\t\t{NR301 Created Date} as string,\n\t\t{NR301 Modified User} as string,\n\t\t{NR301 Modified Date} as string,\n\t\t{Clearing Firm Code} as string,\n\t\t{Compliant Network} as string,\n\t\t{Tax Generation NA File} as string,\n\t\t{Tax Generation NT File} as string,\n\t\t{Tax Generation J File} as string,\n\t\t{Tax Generation D File} as string,\n\t\t{Dealer Fee Eligible} as string,\n\t\tContactEmail_60 as string,\n\t\t{Accept XML SWP} as string,\n\t\t{NSC Participant ID} as string,\n\t\t{Related ID} as string\n\t),\n\tallowSchemaDrift: true,\n\tvalidateSchema: false,\n\tignoreNoFilesFound: false,\n\twildcardPaths:[(concat($ADLFilesDirectory, '/',$rawFileName))]) ~> RawFile\nRawFile derive(RowID = uuid(),\n\t\tLoaded = toTimestamp($Date,'yyyyMMdd')) ~> AddedTwoColumns\nAddedTwoColumns select(skipDuplicateMapInputs: true,\n\tskipDuplicateMapOutputs: true) ~> SelecteRequiredColumn\nSelecteRequiredColumn sink(allowSchemaDrift: true,\n\tvalidateSchema: false,\n\tinput(\n\t\t{Record Type} as string,\n\t\t{Dealer Code} as string,\n\t\t{Dealer Name1} as string,\n\t\t{Dealer Name2} as string,\n\t\t{Dealer Address Line 1} as string,\n\t\t{Dealer Address Line 2} as string,\n\t\t{Dealer Address Line 3} as string,\n\t\t{Dealer ProvinceOrState} as string,\n\t\t{Dealer Postal Code} as string,\n\t\t{Dealer Country} as string,\n\t\t{Dealer Phone Number} as string,\n\t\t{User Id} as string,\n\t\t{Add Date} as string,\n\t\t{Change Date} as string,\n\t\t{Company Code} as string,\n\t\t{Dealer Address Line 4} as string,\n\t\t{Dealer Address Line 5} as string,\n\t\tDeff as string,\n\t\t{Stop Date} as string,\n\t\tContact as string,\n\t\t{Contact Fax} as string,\n\t\t{contact Email} as string,\n\t\tPhone as string,\n\t\t{Dealer Otherids} as string,\n\t\t{Fax Agreement} as string,\n\t\t{Fund Rep} as string,\n\t\t{Dealer error account number} as string,\n\t\tCompetitor as string,\n\t\t{System contact} as string,\n\t\t{Service fee contact} as string,\n\t\t{Nominee Name} as string,\n\t\t{Second Fax number} as string,\n\t\t{Fax contact person} as string,\n\t\t{Agency Agreement} as string,\n\t\t{Non-Res Agreement} as string,\n\t\t{Electronic Switch Agreement} as string,\n\t\t{Fax Agreement 2} as string,\n\t\t{Net Settlement Power of Attorney} as string,\n\t\t{Software provider} as string,\n\t\t{Wire Order Redemption Agreement} as string,\n\t\t{National Account Code} as string,\n\t\t{Top Firm} as string,\n\t\t{Trailer CSS Rpt Option} as string,\n\t\tCorrespondent as string,\n\t\t{Flag for F File} as string,\n\t\t{Flag for T File} as string,\n\t\t{Rpt Trail FOF} as string,\n\t\t{Cheques To this dealer} as string,\n\t\tCity as string,\n\t\t{Outstanding Commission Currency} as string,\n\t\t{Outstanding Commission CAD} as string,\n\t\t{Outstanding Commission Currency 2} as string,\n\t\t{Outstanding Commission USD} as string,\n\t\t{Flag Generation W File} as string,\n\t\t{Confirm Broker Copy} as string,\n\t\t{Broker Type} as string,\n\t\t{CSS Status} as string,\n\t\t{Flag Generation M File} as string,\n\t\t{Volume Settlement Type} as string,\n\t\t{AMS Eligible} as string,\n\t\t{Flag Generation S File} as string,\n\t\t{Days to Settle for WO Red} as string,\n\t\t{CRA Business } as string,\n\t\t{Client Account Transfer Agreement} as string,\n\t\t{Phone Toll Free} as string,\n\t\t{Trailer Payment Frequency} as string,\n\t\t{Dealer Admin Designation} as string,\n\t\t{Dealer NAT Account Indicator} as string,\n\t\t{ProvinceOrState Alpha Code} as string,\n\t\t{Numeric Country Code} as string,\n\t\t{Ownership ID} as string,\n\t\t{KYC Regulation} as string,\n\t\t{KYC Mandate} as string,\n\t\t{KYCSignture-StoreReq} as string,\n\t\t{KYC Status} as string,\n\t\tEPA as string,\n\t\t{Territory Code} as string,\n\t\tUUID as string,\n\t\tGUID as string,\n\t\tCM as string,\n\t\t{NM Add Money Move} as string,\n\t\t{NM Nom Error Corr} as string,\n\t\t{Tplus1 MM Wire} as string,\n\t\t{NM Int Client to Nom} as string,\n\t\t{NM Int Nom to Client} as string,\n\t\t{NM Exchanges} as string,\n\t\t{NM Ext Client to Nom} as string,\n\t\t{NM Ext Nom to Nom} as string,\n\t\t{Accept XML PAC} as string,\n\t\t{NM Cancel Unsettled} as string,\n\t\t{NM Client Error Corr} as string,\n\t\t{NFU Update Preference} as string,\n\t\t{NI 31minus103 Stmt} as string,\n\t\t{Trailer Fee Eligible} as string,\n\t\t{Payment Type} as string,\n\t\t{Comm Pay Frequency} as string,\n\t\t{Days Aged} as string,\n\t\t{Pay Currency} as string,\n\t\t{Rep Currency} as string,\n\t\t{Extra Commission} as string,\n\t\t{Rep Supp Rep Stmt Copy} as string,\n\t\t{Rep Supp Nom Cli Stmt Copy} as string,\n\t\t{Hold Shareholder Statements} as string,\n\t\t{Intermediary Tax Forms} as string,\n\t\t{Statement - Nom To Broker} as string,\n\t\t{Statement - Nom To Rep} as string,\n\t\t{Statement - Nom To Client} as string,\n\t\t{Statement - Int To Broker} as string,\n\t\t{Statement - Int To Rep} as string,\n\t\t{Statement - Client To Broker} as string,\n\t\t{Statement - Client To Rep} as string,\n\t\t{Statement - Client To Client} as string,\n\t\t{Confirm - Nom To Broker} as string,\n\t\t{Confirm - Nom To Rep} as string,\n\t\t{Confirm - Nom To Client} as string,\n\t\t{Confirm - Int To Broker} as string,\n\t\t{Confirm - Int To Rep} as string,\n\t\t{Confirm - Client To Broker} as string,\n\t\t{Confirm - Client To Rep} as string,\n\t\t{Confirm - Client To Client} as string,\n\t\t{T3 Releve16 NR4 - Client} as string,\n\t\t{T3 Releve16 NR4 - Nominee} as string,\n\t\t{T4RSP T4RIF Releve2 NR4 - Client} as string,\n\t\t{T4RSP T4RIF Releve2 NR4 - Nominee} as string,\n\t\t{T5008 Releve18 - Client} as string,\n\t\t{T5008 Releve18 - Nominee} as string,\n\t\t{T5 Releve3 NR4 - Client} as string,\n\t\t{T5 Releve3 NR4 - Nominee} as string,\n\t\t{T4A R1 NR4 RESP - Client} as string,\n\t\t{T4A R1 NR4 RESP - Nominee} as string,\n\t\t{Flag for generation of A file} as string,\n\t\t{Flag for generation of H file} as string,\n\t\t{Flag for generation of NS file} as string,\n\t\t{Flag for generation of PS file} as string,\n\t\t{Default Qtr Stmt - Nom - Broker} as string,\n\t\t{Default Qtr Stmt - Nom - Rep} as string,\n\t\t{Default Qtr Stmt - Nom - Client} as string,\n\t\t{Default Qtr Stmt - Intr - Broker} as string,\n\t\t{Default Qtr Stmt - Intr - Rep} as string,\n\t\t{Default Qtr Stmt - Client - Broker} as string,\n\t\t{Default Qtr Stmt - Client - Rep} as string,\n\t\t{Default Qtr Stmt - Client - Client} as string,\n\t\t{Default Semi Stmt - Nom - Broker} as string,\n\t\t{Default Semi Stmt - Nom - Rep} as string,\n\t\t{Default Semi Stmt - Nom - Client} as string,\n\t\t{Default Semi Stmt - Intr - Broker} as string,\n\t\t{Default Semi Stmt - Intr - Rep} as string,\n\t\t{Default Semi Stmt - Client - Broker} as string,\n\t\t{Default Semi Stmt - Client - Rep} as string,\n\t\t{Default Semi Stmt - Client - Client} as string,\n\t\t{Default Yrly Stmt - Nom - Broker} as string,\n\t\t{Default Yrly Stmt - Nom - Rep} as string,\n\t\t{Default Yrly Stmt - Nom - Client} as string,\n\t\t{Default Yrly Stmt - Intr - Broker} as string,\n\t\t{Default Yrly Stmt - Intr - Rep} as string,\n\t\t{Default Yrly Stmt - Client - Broker} as string,\n\t\t{Default Yrly Stmt - Client - Rep} as string,\n\t\t{Default Yrly Stmt - Client - Client} as string,\n\t\t{Dialogue - Report} as string,\n\t\t{Dialogue - Client Copy Nominee} as string,\n\t\t{Regular - Report} as string,\n\t\t{Regular - Client Copy Nominee} as string,\n\t\t{Flag for movement type CID} as string,\n\t\t{Flag for movement type CNBR} as string,\n\t\t{Flag for movement type COMC} as string,\n\t\t{Flag for movement type COMU} as string,\n\t\t{Flag for movement type FIR} as string,\n\t\t{Flag for movement type ICTC} as string,\n\t\t{Flag for movement type ICTP} as string,\n\t\t{Flag for movement type ICTR} as string,\n\t\t{Flag for movement type SWP} as string,\n\t\t{Flag for movement type TRLC} as string,\n\t\t{Flag for movement type TRLU} as string,\n\t\t{RDR Advice} as string,\n\t\t{REGD Number at Broker level} as string,\n\t\tChannelCode as string,\n\t\t{NR301 Flag} as string,\n\t\t{NR301 Created User} as string,\n\t\t{NR301 Created Date} as string,\n\t\t{NR301 Modified User} as string,\n\t\t{NR301 Modified Date} as string,\n\t\t{Clearing Firm Code} as string,\n\t\t{Compliant Network} as string,\n\t\t{Tax Generation NA File} as string,\n\t\t{Tax Generation NT File} as string,\n\t\t{Tax Generation J File} as string,\n\t\t{Tax Generation D File} as string,\n\t\t{Dealer Fee Eligible} as string,\n\t\tContactEmail_60 as string,\n\t\t{Accept XML SWP} as string,\n\t\t{NSC Participant ID} as string,\n\t\t{Related ID} as string,\n\t\tLoaded as string,\n\t\tRowID as string\n\t),\n\tpartitionFileNames:[($outFileName)],\n\tumask: 0022,\n\tpreCommands: [],\n\tpostCommands: [],\n\tdateFormat:'yyyy/MM/dd',\n\tskipDuplicateMapInputs: true,\n\tskipDuplicateMapOutputs: true,\n\tmapColumn(\n\t\t{Record Type},\n\t\t{Dealer Code},\n\t\t{Dealer Name1},\n\t\t{Dealer Name2},\n\t\t{Dealer Address Line 1},\n\t\t{Dealer Address Line 2},\n\t\t{Dealer Address Line 3},\n\t\t{Dealer ProvinceOrState},\n\t\t{Dealer Postal Code},\n\t\t{Dealer Country},\n\t\t{Dealer Phone Number},\n\t\t{User Id},\n\t\t{Add Date},\n\t\t{Change Date},\n\t\t{Company Code},\n\t\t{Dealer Address Line 4},\n\t\t{Dealer Address Line 5},\n\t\tDeff,\n\t\t{Stop Date},\n\t\tContact,\n\t\t{Contact Fax},\n\t\t{contact Email},\n\t\tPhone,\n\t\t{Dealer Otherids},\n\t\t{Fax Agreement},\n\t\t{Fund Rep},\n\t\t{Dealer error account number},\n\t\tCompetitor,\n\t\t{System contact},\n\t\t{Service fee contact},\n\t\t{Nominee Name},\n\t\t{Second Fax number},\n\t\t{Fax contact person},\n\t\t{Agency Agreement},\n\t\t{Non-Res Agreement},\n\t\t{Electronic Switch Agreement},\n\t\t{Fax Agreement 2},\n\t\t{Net Settlement Power of Attorney},\n\t\t{Software provider},\n\t\t{Wire Order Redemption Agreement},\n\t\t{National Account Code},\n\t\t{Top Firm},\n\t\t{Trailer CSS Rpt Option},\n\t\tCorrespondent,\n\t\t{Flag for F File},\n\t\t{Flag for T File},\n\t\t{Rpt Trail FOF},\n\t\t{Cheques To this dealer},\n\t\tCity,\n\t\t{Outstanding Commission Currency},\n\t\t{Outstanding Commission CAD},\n\t\t{Outstanding Commission Currency 2},\n\t\t{Outstanding Commission USD},\n\t\t{Flag Generation W File},\n\t\t{Confirm Broker Copy},\n\t\t{Broker Type},\n\t\t{CSS Status},\n\t\t{Flag Generation M File},\n\t\t{Volume Settlement Type},\n\t\t{AMS Eligible},\n\t\t{Flag Generation S File},\n\t\t{Days to Settle for WO Red},\n\t\t{CRA Business },\n\t\t{Client Account Transfer Agreement},\n\t\t{Phone Toll Free},\n\t\t{Trailer Payment Frequency},\n\t\t{Dealer Admin Designation},\n\t\t{Dealer NAT Account Indicator},\n\t\t{ProvinceOrState Alpha Code},\n\t\t{Numeric Country Code},\n\t\t{Ownership ID},\n\t\t{KYC Regulation},\n\t\t{KYC Mandate},\n\t\t{KYCSignture-StoreReq},\n\t\t{KYC Status},\n\t\tEPA,\n\t\t{Territory Code},\n\t\tUUID,\n\t\tGUID,\n\t\tCM,\n\t\t{NM Add Money Move},\n\t\t{NM Nom Error Corr},\n\t\t{Tplus1 MM Wire},\n\t\t{NM Int Client to Nom},\n\t\t{NM Int Nom to Client},\n\t\t{NM Exchanges},\n\t\t{NM Ext Client to Nom},\n\t\t{NM Ext Nom to Nom},\n\t\t{Accept XML PAC},\n\t\t{NM Cancel Unsettled},\n\t\t{NM Client Error Corr},\n\t\t{NFU Update Preference},\n\t\t{NI 31minus103 Stmt},\n\t\t{Trailer Fee Eligible},\n\t\t{Payment Type},\n\t\t{Comm Pay Frequency},\n\t\t{Days Aged},\n\t\t{Pay Currency},\n\t\t{Rep Currency},\n\t\t{Extra Commission},\n\t\t{Rep Supp Rep Stmt Copy},\n\t\t{Rep Supp Nom Cli Stmt Copy},\n\t\t{Hold Shareholder Statements},\n\t\t{Intermediary Tax Forms},\n\t\t{Statement - Nom To Broker},\n\t\t{Statement - Nom To Rep},\n\t\t{Statement - Nom To Client},\n\t\t{Statement - Int To Broker},\n\t\t{Statement - Int To Rep},\n\t\t{Statement - Client To Broker},\n\t\t{Statement - Client To Rep},\n\t\t{Statement - Client To Client},\n\t\t{Confirm - Nom To Broker},\n\t\t{Confirm - Nom To Rep},\n\t\t{Confirm - Nom To Client},\n\t\t{Confirm - Int To Broker},\n\t\t{Confirm - Int To Rep},\n\t\t{Confirm - Client To Broker},\n\t\t{Confirm - Client To Rep},\n\t\t{Confirm - Client To Client},\n\t\t{T3 Releve16 NR4 - Client},\n\t\t{T3 Releve16 NR4 - Nominee},\n\t\t{T4RSP T4RIF Releve2 NR4 - Client},\n\t\t{T4RSP T4RIF Releve2 NR4 - Nominee},\n\t\t{T5008 Releve18 - Client},\n\t\t{T5008 Releve18 - Nominee},\n\t\t{T5 Releve3 NR4 - Client},\n\t\t{T5 Releve3 NR4 - Nominee},\n\t\t{T4A R1 NR4 RESP - Client},\n\t\t{T4A R1 NR4 RESP - Nominee},\n\t\t{Flag for generation of A file},\n\t\t{Flag for generation of H file},\n\t\t{Flag for generation of NS file},\n\t\t{Flag for generation of PS file},\n\t\t{Default Qtr Stmt - Nom - Broker},\n\t\t{Default Qtr Stmt - Nom - Rep},\n\t\t{Default Qtr Stmt - Nom - Client},\n\t\t{Default Qtr Stmt - Intr - Broker},\n\t\t{Default Qtr Stmt - Intr - Rep},\n\t\t{Default Qtr Stmt - Client - Broker},\n\t\t{Default Qtr Stmt - Client - Rep},\n\t\t{Default Qtr Stmt - Client - Client},\n\t\t{Default Semi Stmt - Nom - Broker},\n\t\t{Default Semi Stmt - Nom - Rep},\n\t\t{Default Semi Stmt - Nom - Client},\n\t\t{Default Semi Stmt - Intr - Broker},\n\t\t{Default Semi Stmt - Intr - Rep},\n\t\t{Default Semi Stmt - Client - Broker},\n\t\t{Default Semi Stmt - Client - Rep},\n\t\t{Default Semi Stmt - Client - Client},\n\t\t{Default Yrly Stmt - Nom - Broker},\n\t\t{Default Yrly Stmt - Nom - Rep},\n\t\t{Default Yrly Stmt - Nom - Client},\n\t\t{Default Yrly Stmt - Intr - Broker},\n\t\t{Default Yrly Stmt - Intr - Rep},\n\t\t{Default Yrly Stmt - Client - Broker},\n\t\t{Default Yrly Stmt - Client - Rep},\n\t\t{Default Yrly Stmt - Client - Client},\n\t\t{Dialogue - Report},\n\t\t{Dialogue - Client Copy Nominee},\n\t\t{Regular - Report},\n\t\t{Regular - Client Copy Nominee},\n\t\t{Flag for movement type CID},\n\t\t{Flag for movement type CNBR},\n\t\t{Flag for movement type COMC},\n\t\t{Flag for movement type COMU},\n\t\t{Flag for movement type FIR},\n\t\t{Flag for movement type ICTC},\n\t\t{Flag for movement type ICTP},\n\t\t{Flag for movement type ICTR},\n\t\t{Flag for movement type SWP},\n\t\t{Flag for movement type TRLC},\n\t\t{Flag for movement type TRLU},\n\t\t{RDR Advice},\n\t\t{REGD Number at Broker level},\n\t\tChannelCode,\n\t\t{NR301 Flag},\n\t\t{NR301 Created User},\n\t\t{NR301 Created Date},\n\t\t{NR301 Modified User},\n\t\t{NR301 Modified Date},\n\t\t{Clearing Firm Code},\n\t\t{Compliant Network},\n\t\t{Tax Generation NA File},\n\t\t{Tax Generation NT File},\n\t\t{Tax Generation J File},\n\t\t{Tax Generation D File},\n\t\t{Dealer Fee Eligible},\n\t\tContactEmail_60,\n\t\t{Accept XML SWP},\n\t\t{NSC Participant ID},\n\t\t{Related ID},\n\t\tLoaded,\n\t\tRowID\n\t),\n\tpartitionBy('hash', 1)) ~> TempFile"}}}