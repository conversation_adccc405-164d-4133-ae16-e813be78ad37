# Azure Data Factory CI/CD Pipeline with Pipeline Exclusion
# This pipeline demonstrates how to exclude specific ADF pipelines from DEV to PROD promotion

trigger:
  branches:
    include:
    - develop
    - main
  paths:
    include:
    - pipeline/*
    - dataset/*
    - linkedService/*
    - dataflow/*
    - trigger/*
    - factory/*

variables:
  # Environment-specific variables
  - name: devResourceGroupName
    value: 'rg-dev-adf'
  - name: prodResourceGroupName
    value: 'rg-prod-adf'
  - name: devDataFactoryName
    value: 'AZ-DEV-UW-DF-SCD'
  - name: prodDataFactoryName
    value: 'AZ-PRD-UW-DF-SCD'
  - name: azureServiceConnection
    value: 'Azure-Service-Connection'

stages:
- stage: Build
  displayName: 'Build and Filter ADF Artifacts'
  jobs:
  - job: BuildJob
    displayName: 'Build ADF with Pipeline Exclusion'
    pool:
      vmImage: 'windows-latest'
    
    steps:
    - checkout: self
      displayName: 'Checkout Source Code'
    
    - task: PowerShell@2
      displayName: 'Filter ADF Pipelines for Production'
      inputs:
        targetType: 'filePath'
        filePath: '$(Build.SourcesDirectory)/POC/Filter-ADFPipelines.ps1'
        arguments: >
          -SourcePath "$(Build.SourcesDirectory)"
          -TargetPath "$(Build.ArtifactStagingDirectory)/filtered-adf"
          -ExclusionAnnotations @("DEV_ONLY", "EXCLUDE_FROM_PROD", "DEVELOPMENT_ONLY")
        pwsh: true
    
    - task: PowerShell@2
      displayName: 'Generate Exclusion Report'
      inputs:
        targetType: 'inline'
        script: |
          Write-Host "=== ADF Pipeline Exclusion Report ===" -ForegroundColor Magenta
          
          # Count original pipelines
          $originalPipelines = Get-ChildItem "$(Build.SourcesDirectory)/pipeline" -Filter "*.json" | Measure-Object
          Write-Host "Original Pipelines: $($originalPipelines.Count)" -ForegroundColor Yellow
          
          # Count filtered pipelines
          $filteredPath = "$(Build.ArtifactStagingDirectory)/filtered-adf/pipeline"
          if (Test-Path $filteredPath) {
            $filteredPipelines = Get-ChildItem $filteredPath -Filter "*.json" | Measure-Object
            Write-Host "Filtered Pipelines: $($filteredPipelines.Count)" -ForegroundColor Green
            Write-Host "Excluded Pipelines: $($originalPipelines.Count - $filteredPipelines.Count)" -ForegroundColor Red
          } else {
            Write-Host "No filtered pipelines directory found" -ForegroundColor Red
          }
        pwsh: true
    
    - task: PublishBuildArtifacts@1
      displayName: 'Publish Filtered ADF Artifacts'
      inputs:
        pathToPublish: '$(Build.ArtifactStagingDirectory)/filtered-adf'
        artifactName: 'filtered-adf-artifacts'
        publishLocation: 'Container'

- stage: DeployToProd
  displayName: 'Deploy to Production'
  dependsOn: Build
  condition: and(succeeded(), eq(variables['Build.SourceBranch'], 'refs/heads/main'))
  jobs:
  - deployment: DeployProd
    displayName: 'Deploy to Production ADF'
    pool:
      vmImage: 'windows-latest'
    environment: 'Production'
    
    strategy:
      runOnce:
        deploy:
          steps:
          - download: current
            artifact: 'filtered-adf-artifacts'
            displayName: 'Download Filtered ADF Artifacts'
          
          - task: AzurePowerShell@5
            displayName: 'Stop ADF Triggers'
            inputs:
              azureSubscription: '$(azureServiceConnection)'
              scriptType: 'inlineScript'
              script: |
                # Stop all triggers before deployment
                $triggers = Get-AzDataFactoryV2Trigger -ResourceGroupName "$(prodResourceGroupName)" -DataFactoryName "$(prodDataFactoryName)"
                foreach ($trigger in $triggers) {
                  if ($trigger.RuntimeState -eq "Started") {
                    Write-Host "Stopping trigger: $($trigger.Name)"
                    Stop-AzDataFactoryV2Trigger -ResourceGroupName "$(prodResourceGroupName)" -DataFactoryName "$(prodDataFactoryName)" -Name $trigger.Name -Force
                  }
                }
              azurePowerShellVersion: 'LatestVersion'
          
          - task: AzureResourceManagerTemplateDeployment@3
            displayName: 'Deploy ADF ARM Template'
            inputs:
              deploymentScope: 'Resource Group'
              azureResourceManagerConnection: '$(azureServiceConnection)'
              subscriptionId: '$(subscriptionId)'
              action: 'Create Or Update Resource Group'
              resourceGroupName: '$(prodResourceGroupName)'
              location: 'West US 2'
              templateLocation: 'Linked artifact'
              csmFile: '$(Pipeline.Workspace)/filtered-adf-artifacts/ARMTemplateForFactory.json'
              csmParametersFile: '$(Pipeline.Workspace)/filtered-adf-artifacts/ARMTemplateParametersForFactory.json'
              overrideParameters: |
                -factoryName "$(prodDataFactoryName)"
                -location "West US 2"
              deploymentMode: 'Incremental'
          
          - task: AzurePowerShell@5
            displayName: 'Start ADF Triggers'
            inputs:
              azureSubscription: '$(azureServiceConnection)'
              scriptType: 'inlineScript'
              script: |
                # Start triggers after deployment
                $triggers = Get-AzDataFactoryV2Trigger -ResourceGroupName "$(prodResourceGroupName)" -DataFactoryName "$(prodDataFactoryName)"
                foreach ($trigger in $triggers) {
                  Write-Host "Starting trigger: $($trigger.Name)"
                  Start-AzDataFactoryV2Trigger -ResourceGroupName "$(prodResourceGroupName)" -DataFactoryName "$(prodDataFactoryName)" -Name $trigger.Name -Force
                }
              azurePowerShellVersion: 'LatestVersion'
          
          - task: PowerShell@2
            displayName: 'Deployment Summary'
            inputs:
              targetType: 'inline'
              script: |
                Write-Host "=== Deployment Summary ===" -ForegroundColor Green
                Write-Host "Successfully deployed filtered ADF artifacts to Production" -ForegroundColor Green
                Write-Host "Excluded pipelines were not deployed to maintain DEV/PROD separation" -ForegroundColor Yellow
              pwsh: true
