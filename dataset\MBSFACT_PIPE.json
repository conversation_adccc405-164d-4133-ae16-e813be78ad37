{"name": "MBSFACT_PIPE", "properties": {"linkedServiceName": {"referenceName": "AzureDataLakeStorage", "type": "LinkedServiceReference"}, "parameters": {"RootFolder": {"type": "string", "defaultValue": "scdintegration"}, "FolderPath": {"type": "string", "defaultValue": "DDI Migration/MBSFactSet/Temp"}, "FileName": {"type": "string"}}, "folder": {"name": "Inbound/DDI/IN-329 DDI Migration/IN-330 MBSFact Sets"}, "annotations": [], "type": "DelimitedText", "typeProperties": {"location": {"type": "AzureBlobFSLocation", "fileName": {"value": "@dataset().FileName", "type": "Expression"}, "folderPath": {"value": "@dataset().FolderPath", "type": "Expression"}, "fileSystem": {"value": "@dataset().RootFolder", "type": "Expression"}}, "columnDelimiter": "|", "escapeChar": "\\", "firstRowAsHeader": true, "quoteChar": "\""}, "schema": [{"name": "CurrentDate", "type": "String"}, {"name": "InstrumentSEDOL", "type": "String"}, {"name": "InstrumentISIN", "type": "String"}, {"name": "InstrumentCUSIP", "type": "String"}, {"name": "Symbol", "type": "String"}, {"name": "SecurityNm", "type": "String"}, {"name": "MawerSecurityNm", "type": "String"}, {"name": "BBIssuerNm", "type": "String"}, {"name": "ISSUER", "type": "String"}, {"name": "IndustryGroup", "type": "String"}, {"name": "MawerAssetClassTier1Nm", "type": "String"}, {"name": "MawerAssetClassTier2Nm", "type": "String"}, {"name": "MawerAssetClassTier3Nm", "type": "String"}, {"name": "ISSUE_DT", "type": "String"}, {"name": "MATURITY", "type": "String"}, {"name": "MTG_WAM", "type": "String"}, {"name": "MTG_WALA_CALC", "type": "String"}, {"name": "MTG_WACPN", "type": "String"}, {"name": "MTG_PAY_DELAY", "type": "String"}, {"name": "MTG_WAL", "type": "String"}, {"name": "RTG_DBRS", "type": "String"}, {"name": "RTG_SP", "type": "String"}, {"name": "RTG_MOODY", "type": "String"}, {"name": "YLD_YTM_MID", "type": "String"}, {"name": "YLD_CUR_MID", "type": "String"}, {"name": "DUR_ADJ_MID", "type": "String"}, {"name": "CPN", "type": "String"}, {"name": "CNVX_MID", "type": "String"}, {"name": "securitystatuscd", "type": "String"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "type": "String"}, {"name": "CouponType", "type": "String"}, {"name": "Factor", "type": "String"}, {"name": "FactorData", "type": "String"}]}, "type": "Microsoft.DataFactory/factories/datasets"}