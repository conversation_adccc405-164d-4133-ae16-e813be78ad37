{"name": "DECOM- Cash Management Report - CHILD", "properties": {"activities": [{"name": "Set PM Name", "type": "SetVariable", "dependsOn": [], "policy": {"secureOutput": false, "secureInput": false}, "userProperties": [], "typeProperties": {"variableName": "PMName", "value": {"value": "@pipeline().parameters.PMName", "type": "Expression"}}}, {"name": "Set PMA Name", "type": "SetVariable", "dependsOn": [], "policy": {"secureOutput": false, "secureInput": false}, "userProperties": [], "typeProperties": {"variableName": "PMAName", "value": {"value": "@pipeline().parameters.PMAName", "type": "Expression"}}}, {"name": "Get Cash Management Data from ADX", "type": "Copy", "dependsOn": [{"activity": "Set PMA Name", "dependencyConditions": ["Succeeded"]}, {"activity": "Set PM Name", "dependencyConditions": ["Succeeded"]}], "policy": {"timeout": "7.00:00:00", "retry": 0, "retryIntervalInSeconds": 30, "secureOutput": false, "secureInput": false}, "userProperties": [], "typeProperties": {"source": {"type": "AzureDataExplorerSource", "query": {"value": "@concat('let maxLoaded = Money_Market_Report\n| summarize maxLoaded  = max(Loaded);\nMoney_Market_Report\n| join kind = inner maxLoaded on $left.Loaded == $right.maxLoaded\n| where [''Portfolio manager'']  == ''',variables('PMName'),'''\n| where [''Portfolio manager associate'']  == ''',variables('PMAName'),''''\n)", "type": "Expression"}, "queryTimeout": "00:10:00", "noTruncation": false}, "sink": {"type": "DelimitedTextSink", "storeSettings": {"type": "AzureBlobFSWriteSettings"}, "formatSettings": {"type": "DelimitedTextWriteSettings", "quoteAllText": true, "fileExtension": ".txt"}}, "enableStaging": false, "translator": {"type": "TabularTranslator", "typeConversion": true, "typeConversionSettings": {"allowDataTruncation": true, "treatBooleanAsNumber": false}}}, "inputs": [{"referenceName": "ADX_PARAMETER", "type": "DatasetReference", "parameters": {"Table": "Money_Market_Report", "Database": "GENERAL"}}], "outputs": [{"referenceName": "ADL_Cash_Management_2", "type": "DatasetReference", "parameters": {"FilePath": {"value": "@concat('Report Automation/Money Market Report/OUT')", "type": "Expression"}, "FileName": {"value": "@concat('Cash Management Report_',variables('PMName'),'_',variables('PMAName'),'.csv')", "type": "Expression"}}}]}, {"name": "Get PM and PMA Email", "type": "Lookup", "dependsOn": [{"activity": "Get Cash Management Data from ADX", "dependencyConditions": ["Succeeded"]}], "policy": {"timeout": "7.00:00:00", "retry": 0, "retryIntervalInSeconds": 30, "secureOutput": false, "secureInput": false}, "userProperties": [], "typeProperties": {"source": {"type": "AzureDataExplorerSource", "query": "Email_List\n| where Name  in ('<PERSON><PERSON>','<PERSON>')\n| summarize result = strcat_array(make_list(['Email']),\";\")\n", "queryTimeout": "00:10:00", "noTruncation": true}, "dataset": {"referenceName": "ADX_PARAMETER", "type": "DatasetReference", "parameters": {"Table": "Email_List", "Database": "GENERAL"}}}}, {"name": "Set Email Body", "type": "SetVariable", "dependsOn": [{"activity": "Set OutputFileName", "dependencyConditions": ["Succeeded"]}], "policy": {"secureOutput": false, "secureInput": false}, "userProperties": [], "typeProperties": {"variableName": "EmailBody", "value": {"value": "@concat('{\"ADL_File_With_Attachments\":\"scdintegration/Report Automation/Money Market Report/OUT/',\nvariables('OutputFileName'),\n\n'\",\"Subject\":\"Money Market Report\",\n\n\"Attachment_Name\":\"Money Market Report.csv\",\n\n\"Email_Body\":\"Testing Money Market Reports ',variables('OutputFileName'),' - Please ignore this email.\",\n\n\"Email_Recipients\":\"',activity('Get PM and PMA Email').output.firstRow.result,'\"}')", "type": "Expression"}}}, {"name": "Send Email with Attachement", "type": "WebActivity", "dependsOn": [{"activity": "Set Email Body", "dependencyConditions": ["Succeeded"]}], "policy": {"timeout": "7.00:00:00", "retry": 0, "retryIntervalInSeconds": 30, "secureOutput": false, "secureInput": false}, "userProperties": [], "typeProperties": {"method": "POST", "url": "https://prod-181.westus.logic.azure.com:443/workflows/37ec24fd02f54823af11e60dba4a1feb/triggers/manual/paths/invoke?api-version=2016-10-01&sp=%2Ftriggers%2Fmanual%2Frun&sv=1.0&sig=0234K-IZhjGlr4Ho2PkysG9EHxmR3-QUe9CCr45BrcY", "body": {"value": "@variables('EmailBody')", "type": "Expression"}}}, {"name": "Set OutputFileName", "type": "SetVariable", "dependsOn": [{"activity": "Get PM and PMA Email", "dependencyConditions": ["Succeeded"]}], "policy": {"secureOutput": false, "secureInput": false}, "userProperties": [], "typeProperties": {"variableName": "OutputFileName", "value": {"value": "@concat('Cash Management Report_',variables('PMName'),'_',variables('PMAName'),'.csv')", "type": "Expression"}}}], "parameters": {"PMName": {"type": "string"}, "PMAName": {"type": "string"}}, "variables": {"FileName": {"type": "String"}, "PMName": {"type": "String"}, "PMAName": {"type": "String"}, "EmailBody": {"type": "String"}, "OutputFileName": {"type": "String", "defaultValue": "scdintegration/Report Automation/ARCHIVE/OUT/20220726/Cash Management Report_<PERSON><PERSON>_Portfolio Manager.csv"}, "FileCountIndex": {"type": "String"}}, "folder": {"name": "Internal/Cash Management Report Workflow"}, "annotations": [], "lastPublishTime": "2023-12-01T04:10:36Z"}, "type": "Microsoft.DataFactory/factories/pipelines"}