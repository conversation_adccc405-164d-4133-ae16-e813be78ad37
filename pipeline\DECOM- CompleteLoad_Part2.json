{"name": "DECOM- CompleteLoad_Part2", "properties": {"activities": [{"name": "11 DM_TRANSACTIONS_COSTS to MI", "type": "IfCondition", "dependsOn": [], "userProperties": [], "typeProperties": {"expression": {"value": "@equals(pipeline().parameters.Load_DM_TRANSACTIONS_COSTS,'1')", "type": "Expression"}, "ifTrueActivities": [{"name": "DM_TRANSACTIONS_COSTS to MI", "type": "Copy", "dependsOn": [], "policy": {"timeout": "7.00:00:00", "retry": 0, "retryIntervalInSeconds": 30, "secureOutput": false, "secureInput": false}, "userProperties": [], "typeProperties": {"source": {"type": "OracleSource", "partitionOption": "None", "queryTimeout": "02:00:00"}, "sink": {"type": "SqlMISink", "preCopyScript": "delete from dbo.DM_TRANSACTIONS_COSTS", "writeBehavior": "insert", "sqlWriterUseTableLock": false}, "enableStaging": false, "translator": {"type": "TabularTranslator", "mappings": [{"source": {"name": "DWH_EXTRACT_PROCESS_IK", "type": "Decimal"}, "sink": {"name": "DWH_EXTRACT_PROCESS_IK", "type": "Decimal"}}, {"source": {"name": "DWH_LOAD_TS", "type": "DateTime"}, "sink": {"name": "DWH_LOAD_TS", "type": "DateTime"}}, {"source": {"name": "COST", "type": "String"}, "sink": {"name": "COST", "type": "String"}}, {"source": {"name": "COST_NAME", "type": "String"}, "sink": {"name": "COST_NAME", "type": "String"}}, {"source": {"name": "CALCULATION_BASIS", "type": "String"}, "sink": {"name": "CALCULATION_BASIS", "type": "String"}}, {"source": {"name": "CATEGORY_OF_COST", "type": "String"}, "sink": {"name": "CATEGORY_OF_COST", "type": "String"}}, {"source": {"name": "FINANCE_BOOKING_D", "type": "String"}, "sink": {"name": "FINANCE_BOOKING_D", "type": "String"}}, {"source": {"name": "INCLUDE_IN_SETTLE", "type": "String"}, "sink": {"name": "INCLUDE_IN_SETTLE", "type": "String"}}, {"source": {"name": "MAIN_CURRENCY", "type": "String"}, "sink": {"name": "MAIN_CURRENCY", "type": "String"}}, {"source": {"name": "SIGN_OF_COST", "type": "String"}, "sink": {"name": "SIGN_OF_COST", "type": "String"}}, {"source": {"name": "TYPE_OF_COST", "type": "String"}, "sink": {"name": "TYPE_OF_COST", "type": "String"}}, {"source": {"name": "UNIT_COST", "type": "String"}, "sink": {"name": "UNIT_COST", "type": "String"}}, {"source": {"name": "NETT_OR_GROSS", "type": "String"}, "sink": {"name": "NETT_OR_GROSS", "type": "String"}}, {"source": {"name": "BUSINESS_TRANSACTION_CODE", "type": "String"}, "sink": {"name": "BUSINESS_TRANSACTION_CODE", "type": "String"}}, {"source": {"name": "BUSINESS_TRANSACTION_CODE_NO", "type": "Decimal"}, "sink": {"name": "BUSINESS_TRANSACTION_CODE_NO", "type": "Decimal"}}, {"source": {"name": "BROKER", "type": "String"}, "sink": {"name": "BROKER", "type": "String"}}, {"source": {"name": "BROKER_NAME", "type": "String"}, "sink": {"name": "BROKER_NAME", "type": "String"}}, {"source": {"name": "ISSUER", "type": "String"}, "sink": {"name": "ISSUER", "type": "String"}}, {"source": {"name": "ISSUER_NAME", "type": "String"}, "sink": {"name": "ISSUER_NAME", "type": "String"}}, {"source": {"name": "COUNTERPARTY", "type": "String"}, "sink": {"name": "COUNTERPARTY", "type": "String"}}, {"source": {"name": "COUNTERPARTY_NAME", "type": "String"}, "sink": {"name": "COUNTERPARTY_NAME", "type": "String"}}, {"source": {"name": "COUNTRY_OF_ISSUER", "type": "String"}, "sink": {"name": "COUNTRY_OF_ISSUER", "type": "String"}}, {"source": {"name": "COUNTRY_OF_SECURITY", "type": "String"}, "sink": {"name": "COUNTRY_OF_SECURITY", "type": "String"}}, {"source": {"name": "CUSTODY", "type": "String"}, "sink": {"name": "CUSTODY", "type": "String"}}, {"source": {"name": "EXCHANGE", "type": "String"}, "sink": {"name": "EXCHANGE", "type": "String"}}, {"source": {"name": "INSTRUMENT_TYPE", "type": "String"}, "sink": {"name": "INSTRUMENT_TYPE", "type": "String"}}, {"source": {"name": "CURRENCY", "type": "String"}, "sink": {"name": "CURRENCY", "type": "String"}}, {"source": {"name": "PORTFOLIO_CURRENCY", "type": "String"}, "sink": {"name": "PORTFOLIO_CURRENCY", "type": "String"}}, {"source": {"name": "QUOTATION_CURRENCY", "type": "String"}, "sink": {"name": "QUOTATION_CURRENCY", "type": "String"}}, {"source": {"name": "PORTFOLIO", "type": "String"}, "sink": {"name": "PORTFOLIO", "type": "String"}}, {"source": {"name": "ISIN", "type": "String"}, "sink": {"name": "ISIN", "type": "String"}}, {"source": {"name": "SECURITY_ID", "type": "String"}, "sink": {"name": "SECURITY_ID", "type": "String"}}, {"source": {"name": "TRANSACTION_CANCELLATION_FLAG", "type": "String"}, "sink": {"name": "TRANSACTION_CANCELLATION_FLAG", "type": "String"}}, {"source": {"name": "TRANSACTION_STATUS_ACTUAL", "type": "String"}, "sink": {"name": "TRANSACTION_STATUS_ACTUAL", "type": "String"}}, {"source": {"name": "AMOUNT_COST_CURRENCY", "type": "Decimal"}, "sink": {"name": "AMOUNT_COST_CURRENCY", "type": "Decimal"}}, {"source": {"name": "AMOUNT_PORTFOLIO_CURRENCY", "type": "Decimal"}, "sink": {"name": "AMOUNT_PORTFOLIO_CURRENCY", "type": "Decimal"}}, {"source": {"name": "AMOUNT_QUOTATION_CURRENCY", "type": "Decimal"}, "sink": {"name": "AMOUNT_QUOTATION_CURRENCY", "type": "Decimal"}}, {"source": {"name": "AMOUNT_SETTLEMENT_CURRENCY", "type": "Decimal"}, "sink": {"name": "AMOUNT_SETTLEMENT_CURRENCY", "type": "Decimal"}}, {"source": {"name": "COST_DATE_FULL", "type": "DateTime"}, "sink": {"name": "COST_DATE_FULL", "type": "DateTime"}}, {"source": {"name": "COST_INDEX", "type": "Decimal"}, "sink": {"name": "COST_INDEX", "type": "Decimal"}}, {"source": {"name": "COST_TEXT", "type": "String"}, "sink": {"name": "COST_TEXT", "type": "String"}}, {"source": {"name": "FX_RATE", "type": "Decimal"}, "sink": {"name": "FX_RATE", "type": "Decimal"}}, {"source": {"name": "GEN_MATURITY_DATE_FULL", "type": "DateTime"}, "sink": {"name": "GEN_MATURITY_DATE_FULL", "type": "DateTime"}}, {"source": {"name": "ISSUE_DATE", "type": "DateTime"}, "sink": {"name": "ISSUE_DATE", "type": "DateTime"}}, {"source": {"name": "LEG_NO", "type": "Decimal"}, "sink": {"name": "LEG_NO", "type": "Decimal"}}, {"source": {"name": "PAYMENT_DATE_FULL", "type": "DateTime"}, "sink": {"name": "PAYMENT_DATE_FULL", "type": "DateTime"}}, {"source": {"name": "SETTLEMENT_DATE_FULL", "type": "DateTime"}, "sink": {"name": "SETTLEMENT_DATE_FULL", "type": "DateTime"}}, {"source": {"name": "SIGN", "type": "Decimal"}, "sink": {"name": "SIGN", "type": "Decimal"}}, {"source": {"name": "TRADE_DATE_FULL", "type": "DateTime"}, "sink": {"name": "TRADE_DATE_FULL", "type": "DateTime"}}, {"source": {"name": "CUSIP", "type": "String"}, "sink": {"name": "CUSIP", "type": "String"}}, {"source": {"name": "SEDOL", "type": "String"}, "sink": {"name": "SEDOL", "type": "String"}}]}}, "inputs": [{"referenceName": "IMW_SCD_Oracle_UAT", "type": "DatasetReference", "parameters": {"Prefix": "SCDWH", "Table": "DM_TRANSACTIONS_COSTS"}}], "outputs": [{"referenceName": "DM_TRANSACTIONS_COSTS", "type": "DatasetReference"}]}, {"name": "DM_TRANSACTIONS_COSTS Row Count", "type": "Copy", "dependsOn": [{"activity": "Copy to SCDRefresh", "dependencyConditions": ["Succeeded"]}], "policy": {"timeout": "7.00:00:00", "retry": 0, "retryIntervalInSeconds": 30, "secureOutput": false, "secureInput": false}, "userProperties": [], "typeProperties": {"source": {"type": "OracleSource", "oracleReaderQuery": "select NULL AS ProcessTS,'DM_TRANSACTIONS_COSTS' AS TableName,\nCOUNT(DWH_EXTRACT_PROCESS_IK) AS SourceCount,\nNULL AS TargetCount,\nNULL AS SourceSUM ,\nNULL as TargetSUM,\nNULL AS Status\nfrom SCDWH.DM_TRANSACTIONS_COSTS", "partitionOption": "None", "queryTimeout": "02:00:00"}, "sink": {"type": "SqlMISink", "writeBehavior": "insert", "sqlWriterUseTableLock": false}, "enableStaging": false, "translator": {"type": "TabularTranslator", "mappings": [{"source": {"name": "PROCESSTS", "type": "String"}, "sink": {"name": "ProcessTS", "type": "DateTime", "physicalType": "datetime"}}, {"source": {"name": "TABLENAME", "type": "String"}, "sink": {"name": "TableName", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}, {"source": {"name": "SOURCECOUNT", "type": "Double"}, "sink": {"name": "SourceCount", "type": "Int32", "physicalType": "int"}}, {"source": {"name": "TARGETCOUNT", "type": "String"}, "sink": {"name": "TargetCount", "type": "Int32", "physicalType": "int"}}, {"source": {"name": "SOURCESUM", "type": "String"}, "sink": {"name": "SourceSUM", "type": "Double", "physicalType": "float"}}, {"source": {"name": "TARGETSUM", "type": "String"}, "sink": {"name": "TargetSUM", "type": "Double", "physicalType": "float"}}, {"source": {"name": "STATUS", "type": "String"}, "sink": {"name": "Status", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}], "typeConversion": true, "typeConversionSettings": {"allowDataTruncation": true, "treatBooleanAsNumber": false}}}, "inputs": [{"referenceName": "IMW_SCD_Oracle_UAT", "type": "DatasetReference", "parameters": {"Prefix": "SCDWH", "Table": "DM_TRANSACTIONS_COSTS"}}], "outputs": [{"referenceName": "ReconLog", "type": "DatasetReference"}]}, {"name": "Log Final Status DM_TRANSACTIONS_COSTS", "type": "Lookup", "dependsOn": [{"activity": "DM_TRANSACTIONS_COSTS Row Count", "dependencyConditions": ["Succeeded"]}], "policy": {"timeout": "7.00:00:00", "retry": 0, "retryIntervalInSeconds": 30, "secureOutput": false, "secureInput": false}, "userProperties": [], "typeProperties": {"source": {"type": "SqlMISource", "sqlReaderQuery": "update dbo.ReconLog\nSET ProcessTS = getdate(),\nTargetCount = (SELECT COUNT(*) FROM dbo.DM_TRANSACTIONS_COSTS),\nTargetSUM = NULL\nwhere TableName = 'DM_TRANSACTIONS_COSTS'\nand Status IS NULL\n\nupdate dbo.ReconLog\nset Status = (case when SourceCount = TargetCount  then 'Success' Else 'Failed' END)\nwhere Status IS NULL and TableName = 'DM_TRANSACTIONS_COSTS'\n\nSELECT top 1 * FROM dbo.ReconLog ", "partitionOption": "None"}, "dataset": {"referenceName": "DM_TRANSACTIONS_COSTS", "type": "DatasetReference"}}}, {"name": "Copy to SCDData", "type": "Copy", "dependsOn": [{"activity": "DM_TRANSACTIONS_COSTS to MI", "dependencyConditions": ["Succeeded"]}], "policy": {"timeout": "0.12:00:00", "retry": 0, "retryIntervalInSeconds": 30, "secureOutput": false, "secureInput": false}, "userProperties": [], "typeProperties": {"source": {"type": "SqlMISource", "partitionOption": "None"}, "sink": {"type": "SqlMISink", "preCopyScript": "TRUNCATE TABLE SCDData.dbo.DM_TRANSACTIONS_COSTS", "writeBehavior": "insert", "sqlWriterUseTableLock": false}, "enableStaging": false, "translator": {"type": "TabularTranslator", "typeConversion": true, "typeConversionSettings": {"allowDataTruncation": true, "treatBooleanAsNumber": false}}}, "inputs": [{"referenceName": "SQLMI_PARAM_CSTMT", "type": "DatasetReference", "parameters": {"DBName": "SCDStaging", "SchemaName": "dbo", "TableName": "DM_TRANSACTIONS_COSTS"}}], "outputs": [{"referenceName": "SQLMI_PARAM_CSTMT", "type": "DatasetReference", "parameters": {"DBName": "SCDData", "SchemaName": "dbo", "TableName": "DM_TRANSACTIONS_COSTS"}}]}, {"name": "Copy to SCDRefresh", "type": "Copy", "state": "Inactive", "onInactiveMarkAs": "Succeeded", "dependsOn": [{"activity": "Copy to SCDData", "dependencyConditions": ["Succeeded"]}], "policy": {"timeout": "0.12:00:00", "retry": 0, "retryIntervalInSeconds": 30, "secureOutput": false, "secureInput": false}, "userProperties": [], "typeProperties": {"source": {"type": "SqlMISource", "partitionOption": "None"}, "sink": {"type": "SqlMISink", "preCopyScript": "TRUNCATE TABLE SCDRefresh.dbo.DM_TRANSACTIONS_COSTS", "writeBehavior": "insert", "sqlWriterUseTableLock": false}, "enableStaging": false, "translator": {"type": "TabularTranslator", "typeConversion": true, "typeConversionSettings": {"allowDataTruncation": true, "treatBooleanAsNumber": false}}}, "inputs": [{"referenceName": "SQLMI_PARAM_CSTMT", "type": "DatasetReference", "parameters": {"DBName": "SCDStaging", "SchemaName": "dbo", "TableName": "DM_TRANSACTIONS_COSTS"}}], "outputs": [{"referenceName": "SQLMI_PARAM_CSTMT", "type": "DatasetReference", "parameters": {"DBName": "SCDRefresh", "SchemaName": "dbo", "TableName": "DM_TRANSACTIONS_COSTS"}}]}]}}, {"name": "12 DM_CIT_LLC_FUND_PERFORMANCE Load to MI", "type": "IfCondition", "dependsOn": [], "userProperties": [], "typeProperties": {"expression": {"value": "@equals(pipeline().parameters.Load_DM_CIT_LLC_FUND_PERFORMANCE, '1')", "type": "Expression"}, "ifTrueActivities": [{"name": "DM_CIT_LLC_FUND_PERFORMANCE", "type": "Copy", "dependsOn": [], "policy": {"timeout": "7.00:00:00", "retry": 0, "retryIntervalInSeconds": 30, "secureOutput": false, "secureInput": false}, "userProperties": [], "typeProperties": {"source": {"type": "OracleSource", "partitionOption": "None", "queryTimeout": "02:00:00"}, "sink": {"type": "SqlMISink", "preCopyScript": "delete from dbo.DM_CIT_LLC_FUND_PERFORMANCE", "writeBehavior": "insert", "sqlWriterUseTableLock": false}, "enableStaging": false, "translator": {"type": "TabularTranslator", "mappings": [{"source": {"name": "DWH_EXTRACT_PROCESS_IK", "type": "Decimal"}, "sink": {"name": "DWH_EXTRACT_PROCESS_IK", "type": "Int64", "physicalType": "bigint"}}, {"source": {"name": "DWH_LOAD_TS", "type": "DateTime"}, "sink": {"name": "DWH_LOAD_TS", "type": "DateTime", "physicalType": "datetime"}}, {"source": {"name": "PERF_CIT_LLC_FUND_KEY", "type": "Decimal"}, "sink": {"name": "PERF_CIT_LLC_FUND_KEY", "type": "Int64", "physicalType": "bigint"}}, {"source": {"name": "FUND_NAME", "type": "String"}, "sink": {"name": "FUND_NAME", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}, {"source": {"name": "FUND_NAME_OLD", "type": "String"}, "sink": {"name": "FUND_NAME_OLD", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}, {"source": {"name": "FUND_CODE", "type": "String"}, "sink": {"name": "FUND_CODE", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}, {"source": {"name": "CLASS_NAME", "type": "String"}, "sink": {"name": "CLASS_NAME", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}, {"source": {"name": "SERIES", "type": "String"}, "sink": {"name": "SERIES", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}, {"source": {"name": "RETURN_TYPE", "type": "String"}, "sink": {"name": "RETURN_TYPE", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}, {"source": {"name": "AS_OF_DATE", "type": "String"}, "sink": {"name": "AS_OF_DATE", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}, {"source": {"name": "INCEPTION_DATE", "type": "String"}, "sink": {"name": "INCEPTION_DATE", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}, {"source": {"name": "RETURN_CURRENCY", "type": "String"}, "sink": {"name": "RETURN_CURRENCY", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}, {"source": {"name": "CALENDAR_YTD", "type": "String"}, "sink": {"name": "CALENDAR_YTD", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}, {"source": {"name": "INCEPTION_TO_DATE", "type": "String"}, "sink": {"name": "INCEPTION_TO_DATE", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}, {"source": {"name": "MONTH_TO_DATE", "type": "String"}, "sink": {"name": "MONTH_TO_DATE", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}, {"source": {"name": "ONE_DAY", "type": "String"}, "sink": {"name": "ONE_DAY", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}, {"source": {"name": "THREE_MONTHS", "type": "String"}, "sink": {"name": "THREE_MONTHS", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}, {"source": {"name": "FIVE_YEARS", "type": "String"}, "sink": {"name": "FIVE_YEARS", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}, {"source": {"name": "TEN_YEARS", "type": "String"}, "sink": {"name": "TEN_YEARS", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}, {"source": {"name": "CURRENT_PRICE", "type": "String"}, "sink": {"name": "CURRENT_PRICE", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}, {"source": {"name": "PREVIOUS_PRICE", "type": "String"}, "sink": {"name": "PREVIOUS_PRICE", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}, {"source": {"name": "PRICE_CHANGE", "type": "String"}, "sink": {"name": "PRICE_CHANGE", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}, {"source": {"name": "PCT_PRICE_CHANGE", "type": "String"}, "sink": {"name": "PCT_PRICE_CHANGE", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}, {"source": {"name": "INCOME_DIST_RATE", "type": "String"}, "sink": {"name": "INCOME_DIST_RATE", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}, {"source": {"name": "TOTAL_NET_ASSETS", "type": "String"}, "sink": {"name": "TOTAL_NET_ASSETS", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}, {"source": {"name": "UNITS_OUTSTANDING", "type": "String"}, "sink": {"name": "UNITS_OUTSTANDING", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}, {"source": {"name": "CAP_GAINS_DIST_FACTOR", "type": "String"}, "sink": {"name": "CAP_GAINS_DIST_FACTOR", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}, {"source": {"name": "CAP_SUBSCRIPTIONS", "type": "String"}, "sink": {"name": "CAP_SUBSCRIPTIONS", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}, {"source": {"name": "CAP_REDEMPTIONS", "type": "String"}, "sink": {"name": "CAP_REDEMPTIONS", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}, {"source": {"name": "ORIGINATING_SYSTEM_NO", "type": "Decimal"}, "sink": {"name": "ORIGINATING_SYSTEM_NO", "type": "Int64", "physicalType": "bigint"}}, {"source": {"name": "ONE_YEAR", "type": "String"}, "sink": {"name": "ONE_YEAR", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}, {"source": {"name": "THREE_YEARS", "type": "String"}, "sink": {"name": "THREE_YEARS", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}, {"source": {"name": "LAST_CHANGE_TS", "type": "DateTime"}, "sink": {"name": "LAST_CHANGE_TS", "type": "DateTime", "physicalType": "datetime"}}], "typeConversion": true, "typeConversionSettings": {"allowDataTruncation": true, "treatBooleanAsNumber": false}}}, "inputs": [{"referenceName": "IMW_SCD_Oracle_UAT", "type": "DatasetReference", "parameters": {"Prefix": "SCDWH", "Table": "DM_CIT_LLC_FUND_PERFORMANCE"}}], "outputs": [{"referenceName": "DM_CIT_LLC_FUND_PERFORMANCE", "type": "DatasetReference"}]}, {"name": "DM_CIT_LLC_FUND_PERFORMANCE_IMW_Recon", "type": "Copy", "dependsOn": [{"activity": "DM_CIT_LLC_FUND_PERFORMANCE", "dependencyConditions": ["Succeeded"]}], "policy": {"timeout": "7.00:00:00", "retry": 0, "retryIntervalInSeconds": 30, "secureOutput": false, "secureInput": false}, "userProperties": [], "typeProperties": {"source": {"type": "OracleSource", "oracleReaderQuery": "select NULL AS ProcessTS,'DM_CIT_LLC_FUND_PERFORMANCE' AS TableName,\nCOUNT(PERF_CIT_LLC_FUND_KEY) AS SourceCount,\nNULL AS TargetCount,\nNULL AS SourceSUM ,\nNULL as TargetSUM,\nNULL AS Status\n\nfrom SCDWH.DM_CIT_LLC_FUND_PERFORMANCE", "partitionOption": "None", "queryTimeout": "02:00:00"}, "sink": {"type": "SqlMISink", "writeBehavior": "insert", "sqlWriterUseTableLock": false}, "enableStaging": false, "translator": {"type": "TabularTranslator", "mappings": [{"source": {"name": "PROCESSTS", "type": "String"}, "sink": {"name": "ProcessTS", "type": "DateTime", "physicalType": "datetime"}}, {"source": {"name": "TABLENAME", "type": "String"}, "sink": {"name": "TableName", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}, {"source": {"name": "SOURCECOUNT", "type": "Double"}, "sink": {"name": "SourceCount", "type": "Int32", "physicalType": "int"}}, {"source": {"name": "TARGETCOUNT", "type": "String"}, "sink": {"name": "TargetCount", "type": "Int32", "physicalType": "int"}}, {"source": {"name": "SOURCESUM", "type": "String"}, "sink": {"name": "SourceSUM", "type": "Double", "physicalType": "float"}}, {"source": {"name": "TARGETSUM", "type": "String"}, "sink": {"name": "TargetSUM", "type": "Double", "physicalType": "float"}}, {"source": {"name": "STATUS", "type": "String"}, "sink": {"name": "Status", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}], "typeConversion": true, "typeConversionSettings": {"allowDataTruncation": true, "treatBooleanAsNumber": false}}}, "inputs": [{"referenceName": "IMW_SCD_Oracle_UAT", "type": "DatasetReference", "parameters": {"Prefix": "SCDWH", "Table": "DM_CIT_LLC_FUND_PERFORMANCE"}}], "outputs": [{"referenceName": "ReconLog", "type": "DatasetReference"}]}, {"name": "Log Final Status", "type": "Lookup", "dependsOn": [{"activity": "DM_CIT_LLC_FUND_PERFORMANCE_IMW_Recon", "dependencyConditions": ["Succeeded"]}], "policy": {"timeout": "7.00:00:00", "retry": 0, "retryIntervalInSeconds": 30, "secureOutput": false, "secureInput": false}, "userProperties": [], "typeProperties": {"source": {"type": "SqlMISource", "sqlReaderQuery": "update dbo.ReconLog\nSET ProcessTS = getdate(),\nTargetCount = (SELECT COUNT(*) FROM dbo.DM_CIT_LLC_FUND_PERFORMANCE),\nTargetSUM = NULL\nwhere TableName = 'DM_CIT_LLC_FUND_PERFORMANCE'\nand Status IS NULL\n\nupdate dbo.ReconLog\nset Status = (case when SourceCount = TargetCount then 'Success' Else 'Failed' END)\nwhere Status IS NULL and TableName = 'DM_CIT_LLC_FUND_PERFORMANCE'\n\nSELECT top 1 * FROM dbo.ReconLog ", "partitionOption": "None"}, "dataset": {"referenceName": "ReconLog", "type": "DatasetReference"}}}]}}], "parameters": {"Load_DM_TRANSACTIONS_COSTS": {"type": "string", "defaultValue": "1"}, "Load_DM_CIT_LLC_FUND_PERFORMANCE": {"type": "string", "defaultValue": "0"}}, "folder": {"name": "Internal/IMW/IWM-MI CS CP/CompleteLoad"}, "annotations": [], "lastPublishTime": "2024-08-07T22:58:38Z"}, "type": "Microsoft.DataFactory/factories/pipelines"}