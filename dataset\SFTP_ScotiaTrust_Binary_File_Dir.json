{"name": "SFTP_ScotiaTrust_Binary_File_Dir", "properties": {"linkedServiceName": {"referenceName": "ScotiaTrustFileShare", "type": "LinkedServiceReference"}, "parameters": {"Path": {"type": "string"}}, "folder": {"name": "SFTP"}, "annotations": [], "type": "Binary", "typeProperties": {"location": {"type": "AzureFileStorageLocation", "folderPath": {"value": "@dataset().Path", "type": "Expression"}}}}, "type": "Microsoft.DataFactory/factories/datasets"}