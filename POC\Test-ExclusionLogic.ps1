# Test script for ADF Pipeline Exclusion Logic
# This script validates the exclusion mechanism with sample data

param(
    [Parameter(Mandatory=$false)]
    [string]$TestDataPath = "./test-data",
    
    [Parameter(Mandatory=$false)]
    [switch]$CreateTestData
)

Write-Host "=== ADF Pipeline Exclusion Logic Test ===" -ForegroundColor Magenta

# Create test data if requested
if ($CreateTestData) {
    Write-Host "Creating test data..." -ForegroundColor Yellow
    
    if (-not (Test-Path $TestDataPath)) {
        New-Item -ItemType Directory -Path $TestDataPath -Force | Out-Null
    }
    
    # Create pipeline directory
    $pipelineDir = Join-Path $TestDataPath "pipeline"
    if (-not (Test-Path $pipelineDir)) {
        New-Item -ItemType Directory -Path $pipelineDir -Force | Out-Null
    }
    
    # Create sample pipelines
    $pipelines = @(
        @{
            name = "PROD-Pipeline-1"
            annotations = @("PRODUCTION", "CRITICAL")
            shouldExclude = $false
        },
        @{
            name = "DEV-ONLY-Test-Pipeline"
            annotations = @("DEV_ONLY", "TESTING")
            shouldExclude = $true
        },
        @{
            name = "Regular-Pipeline"
            annotations = @("STANDARD")
            shouldExclude = $false
        },
        @{
            name = "EXPERIMENTAL-Pipeline"
            annotations = @("EXPERIMENTAL", "EXCLUDE_FROM_PROD")
            shouldExclude = $true
        }
    )
    
    foreach ($pipeline in $pipelines) {
        $pipelineContent = @{
            name = $pipeline.name
            properties = @{
                description = "Test pipeline for exclusion logic validation"
                activities = @(
                    @{
                        name = "Sample Activity"
                        type = "Wait"
                        typeProperties = @{
                            waitTimeInSeconds = 1
                        }
                    }
                )
                annotations = $pipeline.annotations
                folder = @{
                    name = if ($pipeline.shouldExclude) { "DEV-Only" } else { "Production" }
                }
            }
            type = "Microsoft.DataFactory/factories/pipelines"
        }
        
        $fileName = "$($pipeline.name).json"
        $filePath = Join-Path $pipelineDir $fileName
        $pipelineContent | ConvertTo-Json -Depth 5 | Out-File $filePath -Encoding UTF8
        
        Write-Host "Created test pipeline: $fileName" -ForegroundColor Green
    }
    
    # Create trigger directory and sample trigger
    $triggerDir = Join-Path $TestDataPath "trigger"
    if (-not (Test-Path $triggerDir)) {
        New-Item -ItemType Directory -Path $triggerDir -Force | Out-Null
    }
    
    $triggerContent = @{
        name = "DEV-ONLY-Trigger"
        properties = @{
            annotations = @("DEV_ONLY")
            runtimeState = "Started"
            pipelines = @(
                @{
                    pipelineReference = @{
                        referenceName = "DEV-ONLY-Test-Pipeline"
                        type = "PipelineReference"
                    }
                }
            )
            type = "ScheduleTrigger"
            typeProperties = @{
                recurrence = @{
                    frequency = "Day"
                    interval = 1
                }
            }
        }
    }
    
    $triggerPath = Join-Path $triggerDir "DEV-ONLY-Trigger.json"
    $triggerContent | ConvertTo-Json -Depth 5 | Out-File $triggerPath -Encoding UTF8
    Write-Host "Created test trigger: DEV-ONLY-Trigger.json" -ForegroundColor Green
    
    Write-Host "Test data created successfully!" -ForegroundColor Green
}

# Run the exclusion filter on test data
if (Test-Path $TestDataPath) {
    Write-Host "`nRunning exclusion filter on test data..." -ForegroundColor Yellow
    
    $outputPath = Join-Path $TestDataPath "filtered-output"
    
    # Run the filter script
    $filterScript = Join-Path $PSScriptRoot "Filter-ADFPipelines.ps1"
    if (Test-Path $filterScript) {
        & $filterScript -SourcePath $TestDataPath -TargetPath $outputPath -ExclusionAnnotations @("DEV_ONLY", "EXCLUDE_FROM_PROD", "EXPERIMENTAL")
        
        # Validate results
        Write-Host "`n=== VALIDATION RESULTS ===" -ForegroundColor Magenta
        
        $originalPipelines = Get-ChildItem "$TestDataPath/pipeline" -Filter "*.json" | Measure-Object
        $filteredPipelines = Get-ChildItem "$outputPath/pipeline" -Filter "*.json" -ErrorAction SilentlyContinue | Measure-Object
        
        Write-Host "Original Pipelines: $($originalPipelines.Count)" -ForegroundColor White
        Write-Host "Filtered Pipelines: $($filteredPipelines.Count)" -ForegroundColor White
        Write-Host "Excluded Pipelines: $($originalPipelines.Count - $filteredPipelines.Count)" -ForegroundColor White
        
        # Check if exclusion report was generated
        $reportPath = Join-Path $outputPath "exclusion-report.json"
        if (Test-Path $reportPath) {
            Write-Host "`nExclusion report generated successfully!" -ForegroundColor Green
            $report = Get-Content $reportPath | ConvertFrom-Json
            Write-Host "Report timestamp: $($report.timestamp)" -ForegroundColor Cyan
            Write-Host "Excluded pipelines: $($report.excludedPipelines -join ', ')" -ForegroundColor Red
            Write-Host "Included pipelines: $($report.includedPipelines -join ', ')" -ForegroundColor Green
        }
        
        Write-Host "`nTest completed successfully!" -ForegroundColor Green
    } else {
        Write-Error "Filter script not found: $filterScript"
    }
} else {
    Write-Warning "Test data path not found: $TestDataPath"
    Write-Host "Run with -CreateTestData to generate test data first." -ForegroundColor Yellow
}

Write-Host "`n=== Test Summary ===" -ForegroundColor Magenta
Write-Host "The exclusion logic test validates that:" -ForegroundColor White
Write-Host "1. Pipelines with exclusion annotations are filtered out" -ForegroundColor White
Write-Host "2. Triggers referencing excluded pipelines are also excluded" -ForegroundColor White
Write-Host "3. Exclusion reports are generated correctly" -ForegroundColor White
Write-Host "4. Non-excluded artifacts are copied properly" -ForegroundColor White
