{"name": "SQLMI_MOW_PARAM", "properties": {"linkedServiceName": {"referenceName": "SQLMI_MOW_PRD", "type": "LinkedServiceReference", "parameters": {"DBName": {"value": "@dataset().DBName", "type": "Expression"}}}, "parameters": {"DBName": {"type": "string"}, "SchemaName": {"type": "string"}, "TableName": {"type": "string"}}, "folder": {"name": "General"}, "annotations": [], "type": "AzureSqlMITable", "schema": [], "typeProperties": {"schema": {"value": "@dataset().SchemaName", "type": "Expression"}, "table": {"value": "@dataset().TableName", "type": "Expression"}}}, "type": "Microsoft.DataFactory/factories/datasets"}