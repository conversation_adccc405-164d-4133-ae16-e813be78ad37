{"name": "SFTP_SCD_PARAMS", "properties": {"linkedServiceName": {"referenceName": "SCD_SFTP_Prod", "type": "LinkedServiceReference"}, "parameters": {"FileName": {"type": "string"}, "FilePath": {"type": "string"}, "Delimiter": {"type": "string"}}, "folder": {"name": "General"}, "annotations": [], "type": "DelimitedText", "typeProperties": {"location": {"type": "SftpLocation", "fileName": {"value": "@dataset().FileName", "type": "Expression"}, "folderPath": {"value": "@dataset().FilePath", "type": "Expression"}}, "columnDelimiter": {"value": "@dataset().Delimiter", "type": "Expression"}, "escapeChar": "\\", "firstRowAsHeader": true, "quoteChar": ""}, "schema": []}, "type": "Microsoft.DataFactory/factories/datasets"}