{"name": "DECOM- IN-416 Mawer Order Flow", "properties": {"activities": [{"name": "Generate Mawer order source file", "type": "Copy", "dependsOn": [{"activity": "Set variable1", "dependencyConditions": ["Succeeded"]}], "policy": {"timeout": "7.00:00:00", "retry": 0, "retryIntervalInSeconds": 30, "secureOutput": false, "secureInput": false}, "userProperties": [], "typeProperties": {"source": {"type": "SqlServerSource", "sqlReaderQuery": "\nDeclare @startdate Datetime;\nset @Startdate=CAST(CURRENT_TIMESTAMP -1 AS DATE);\n\nSELECT        o.ORDER_ID AS orderid, o.CREATE_DATE AS ordercreatedate, o.TO_TRADER_DATE AS orderreleasedate, o.TRANS_TYPE AS side, o.INSTRUCTION AS orderinstruction, o.LIMIT_PRICE AS managerlimit,\n                         o.TDR_LIMIT_PRICE AS traderlimit, o.FIX_STOPPX AS stopprice, o.EXEC_BROKER AS orderexecutingbroker, o.BROKER_REASON AS orderbrokerreason, o.IPO, o.USR_CLASS_CD_3 AS secondary,\n                         o.STATUS AS orderstatus, o.ORDER_DURATION AS timeinforce, o.NET_TRADE_IND AS nettradeind, o.CREATE_USER AS createuser, o.MANAGER AS ordermanagerid, o.TRADER AS ordertraderid,\n                         o.TRADE_DATE AS tradedate, o.SETTLE_DATE AS settledate, o.SEC_ID AS crdinternalsecurityid, s.SEC_NAME AS securityname, s.CUSIP AS cusip, s.<PERSON>ICKER AS ticker, s.SEDOL AS sedol, s.ISIN_NO AS isin,\n                         s.<PERSON>L<PERSON>AN AS valoran, sc.UDF_CHAR8 AS bloombergid, s.SEC_TYP_CD AS securitytype, o.TARGET_QTY AS ordertargetqty, o.EXEC_QTY AS orderexecqty, o.EXEC_PRICE AS orderaverageprice,\n                         o.EXEC_AMT AS orderprinamount, o.COMMISION_AMT AS ordercommissionamt, o.FEE_1 + o.FEE_2 + o.FEE_3 + o.FEE_4 + o.FEE_5 + o.FEE_6 AS ordertotalfees, o.NET_PRIN_AMT AS ordernetamount,\n                         o.REASON_CD AS orderreason, s.ISSUE_CNTRY_CD AS countryofissue, s.CNTRY_OF_RISK AS countryofrisk, o.EXCH_CD AS exchangecode, o.PRIN_LOCAL_CRRNCY AS currencycode, oss.PROG_TRD_ID,\n                         CASE WHEN ISNULL(oss.PROG_TRD_ID, 0) = 0 THEN 'Regular' ELSE 'Program' END AS Program\nFROM            [MWR-SRV-crSQL1A].[CRDMAINMAWIM].[dbo].[TS_ORDER] AS o INNER JOIN\n                         [MWR-SRV-crSQL1A].[CRDMAINMAWIM].[dbo].[CSM_SECURITY] AS s ON o.SEC_ID = s.SEC_ID INNER JOIN\n                         [MWR-SRV-crSQL1A].[CRDMAINMAWIM].[dbo].[TS_ORDER_SEC_SPEC] AS oss ON o.ORDER_ID = oss.ORDER_ID INNER JOIN\n                         [MWR-SRV-crSQL1A].[CRDMAINMAWIM].[dbo].[CSM_SECURITY_CUST] AS sc ON s.SEC_ID = sc.SEC_ID\nWHERE        (o.TRADE_DATE >=@startdate) AND (o.TRADE_DATE <=@startdate)", "queryTimeout": "02:00:00", "partitionOption": "None"}, "sink": {"type": "DelimitedTextSink", "storeSettings": {"type": "AzureBlobFSWriteSettings"}, "formatSettings": {"type": "DelimitedTextWriteSettings", "quoteAllText": true, "fileExtension": ".txt"}}, "enableStaging": false, "translator": {"type": "TabularTranslator", "typeConversion": true, "typeConversionSettings": {"allowDataTruncation": true, "treatBooleanAsNumber": false}}}, "inputs": [{"referenceName": "ODSExternalData_MawerIHSMarkit_sql", "type": "DatasetReference"}], "outputs": [{"referenceName": "AzureDataLakeStorage_MawerIHSmarkitsink_csv", "type": "DatasetReference", "parameters": {"FileName": {"value": "@variables('FileName')", "type": "Expression"}}}]}, {"name": "Set variable1", "type": "SetVariable", "dependsOn": [], "policy": {"secureOutput": false, "secureInput": false}, "userProperties": [], "typeProperties": {"variableName": "FileName", "value": {"value": "@concat('Mawer Order_',formatDateTime(convertFromUtc(adddays(utcnow(),-1),'Mountain Standard Time'),'MMddyy'),'.txt')", "type": "Expression"}}}, {"name": "Archive", "type": "Copy", "dependsOn": [{"activity": "Generate Mawer order source file", "dependencyConditions": ["Succeeded"]}], "policy": {"timeout": "7.00:00:00", "retry": 0, "retryIntervalInSeconds": 30, "secureOutput": false, "secureInput": false}, "userProperties": [], "typeProperties": {"source": {"type": "BinarySource", "storeSettings": {"type": "AzureBlobFSReadSettings", "recursive": false, "wildcardFolderPath": "DDI Migration/Mawer IHSMarkit - workflow/Outputfiles", "wildcardFileName": {"value": "@variables('FileName')", "type": "Expression"}, "deleteFilesAfterCompletion": true}, "formatSettings": {"type": "BinaryReadSettings"}}, "sink": {"type": "BinarySink", "storeSettings": {"type": "AzureBlobFSWriteSettings"}}, "enableStaging": false}, "inputs": [{"referenceName": "AzureDataLakeStorageMawerIHSMarkit_Binary_Source", "type": "DatasetReference"}], "outputs": [{"referenceName": "AzureDataLakeStorage_MawerIHSMarkit_Binary_Sink", "type": "DatasetReference"}]}], "variables": {"FileName": {"type": "String"}}, "folder": {"name": "Internal/DDI/IN-329 DDI Migration/IN-384 Mawer IHSMarkit - TransactionCostAnalysis - Workflow"}, "annotations": [], "lastPublishTime": "2023-12-01T04:11:14Z"}, "type": "Microsoft.DataFactory/factories/pipelines"}