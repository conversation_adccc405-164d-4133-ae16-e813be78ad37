{"name": "IN-152 Pershing FX", "properties": {"activities": [{"name": "Archive Folder", "type": "GetMetadata", "dependsOn": [], "policy": {"timeout": "0.12:00:00", "retry": 0, "retryIntervalInSeconds": 30, "secureOutput": false, "secureInput": false}, "userProperties": [], "typeProperties": {"dataset": {"referenceName": "ADL_Binary_Dir", "type": "DatasetReference", "parameters": {"Path": {"value": "@concat(pipeline().parameters.pADLPath,'/ARCHIVE/',pipeline().parameters.pDate,'/OUT')", "type": "Expression"}}}, "fieldList": ["exists"], "storeSettings": {"type": "AzureBlobFSReadSettings", "recursive": true, "enablePartitionDiscovery": false}, "formatSettings": {"type": "BinaryReadSettings"}}}, {"name": "If file is not in ARCHIVE", "type": "IfCondition", "dependsOn": [{"activity": "Archive Folder", "dependencyConditions": ["Succeeded"]}], "userProperties": [], "typeProperties": {"expression": {"value": "@not(activity('Archive Folder').output.exists)", "type": "Expression"}, "ifTrueActivities": [{"name": "Create FX File", "type": "Copy", "dependsOn": [], "policy": {"timeout": "7.00:00:00", "retry": 0, "retryIntervalInSeconds": 30, "secureOutput": false, "secureInput": false}, "userProperties": [], "typeProperties": {"source": {"type": "SqlMISource", "sqlReaderStoredProcedureName": "[Integration].[FX].[sp_PershingFX]", "storedProcedureParameters": {"DataDate": {"type": "String", "value": {"value": "@pipeline().parameters.pDate", "type": "Expression"}}}, "partitionOption": "None"}, "sink": {"type": "DelimitedTextSink", "storeSettings": {"type": "AzureBlobFSWriteSettings"}, "formatSettings": {"type": "DelimitedTextWriteSettings", "quoteAllText": true, "fileExtension": ".txt"}}, "enableStaging": false, "translator": {"type": "TabularTranslator", "typeConversion": true, "typeConversionSettings": {"allowDataTruncation": true, "treatBooleanAsNumber": false}}}, "inputs": [{"referenceName": "SQLMI_MOW_PARAM", "type": "DatasetReference", "parameters": {"DBName": "Integration", "SchemaName": "FX", "TableName": "sp_PershingFX"}}], "outputs": [{"referenceName": "ADL_Delimited_File", "type": "DatasetReference", "parameters": {"FilePath": {"value": "@concat(pipeline().parameters.pADLPath,'/OUT')", "type": "Expression"}, "FileName": {"value": "@concat(pipeline().parameters.pDate,\nformatDateTime(utcnow(),'HHmmss'),'_',\nsubstring(pipeline().parameters.pDate,4,2),\nsubstring(pipeline().parameters.pDate,6,2),\nsubstring(pipeline().parameters.pDate,2,2),\n'_Pershing_FXTrades.csv')", "type": "Expression"}, "ColDelimiter": ",", "FirstRowHeader": true}}]}, {"name": "Set vSendToSCD variable", "type": "SetVariable", "dependsOn": [{"activity": "Create FX File", "dependencyConditions": ["Succeeded"]}], "policy": {"secureOutput": false, "secureInput": false}, "userProperties": [], "typeProperties": {"variableName": "vSendToSCD", "value": {"value": "@bool(pipeline().parameters.pSendToSCD)", "type": "Expression"}}}]}}, {"name": "Archive OUT", "type": "Copy", "dependsOn": [{"activity": "If send the files to SCD", "dependencyConditions": ["Succeeded"]}], "policy": {"timeout": "7.00:00:00", "retry": 0, "retryIntervalInSeconds": 30, "secureOutput": false, "secureInput": false}, "userProperties": [], "typeProperties": {"source": {"type": "BinarySource", "storeSettings": {"type": "AzureBlobFSReadSettings", "recursive": true, "deleteFilesAfterCompletion": true}, "formatSettings": {"type": "BinaryReadSettings"}}, "sink": {"type": "BinarySink", "storeSettings": {"type": "AzureBlobFSWriteSettings"}}, "enableStaging": false}, "inputs": [{"referenceName": "ADL_Binary_Dir", "type": "DatasetReference", "parameters": {"Path": {"value": "@concat(pipeline().parameters.pADLPath,'/OUT')", "type": "Expression"}}}], "outputs": [{"referenceName": "ADL_Binary_Dir", "type": "DatasetReference", "parameters": {"Path": {"value": "@concat(pipeline().parameters.pADLPath,'/ARCHIVE/',pipeline().parameters.pDate,'/OUT')", "type": "Expression"}}}]}, {"name": "If send the files to SCD", "type": "IfCondition", "dependsOn": [{"activity": "If file is not in ARCHIVE", "dependencyConditions": ["Succeeded"]}], "userProperties": [], "typeProperties": {"expression": {"value": "@variables('vSendToSCD')", "type": "Expression"}, "ifTrueActivities": [{"name": "Copy Output file to SCD SFTP", "type": "Copy", "dependsOn": [], "policy": {"timeout": "7.00:00:00", "retry": 0, "retryIntervalInSeconds": 30, "secureOutput": false, "secureInput": false}, "userProperties": [], "typeProperties": {"source": {"type": "BinarySource", "storeSettings": {"type": "AzureBlobFSReadSettings", "recursive": false, "deleteFilesAfterCompletion": false}, "formatSettings": {"type": "BinaryReadSettings"}}, "sink": {"type": "BinarySink", "storeSettings": {"type": "SftpWriteSettings", "operationTimeout": "01:00:00", "useTempFileRename": true}}, "enableStaging": false}, "inputs": [{"referenceName": "ADL_Binary_Dir", "type": "DatasetReference", "parameters": {"Path": {"value": "@concat(pipeline().parameters.pADLPath,'/OUT')", "type": "Expression"}}}], "outputs": [{"referenceName": "SCD_SFTP_Binary_Dir", "type": "DatasetReference", "parameters": {"Path": {"value": "@pipeline().parameters.pSCDsftpPath", "type": "Expression"}}}]}]}}], "parameters": {"pDate": {"type": "string", "defaultValue": "20240117"}, "pADLPath": {"type": "string", "defaultValue": "Custodian Data/Pershing/Pershing FX"}, "pADLContainer": {"type": "string", "defaultValue": "scdintegration"}, "pSendToSCD": {"type": "bool", "defaultValue": false}, "pSCDsftpPath": {"type": "string", "defaultValue": "a"}, "pDataType": {"type": "string"}}, "variables": {"vSendToSCD": {"type": "Boolean", "defaultValue": false}}, "folder": {"name": "Custodians and Vendors/Pershing/Pershing FX"}, "annotations": [], "lastPublishTime": "2024-08-14T21:31:32Z"}, "type": "Microsoft.DataFactory/factories/pipelines"}