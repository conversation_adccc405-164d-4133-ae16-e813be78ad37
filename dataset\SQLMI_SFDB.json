{"name": "SQLMI_SFDB", "properties": {"linkedServiceName": {"referenceName": "AzureSQLManagedInstance", "type": "LinkedServiceReference", "parameters": {"dbName": {"value": "@if(equals(pipeline().DataFactory,'AZ-PRD-UW-DF-SCD'),'SalesforceData','Salesforce' )", "type": "Expression"}}}, "folder": {"name": "Inbound/Salesforce/IN-193 DW Inbound Interface/IN-196 SF To IMW"}, "annotations": [], "type": "AzureSqlMITable", "schema": []}, "type": "Microsoft.DataFactory/factories/datasets"}