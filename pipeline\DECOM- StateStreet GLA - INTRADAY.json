{"name": "DECOM- StateStreet GLA - INTRADAY", "properties": {"activities": [{"name": "Set Date Variable", "type": "SetVariable", "dependsOn": [{"activity": "Only Run in PROD ADF", "dependencyConditions": ["Succeeded"]}], "policy": {"secureOutput": false, "secureInput": false}, "userProperties": [], "typeProperties": {"variableName": "Date", "value": {"value": "@formatDateTime(convertFromUtc(utcnow(),'Mountain Standard Time'),'yyyyMMdd')", "type": "Expression"}}}, {"name": "Read from State Street FileShare", "type": "GetMetadata", "dependsOn": [{"activity": "Set Date Variable", "dependencyConditions": ["Succeeded"]}], "policy": {"timeout": "7.00:00:00", "retry": 0, "retryIntervalInSeconds": 30, "secureOutput": false, "secureInput": false}, "userProperties": [], "typeProperties": {"dataset": {"referenceName": "FileShare_StateStreetBilling", "type": "DatasetReference"}, "fieldList": ["childItems"], "storeSettings": {"type": "AzureFileStorageReadSettings", "recursive": true, "enablePartitionDiscovery": false}, "formatSettings": {"type": "DelimitedTextReadSettings"}}}, {"name": "Filter Out INTRADAYGLA Files", "type": "Filter", "dependsOn": [{"activity": "Read from State Street FileShare", "dependencyConditions": ["Succeeded"]}], "userProperties": [], "typeProperties": {"items": {"value": "@activity('Read from State Street FileShare').output.childItems", "type": "Expression"}, "condition": {"value": "@AND(contains(item().name, variables('Date')),contains(item().name, 'INTRADAYGLA'))", "type": "Expression"}}}, {"name": "Check If INTRADAYGLA Exists", "type": "IfCondition", "dependsOn": [{"activity": "Filter Out INTRADAYGLA Files", "dependencyConditions": ["Succeeded"]}], "userProperties": [], "typeProperties": {"expression": {"value": "@greater(activity('Filter Out INTRADAYGLA Files').output.FilteredItemsCount,0)", "type": "Expression"}, "ifFalseActivities": [{"name": "Update Log with File NOT Found", "type": "Lookup", "dependsOn": [], "policy": {"timeout": "7.00:00:00", "retry": 0, "retryIntervalInSeconds": 30, "secureOutput": false, "secureInput": false}, "userProperties": [], "typeProperties": {"source": {"type": "SqlMISource", "sqlReaderQuery": {"value": "@concat('INSERT INTO Billing.StateStreet.GLAFileLogs\n\nSELECT ''',variables('InputFileName'),\n''',''INTRADAY'', 0,''',variables('Date'),''',''Azure''', ' select 1 ')", "type": "Expression"}, "partitionOption": "None"}, "dataset": {"referenceName": "SQLMI_MOW_PARAM", "type": "DatasetReference", "parameters": {"DBName": "Billing", "SchemaName": "StateStreet", "TableName": "GLAFileLogs"}}}}], "ifTrueActivities": [{"name": "Run SS GLA - Child Pipeline", "type": "ExecutePipeline", "dependsOn": [], "policy": {"secureInput": false}, "userProperties": [], "typeProperties": {"pipeline": {"referenceName": "DECOM- StateStreet GLA - Child", "type": "PipelineReference"}, "waitOnCompletion": true, "parameters": {"InputFileName": {"value": "@activity('Filter Out INTRADAYGLA Files').output.Value[0].name", "type": "Expression"}, "Date": {"value": "@variables('Date')", "type": "Expression"}}}}]}}, {"name": "Only Run in PROD ADF", "type": "IfCondition", "dependsOn": [], "userProperties": [], "typeProperties": {"expression": {"value": "@equals(pipeline().DataFactory,'AZ-PRD-UW-DF-SCD' )", "type": "Expression"}, "ifFalseActivities": [{"name": "ERROR ONLY RUN IN PROD", "type": "Fail", "dependsOn": [], "userProperties": [], "typeProperties": {"message": {"value": "@concat('ONLY RUN IN PROD')", "type": "Expression"}, "errorCode": "500"}}]}}], "variables": {"Date": {"type": "String"}, "InputFileName": {"type": "String"}}, "folder": {"name": "ZzzInternal\\Billing and QFM"}, "annotations": [], "lastPublishTime": "2024-01-25T21:38:20Z"}, "type": "Microsoft.DataFactory/factories/pipelines"}