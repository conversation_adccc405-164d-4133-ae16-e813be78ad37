{"name": "IN222 Manulife FOF Transactions", "properties": {"description": "To append SCD order Id to the transactions", "folder": {"name": "Inbound"}, "type": "MappingDataFlow", "typeProperties": {"sources": [{"dataset": {"referenceName": "ADL_Manulife_FOF_TRN_CSV", "type": "DatasetReference"}, "name": "FOFTRN"}, {"dataset": {"referenceName": "ADL_Manulife_FOF_TRN_CSV", "type": "DatasetReference"}, "name": "SCDOrders"}, {"dataset": {"referenceName": "ADL_Manulife_FOF_TRN_CSV", "type": "DatasetReference"}, "name": "POSITIONSMODELPORTFOLIO"}, {"dataset": {"referenceName": "ADL_Manulife_FOF_TRN_CSV", "type": "DatasetReference"}, "name": "DMPORTFOLIOCUSTODY"}, {"dataset": {"referenceName": "ADL_Manulife_FOF_TRN_CSV", "type": "DatasetReference"}, "name": "DMPORTFOLIO"}], "sinks": [{"dataset": {"referenceName": "ADL_Manulife_FOF_TRN_CSV_DIR", "type": "DatasetReference"}, "name": "SinkToCSV"}, {"dataset": {"referenceName": "ADL_Manulife_FOF_TRN_CSV_DIR", "type": "DatasetReference"}, "name": "SinkToCSVNonMatching"}], "transformations": [{"name": "SCDTrnANdSecId"}, {"name": "TradeDate"}, {"name": "JoinToGetScdOrderNo"}, {"name": "DCSColumns"}, {"name": "Join1"}, {"name": "SelectRelevantFields"}, {"name": "SplitSCDOrderNo"}, {"name": "filterNullRecords"}, {"name": "filter1"}, {"name": "join2"}, {"name": "selectDMPORTFOLIO"}, {"name": "lookupPortfolios"}, {"name": "WindowAddingRowNum"}, {"name": "filterMaulifesec"}], "script": "parameters{\n\tDate as string\n}\nsource(output(\n\t\tAccount as string,\n\t\tALTACCOUNT as string,\n\t\tAccountName as string,\n\t\tFund as string,\n\t\tClass as string,\n\t\tFundName as string,\n\t\tTransactionType as string,\n\t\tAmount as double,\n\t\tUnits as double,\n\t\tPrice as double,\n\t\tTradeDate as string,\n\t\tSystemSettleDate as string,\n\t\tReversed as string\n\t),\n\tallowSchemaDrift: true,\n\tvalidateSchema: false,\n\tignoreNoFilesFound: false,\n\twildcardPaths:[(concat('Custodian Data/Manulife FOF Transaction Settlement/TEMP/Fund on Fund Transaction*.csv'))]) ~> FOFTRN\nsource(output(\n\t\tCustodianName as string,\n\t\tPortfolioCode as string,\n\t\tSCDOrderNo as string,\n\t\tSecId as string,\n\t\tTransactionType as string,\n\t\tOrderAmount as string,\n\t\tOrderActionType as string,\n\t\tOrderDateTime as string\n\t),\n\tallowSchemaDrift: true,\n\tvalidateSchema: false,\n\tignoreNoFilesFound: false,\n\twildcardPaths:['Custodian Data/Manulife FOF Transaction Settlement/TEMP/ScdOrders.csv']) ~> SCDOrders\nsource(output(\n\t\tPORTFOLIO as string,\n\t\tSECURITY_ID as string,\n\t\tMODEL_PORTFOLIO as string\n\t),\n\tallowSchemaDrift: true,\n\tvalidateSchema: false,\n\tignoreNoFilesFound: false,\n\twildcardPaths:[(concat('Static/IMW_POSITIONS_MODEL_PORTFOLIO.csv'))]) ~> POSITIONSMODELPORTFOLIO\nsource(output(\n\t\tPORTFOLIO as string,\n\t\tCUSTODY as string,\n\t\tCURRENCY as string,\n\t\tCUSTODY_IK as short\n\t),\n\tallowSchemaDrift: true,\n\tvalidateSchema: false,\n\tignoreNoFilesFound: false,\n\twildcardPaths:[(concat('Static/DM_PORTFOLIO_CUSTODY.csv'))]) ~> DMPORTFOLIOCUSTODY\nsource(output(\n\t\tDWH_EXTRACT_PROCESS_IK as string,\n\t\tDWH_LOAD_TS as string,\n\t\tLAST_CHANGE_TS as string,\n\t\tPORTFOLIO_KEY as string,\n\t\tPORTFOLIO_IK as string,\n\t\tPORTFOLIO as string,\n\t\tPORTFOLIO_NAME as string,\n\t\tPRIMARY_OWNER_CONTACT_CODE as string,\n\t\tPRIMARY_OWNER_CLIENT_KEY as string,\n\t\tPORTFOLIO_GROUP as string,\n\t\tGROUP_NAME as string,\n\t\tGROUP_TYPE as string,\n\t\tGROUP_TYPE_NAME as string,\n\t\tBUSINESS_SEGMENT as string,\n\t\tPORTFOLIO_STATUS as string,\n\t\tFUND_PORTFOLIO_TYPE as string,\n\t\tCOLLATERAL_ACCOUNT as string,\n\t\tACCREDITED_INVESTOR as string,\n\t\tACCREDITED_INVESTOR_NAME as string,\n\t\tANTI_MONEY_LAUNDERING_RISK as string,\n\t\tCROSS_TRADE_APPROVAL as string,\n\t\tMAWER_PORTFOLIO_TYPE as string,\n\t\tCURRENCY as string,\n\t\tCURRENCY_NAME as string,\n\t\tHOLDINGS_TYPE as string,\n\t\tINSIDER_STATUS as string,\n\t\tINVESTMENT_AUTHORITY_13F as string,\n\t\tINVESTMENT_DISCRETION_13F as string,\n\t\tINVESTMENT_OBJECTIVE as string,\n\t\tMODEL_PORTFOLIO as string,\n\t\tMODEL_PORTFOLIO_TYPE as string,\n\t\tLEVERAGED_ACCOUNT as string,\n\t\tMANAGEMENT_TYPE as string,\n\t\tRISK_TOLERANCE as string,\n\t\tTAXABLE_STATUS as string,\n\t\tTAX_JURISDICTION as string,\n\t\tTIME_HORIZON as string,\n\t\tOPEN_DATE as string,\n\t\tCLOSING_DATE as string,\n\t\tINCEPTION_DATE as string,\n\t\tTERMINATION_DATE as string,\n\t\tPORTFOLIO_TYPE as string,\n\t\tPORTFOLIO_SUB_TYPE as string,\n\t\tMANAGER_1 as short,\n\t\tTEXT_FOR_MANAGER_1 as string,\n\t\tMANAGER_2 as short,\n\t\tTEXT_FOR_MANAGER_2 as string,\n\t\tPRE_TRADE_COMPLIANCE as string,\n\t\tPROXY_VOTING as string,\n\t\tMULTI_STRATEGY_REBALANCING as string,\n\t\tOWNERSHIP_TYPE as string,\n\t\tOWNERSHIP_TYPE_NAME as string,\n\t\tPERMITTED_CLIENT as string,\n\t\tPERMITTED_CLIENT_NAME as string,\n\t\tPOLITICALLY_EXPOSED_PERSON as boolean,\n\t\tMARKETING_SEGMENT as string,\n\t\tTAX_REFERENCE_NO_IK as boolean,\n\t\tCUSTOMER_NUMBER as string,\n\t\tCUSTOMER_REFERENCE as string,\n\t\tPORTFOLIO_BELONGS_TO_BANK as boolean,\n\t\tREASON_FOR_CLOSURE as string,\n\t\tXFER_FROM_PORTFOLIO as string,\n\t\tXFER_TO_PORTFOLIO as string,\n\t\tMAWER_PORTFOLIO_SUBTYPE as string,\n\t\tPORTFOLIO_CLIENT_TYPE as string,\n\t\tPORTFOLIO_END_OF_YEAR as double,\n\t\tCOUNTRY as string,\n\t\tCOUNTRY_NAME as string,\n\t\tOWNER_FIRST_NAME as string,\n\t\tOWNER_LAST_NAME as string,\n\t\tOWNER_CONTACT_SSID as integer,\n\t\tPRIMARY_OWNER_ID as string,\n\t\tBILLING_CITY as string,\n\t\tBILLING_COUNTRY as string,\n\t\tBILLING_POSTAL_CODE as string,\n\t\tBILLING_STATE as string,\n\t\tBILLING_STREET as string,\n\t\tSHIPPING_CITY as string,\n\t\tSHIPPING_COUNTRY as string,\n\t\tSHIPPING_POSTAL_CODE as string,\n\t\tSHIPPING_STATE as string,\n\t\tSHIPPING_STREET as string,\n\t\tJOINT_OWNER_FIRST_NAME as string,\n\t\tJOINT_OWNER_LAST_NAME as string,\n\t\tJOINT_OWNER_CONTACT_SSID as integer,\n\t\tJOINT_OWNER_ID as string,\n\t\tFUND_ACCOUNT_NUMBER as string,\n\t\tHOUSEHOLD_ID as string,\n\t\tOWNER_DIVISION as string,\n\t\tIS_CUSTODIAL_ACCOUNT as boolean,\n\t\tCUSTODIAN_NAME as string,\n\t\tCUSTODIAN_NAME_OLD as string,\n\t\tCAD_CUSTODIAN_ACCT as string,\n\t\tUSD_CUSTODIAN_ACCT as string,\n\t\tPM_USER_ID as string,\n\t\tPM_TITLE as string,\n\t\tPM_FIRST_NAME as string,\n\t\tPM_LAST_NAME as string,\n\t\tPM_PHONE as string,\n\t\tPM_EMAIL as string,\n\t\tPM_FAX as string,\n\t\tPM_SIGNATURE as string,\n\t\tPMA_USER_ID as string,\n\t\tPMA_TITLE as string,\n\t\tPMA_FIRST_NAME as string,\n\t\tPMA_LAST_NAME as string,\n\t\tPMA_PHONE as string,\n\t\tPMA_EMAIL as string,\n\t\tPMA_FAX as string,\n\t\tPMA_SIGNATURE as string,\n\t\tPORTFOLIO_REPORTING_CURRENCY as string,\n\t\tBILLABLE_FLAG as boolean,\n\t\tORIGINATING_SYSTEM_NO as boolean,\n\t\tCURRENT_VERSION as boolean,\n\t\tPORTFOLIO_VALID_FROM_DATE as string,\n\t\tPORTFOLIO_VALID_TO_DATE as string,\n\t\tFUND_INVESTMENT_VEHICLE_CODE as string,\n\t\tFUND_INVESTMENT_VEHICLE_NAME as string\n\t),\n\tallowSchemaDrift: true,\n\tvalidateSchema: false,\n\tignoreNoFilesFound: false,\n\twildcardPaths:[(concat('Static/DM_PORTFOLIO.csv'))]) ~> DMPORTFOLIO\nlookupPortfolios derive(SCDTRNType = iif(lower( TransactionType)==\"redemption\", \"SELL\", \"BUY\"),\n\t\tTrnSecId = concat(\"EPL\", Fund,Class),\n\t\tAbsAmount = toString(abs(toDecimal(Amount,20,2))),\n\t\tTradeDate = toString(toDate(TradeDate,\"yyyy-MM-dd\"), 'yyyyMMdd'),\n\t\tSystemSettleDate = toString(toDate(SystemSettleDate,\"yyyy-MM-dd\"), 'yyyyMMdd'),\n\t\tALTACCOUNT = iif(isNull(ALTACCOUNT), PTF_ALT_ACCOUNT, ALTACCOUNT),\n\t\tRNM = 1) ~> SCDTrnANdSecId\nSCDOrders derive(SCDOrderTradeDate = toString(toDate(OrderDateTime,\"yyyy-MM-dd\"), 'yyyyMMdd')) ~> TradeDate\nSCDTrnANdSecId, TradeDate join(ALTACCOUNT == PortfolioCode\n\t&& TrnSecId == SecId\n\t&& TradeDate == SCDOrderTradeDate\n\t&& SCDTRNType == SCDOrders@TransactionType\n\t&& AbsAmount == OrderAmount,\n\tjoinType:'left',\n\tmatchType:'exact',\n\tignoreSpaces: false,\n\tbroadcast: 'auto')~> JoinToGetScdOrderNo\nWindowAddingRowNum derive({Trans Code} = case( \r\n    FOFTRN@TransactionType ==\"Redemption\", 'AllocSellExt',\r\n    FOFTRN@TransactionType ==\"Purchase\", 'AllocBuyExt'\r\n),\n\t\tCounterparty = 'Internal Broker',\n\t\tBroker = 'Internal Broker',\n\t\t{*** Menu item: Delete ***} = case(!isNull(Reversed), 1, 0),\n\t\t{Transaction origin} = 'MANULIFE',\n\t\tBank = 'RBC',\n\t\tExchange = 'XOTC',\n\t\t{SWIFT Bypass} = 'YES',\n\t\t{Trade Confirmation Bypass} = 'YES',\n\t\t{Pre-Trade Compliance Bypass} = 'YES',\n\t\t{External Transaction Number} = concat('IN222ManuDrip', TradeDate, toString(RowNum))) ~> DCSColumns\nJoinToGetScdOrderNo, POSITIONSMODELPORTFOLIO join(trim(ALTACCOUNT) == trim(PORTFOLIO)\n\t&& trim(TrnSecId) == trim(SECURITY_ID),\n\tjoinType:'left',\n\tmatchType:'exact',\n\tignoreSpaces: false,\n\tbroadcast: 'auto')~> Join1\nfilterMaulifesec select(mapColumn(\n\t\tAccount,\n\t\tALTACCOUNT,\n\t\tAccountName,\n\t\tFund,\n\t\tClass,\n\t\tFundName,\n\t\tTransactionType = FOFTRN@TransactionType,\n\t\tAmount,\n\t\tUnits,\n\t\tPrice,\n\t\t{Trade date} = TradeDate,\n\t\tSystemSettleDate,\n\t\tReversed,\n\t\tSecId = TrnSecId,\n\t\tAbsAmount,\n\t\tSCDOrderNo,\n\t\tModelPF = MODEL_PORTFOLIO,\n\t\tCustody = CUSTODY,\n\t\t{External Transaction Number}\n\t),\n\tskipDuplicateMapInputs: true,\n\tskipDuplicateMapOutputs: true) ~> SelectRelevantFields\nSelectRelevantFields split(isNull(SCDOrderNo),\n\tdisjoint: false) ~> SplitSCDOrderNo@(NonMatchinRows, MatchingRows)\nDCSColumns filter(FOFTRN@TransactionType!='') ~> filterNullRecords\nDMPORTFOLIOCUSTODY filter(CURRENCY=='CAD') ~> filter1\nJoin1, filter1 join(trim(ALTACCOUNT) == trim(DMPORTFOLIOCUSTODY@PORTFOLIO),\n\tjoinType:'left',\n\tmatchType:'exact',\n\tignoreSpaces: false,\n\tbroadcast: 'auto')~> join2\nDMPORTFOLIO select(mapColumn(\n\t\tPTF_ALT_ACCOUNT = PORTFOLIO,\n\t\tPTF_ACCOUNT = FUND_ACCOUNT_NUMBER\n\t),\n\tskipDuplicateMapInputs: true,\n\tskipDuplicateMapOutputs: true) ~> selectDMPORTFOLIO\nFOFTRN, selectDMPORTFOLIO lookup(trim(Account) == trim(PTF_ACCOUNT),\n\tmultiple: false,\n\tpickup: 'any',\n\tbroadcast: 'auto')~> lookupPortfolios\njoin2 window(asc(RNM, true),\n\tRowNum = rowNumber()) ~> WindowAddingRowNum\nfilterNullRecords filter(AccountName != 'MANULIFE SECURITIES INTERNATIONAL') ~> filterMaulifesec\nSplitSCDOrderNo@MatchingRows sink(allowSchemaDrift: true,\n\tvalidateSchema: false,\n\tpartitionFileNames:[(concat('MANULIFE_FOF_TRANS_SCD_',$Date,'.csv'))],\n\tumask: 0022,\n\tpreCommands: [],\n\tpostCommands: [],\n\tskipDuplicateMapInputs: true,\n\tskipDuplicateMapOutputs: true,\n\tpartitionBy('hash', 1)) ~> SinkToCSV\nSplitSCDOrderNo@NonMatchinRows sink(allowSchemaDrift: true,\n\tvalidateSchema: false,\n\tpartitionFileNames:[(concat('MANULIFE_FOF_TRANS_',$Date,'.csv'))],\n\tumask: 0022,\n\tpreCommands: [],\n\tpostCommands: [],\n\tskipDuplicateMapInputs: true,\n\tskipDuplicateMapOutputs: true,\n\tpartitionBy('hash', 1)) ~> SinkToCSVNonMatching"}}}