{"name": "IN-141 USBank Recon", "properties": {"activities": [{"name": "Copy Transactions File from SFTP to ADL", "type": "Copy", "dependsOn": [], "policy": {"timeout": "7.00:00:00", "retry": 0, "retryIntervalInSeconds": 30, "secureOutput": false, "secureInput": false}, "userProperties": [], "typeProperties": {"source": {"type": "BinarySource", "storeSettings": {"type": "SftpReadSettings", "recursive": false, "deleteFilesAfterCompletion": false, "disableChunking": false}, "formatSettings": {"type": "BinaryReadSettings"}}, "sink": {"type": "BinarySink", "storeSettings": {"type": "AzureBlobFSWriteSettings"}}, "enableStaging": false}, "inputs": [{"referenceName": "SFTP_USBank_Binary_File", "type": "DatasetReference", "parameters": {"Path": "Inbox", "FileName": {"value": "@concat('toa0.mawerinvestmentmanagement.xf00.transactions.',pipeline().parameters.pDate,'.csv')", "type": "Expression"}}}], "outputs": [{"referenceName": "ADL_Binary_File", "type": "DatasetReference", "parameters": {"FileName": {"value": "@concat(pipeline().parameters.pDate,'USBTrans.csv')", "type": "Expression"}, "Path": {"value": "@concat(pipeline().parameters.pADLPath,'/IN')", "type": "Expression"}, "Container": {"value": "@pipeline().parameters.pADLContainer", "type": "Expression"}}}]}, {"name": "Archive IN", "type": "Copy", "dependsOn": [{"activity": "If send the files to SCD", "dependencyConditions": ["Succeeded"]}], "policy": {"timeout": "7.00:00:00", "retry": 0, "retryIntervalInSeconds": 30, "secureOutput": false, "secureInput": false}, "userProperties": [], "typeProperties": {"source": {"type": "BinarySource", "storeSettings": {"type": "AzureBlobFSReadSettings", "recursive": true, "deleteFilesAfterCompletion": true}, "formatSettings": {"type": "BinaryReadSettings"}}, "sink": {"type": "BinarySink", "storeSettings": {"type": "AzureBlobFSWriteSettings"}}, "enableStaging": false}, "inputs": [{"referenceName": "ADL_Binary_Dir", "type": "DatasetReference", "parameters": {"Path": {"value": "@concat(pipeline().parameters.pADLPath,'/IN')", "type": "Expression"}}}], "outputs": [{"referenceName": "ADL_Binary_Dir", "type": "DatasetReference", "parameters": {"Path": {"value": "@concat(pipeline().parameters.pADLPath,'/ARCHIVE/',pipeline().parameters.pDate,'/IN')", "type": "Expression"}}}]}, {"name": "Archive OUT", "type": "Copy", "dependsOn": [{"activity": "If send the files to SCD", "dependencyConditions": ["Succeeded"]}], "policy": {"timeout": "7.00:00:00", "retry": 0, "retryIntervalInSeconds": 30, "secureOutput": false, "secureInput": false}, "userProperties": [], "typeProperties": {"source": {"type": "BinarySource", "storeSettings": {"type": "AzureBlobFSReadSettings", "recursive": true, "deleteFilesAfterCompletion": true}, "formatSettings": {"type": "BinaryReadSettings"}}, "sink": {"type": "BinarySink", "storeSettings": {"type": "AzureBlobFSWriteSettings"}}, "enableStaging": false}, "inputs": [{"referenceName": "ADL_Binary_Dir", "type": "DatasetReference", "parameters": {"Path": {"value": "@concat(pipeline().parameters.pADLPath,'/OUT')", "type": "Expression"}}}], "outputs": [{"referenceName": "ADL_Binary_Dir", "type": "DatasetReference", "parameters": {"Path": {"value": "@concat(pipeline().parameters.pADLPath,'/ARCHIVE/',pipeline().parameters.pDate,'/OUT')", "type": "Expression"}}}]}, {"name": "If send the files to SCD", "type": "IfCondition", "dependsOn": [{"activity": "Create Transactions File", "dependencyConditions": ["Succeeded"]}, {"activity": "Create Positions File", "dependencyConditions": ["Succeeded"]}], "userProperties": [], "typeProperties": {"expression": {"value": "@pipeline().parameters.pSendToSCD", "type": "Expression"}, "ifTrueActivities": [{"name": "Copy files to SCD SFTP", "type": "Copy", "dependsOn": [], "policy": {"timeout": "7.00:00:00", "retry": 0, "retryIntervalInSeconds": 30, "secureOutput": false, "secureInput": false}, "userProperties": [], "typeProperties": {"source": {"type": "BinarySource", "storeSettings": {"type": "AzureBlobFSReadSettings", "recursive": true, "deleteFilesAfterCompletion": false}, "formatSettings": {"type": "BinaryReadSettings"}}, "sink": {"type": "BinarySink", "storeSettings": {"type": "SftpWriteSettings", "operationTimeout": "01:00:00", "useTempFileRename": true}}, "enableStaging": false}, "inputs": [{"referenceName": "ADL_Binary_Dir", "type": "DatasetReference", "parameters": {"Path": {"value": "@concat(pipeline().parameters.pADLPath,'/OUT')", "type": "Expression"}}}], "outputs": [{"referenceName": "SCD_SFTP_Binary_Dir", "type": "DatasetReference", "parameters": {"Path": {"value": "@pipeline().parameters.pSCDsftpPath", "type": "Expression"}}}]}]}}, {"name": "Copy Positions File from SFTP to ADL", "type": "Copy", "dependsOn": [], "policy": {"timeout": "7.00:00:00", "retry": 0, "retryIntervalInSeconds": 30, "secureOutput": false, "secureInput": false}, "userProperties": [], "typeProperties": {"source": {"type": "BinarySource", "storeSettings": {"type": "SftpReadSettings", "recursive": true, "deleteFilesAfterCompletion": false, "disableChunking": false}, "formatSettings": {"type": "BinaryReadSettings"}}, "sink": {"type": "BinarySink", "storeSettings": {"type": "AzureBlobFSWriteSettings"}}, "enableStaging": false}, "inputs": [{"referenceName": "SFTP_USBank_Binary_File", "type": "DatasetReference", "parameters": {"Path": "Inbox", "FileName": {"value": "@concat('toa0.mawerinvestmentmanagement.xf00.holdings.',pipeline().parameters.pDate,'.csv')", "type": "Expression"}}}], "outputs": [{"referenceName": "ADL_Binary_File", "type": "DatasetReference", "parameters": {"FileName": {"value": "@concat(pipeline().parameters.pDate,'USBPstns.csv')", "type": "Expression"}, "Path": {"value": "@concat(pipeline().parameters.pADLPath,'/IN')", "type": "Expression"}, "Container": {"value": "@pipeline().parameters.pADLContainer", "type": "Expression"}}}]}, {"name": "Stage Transactions", "type": "Copy", "dependsOn": [{"activity": "Copy Transactions File from SFTP to ADL", "dependencyConditions": ["Succeeded"]}], "policy": {"timeout": "0.12:00:00", "retry": 0, "retryIntervalInSeconds": 30, "secureOutput": false, "secureInput": false}, "userProperties": [], "typeProperties": {"source": {"type": "DelimitedTextSource", "additionalColumns": [{"name": "DataDate", "value": {"value": "@pipeline().parameters.pDate", "type": "Expression"}}], "storeSettings": {"type": "AzureBlobFSReadSettings", "recursive": false, "enablePartitionDiscovery": false}, "formatSettings": {"type": "DelimitedTextReadSettings", "skipLineCount": 1}}, "sink": {"type": "SqlMISink", "preCopyScript": {"value": "@{concat('DELETE FROM [Integration].[Recon].[USBankTransactionsStaging] \nWHERE [DataDate] = ''',pipeline().parameters.pDate,'''')}", "type": "Expression"}, "writeBehavior": "insert", "sqlWriterUseTableLock": false}, "enableStaging": false, "translator": {"type": "TabularTranslator", "mappings": [{"source": {"name": "Account Number", "type": "String", "physicalType": "String"}, "sink": {"name": "Account Number", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}, {"source": {"name": "Account Name", "type": "String", "physicalType": "String"}, "sink": {"name": "Account Name", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}, {"source": {"name": "CUSIP", "type": "String", "physicalType": "String"}, "sink": {"name": "CUSIP", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}, {"source": {"name": "Ticker", "type": "String", "physicalType": "String"}, "sink": {"name": "Ticker", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}, {"source": {"name": "SEDOL", "type": "String", "physicalType": "String"}, "sink": {"name": "SEDOL", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}, {"source": {"name": "ISIN", "type": "String", "physicalType": "String"}, "sink": {"name": "ISIN", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}, {"source": {"name": "Option Symbol", "type": "String", "physicalType": "String"}, "sink": {"name": "Option Symbol", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}, {"source": {"name": "Asset Short Name", "type": "String", "physicalType": "String"}, "sink": {"name": "Asset Short Name", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}, {"source": {"name": "Currency of Issue", "type": "String", "physicalType": "String"}, "sink": {"name": "Currency of Issue", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}, {"source": {"name": "Country of Issue", "type": "String", "physicalType": "String"}, "sink": {"name": "Country of Issue", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}, {"source": {"name": "Security Type", "type": "String", "physicalType": "String"}, "sink": {"name": "Security Type", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}, {"source": {"name": "Security Type Description", "type": "String", "physicalType": "String"}, "sink": {"name": "Security Type Description", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}, {"source": {"name": "Minor Asset Class", "type": "String", "physicalType": "String"}, "sink": {"name": "Minor Asset Class", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}, {"source": {"name": "Minor Asset Class Description", "type": "String", "physicalType": "String"}, "sink": {"name": "Minor Asset Class Description", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}, {"source": {"name": "Issue Date", "type": "String", "physicalType": "String"}, "sink": {"name": "Issue Date", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}, {"source": {"name": "Maturity Date", "type": "String", "physicalType": "String"}, "sink": {"name": "Maturity Date", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}, {"source": {"name": "First Coupon Date", "type": "String", "physicalType": "String"}, "sink": {"name": "First Coupon Date", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}, {"source": {"name": "First Of Next Two Income Dates", "type": "String", "physicalType": "String"}, "sink": {"name": "First Of Next Two Income Dates", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}, {"source": {"name": "Second Of Next Two Income Dates", "type": "String", "physicalType": "String"}, "sink": {"name": "Second Of Next Two Income Dates", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}, {"source": {"name": "Interest Rate", "type": "String", "physicalType": "String"}, "sink": {"name": "Interest Rate", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}, {"source": {"name": "Interest Dividend Frequency", "type": "String", "physicalType": "String"}, "sink": {"name": "Interest Dividend Frequency", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}, {"source": {"name": "Local Interest Rate", "type": "String", "physicalType": "String"}, "sink": {"name": "Local Interest Rate", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}, {"source": {"name": "S&P Rating", "type": "String", "physicalType": "String"}, "sink": {"name": "S&P Rating", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}, {"source": {"name": "<PERSON><PERSON>ing", "type": "String", "physicalType": "String"}, "sink": {"name": "<PERSON><PERSON>ing", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}, {"source": {"name": "Portfolio", "type": "String", "physicalType": "String"}, "sink": {"name": "Portfolio", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}, {"source": {"name": "Portfolio Name", "type": "String", "physicalType": "String"}, "sink": {"name": "Portfolio Name", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}, {"source": {"name": "Transaction Date", "type": "String", "physicalType": "String"}, "sink": {"name": "Transaction Date", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}, {"source": {"name": "Trade Date", "type": "String", "physicalType": "String"}, "sink": {"name": "Trade Date", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}, {"source": {"name": "Settlement Date", "type": "String", "physicalType": "String"}, "sink": {"name": "Settlement Date", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}, {"source": {"name": "Pending Item Number", "type": "String", "physicalType": "String"}, "sink": {"name": "Pending Item Number", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}, {"source": {"name": "Poster Location", "type": "String", "physicalType": "String"}, "sink": {"name": "Poster Location", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}, {"source": {"name": "Batch Number", "type": "String", "physicalType": "String"}, "sink": {"name": "Batch Number", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}, {"source": {"name": "Transaction Number", "type": "String", "physicalType": "String"}, "sink": {"name": "Transaction Number", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}, {"source": {"name": "Transaction Type", "type": "String", "physicalType": "String"}, "sink": {"name": "Transaction Type", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}, {"source": {"name": "Transaction Type Description", "type": "String", "physicalType": "String"}, "sink": {"name": "Transaction Type Description", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}, {"source": {"name": "Minor Transaction Type", "type": "String", "physicalType": "String"}, "sink": {"name": "Minor Transaction Type", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}, {"source": {"name": "Minor Transaction Type Description", "type": "String", "physicalType": "String"}, "sink": {"name": "Minor Transaction Type Description", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}, {"source": {"name": "Action Code", "type": "String", "physicalType": "String"}, "sink": {"name": "Action Code", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}, {"source": {"name": "Disbursement Code", "type": "String", "physicalType": "String"}, "sink": {"name": "Disbursement Code", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}, {"source": {"name": "Disbursement Code Description", "type": "String", "physicalType": "String"}, "sink": {"name": "Disbursement Code Description", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}, {"source": {"name": "Receipt Code", "type": "String", "physicalType": "String"}, "sink": {"name": "Receipt Code", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}, {"source": {"name": "Receipt Code Description", "type": "String", "physicalType": "String"}, "sink": {"name": "Receipt Code Description", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}, {"source": {"name": "Broker Code", "type": "String", "physicalType": "String"}, "sink": {"name": "Broker Code", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}, {"source": {"name": "Broker FINS", "type": "String", "physicalType": "String"}, "sink": {"name": "Broker FINS", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}, {"source": {"name": "Broker Name", "type": "String", "physicalType": "String"}, "sink": {"name": "Broker Name", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}, {"source": {"name": "FX Trade", "type": "String", "physicalType": "String"}, "sink": {"name": "FX Trade", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}, {"source": {"name": "FX Currency", "type": "String", "physicalType": "String"}, "sink": {"name": "FX Currency", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}, {"source": {"name": "Settlement Currency", "type": "String", "physicalType": "String"}, "sink": {"name": "Settlement Currency", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}, {"source": {"name": "Shares", "type": "String", "physicalType": "String"}, "sink": {"name": "Shares", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}, {"source": {"name": "Trade Price", "type": "String", "physicalType": "String"}, "sink": {"name": "Trade Price", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}, {"source": {"name": "Local Trade Price", "type": "String", "physicalType": "String"}, "sink": {"name": "Local Trade Price", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}, {"source": {"name": "Exchange Rate", "type": "String", "physicalType": "String"}, "sink": {"name": "Exchange Rate", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}, {"source": {"name": "Settlement Amount", "type": "String", "physicalType": "String"}, "sink": {"name": "Settlement Amount", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}, {"source": {"name": "Cost Amount", "type": "String", "physicalType": "String"}, "sink": {"name": "Cost Amount", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}, {"source": {"name": "Local Cost Amount", "type": "String", "physicalType": "String"}, "sink": {"name": "Local Cost Amount", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}, {"source": {"name": "Book Value", "type": "String", "physicalType": "String"}, "sink": {"name": "Book Value", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}, {"source": {"name": "Local Book Value", "type": "String", "physicalType": "String"}, "sink": {"name": "Local Book Value", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}, {"source": {"name": "Federal Long Term Gain Loss", "type": "String", "physicalType": "String"}, "sink": {"name": "Federal Long Term Gain Loss", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}, {"source": {"name": "Local Federal Long Term Gain Loss", "type": "String", "physicalType": "String"}, "sink": {"name": "Local Federal Long Term Gain Loss", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}, {"source": {"name": "Federal Short Term Gain Loss", "type": "String", "physicalType": "String"}, "sink": {"name": "Federal Short Term Gain Loss", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}, {"source": {"name": "Local Federal Short Term Gain Loss", "type": "String", "physicalType": "String"}, "sink": {"name": "Local Federal Short Term Gain Loss", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}, {"source": {"name": "Book Value Gain Loss", "type": "String", "physicalType": "String"}, "sink": {"name": "Book Value Gain Loss", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}, {"source": {"name": "Local Book Value Gain Loss", "type": "String", "physicalType": "String"}, "sink": {"name": "Local Book Value Gain Loss", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}, {"source": {"name": "Original Face Value", "type": "String", "physicalType": "String"}, "sink": {"name": "Original Face Value", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}, {"source": {"name": "Broker Commission", "type": "String", "physicalType": "String"}, "sink": {"name": "Broker Commission", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}, {"source": {"name": "Local Commission", "type": "String", "physicalType": "String"}, "sink": {"name": "Local Commission", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}, {"source": {"name": "Postage and Insurance Fees", "type": "String", "physicalType": "String"}, "sink": {"name": "Postage and Insurance Fees", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}, {"source": {"name": "SEC Fees", "type": "String", "physicalType": "String"}, "sink": {"name": "SEC Fees", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}, {"source": {"name": "Miscellaneous Fees", "type": "String", "physicalType": "String"}, "sink": {"name": "Miscellaneous Fees", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}, {"source": {"name": "Local Miscellaneous Fees", "type": "String", "physicalType": "String"}, "sink": {"name": "Local Miscellaneous Fees", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}, {"source": {"name": "Transaction Description", "type": "String", "physicalType": "String"}, "sink": {"name": "Transaction Description", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}, {"source": {"name": "Explanation Line 2", "type": "String", "physicalType": "String"}, "sink": {"name": "Explanation Line 2", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}, {"source": {"name": "Explanation Line 3", "type": "String", "physicalType": "String"}, "sink": {"name": "Explanation Line 3", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}, {"source": {"name": "Explanation Line 4", "type": "String", "physicalType": "String"}, "sink": {"name": "Explanation Line 4", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}, {"source": {"name": "Explanation Line 5", "type": "String", "physicalType": "String"}, "sink": {"name": "Explanation Line 5", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}, {"source": {"name": "Reversed Transaction Flag", "type": "String", "physicalType": "String"}, "sink": {"name": "Reversed Transaction Flag", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}, {"source": {"name": "Reversing Transaction Flag", "type": "String", "physicalType": "String"}, "sink": {"name": "Reversing Transaction Flag", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}, {"source": {"name": "Suppress Display Flag", "type": "String", "physicalType": "String"}, "sink": {"name": "Suppress Display Flag", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}, {"source": {"name": "Pending Trade Flag", "type": "String", "physicalType": "String"}, "sink": {"name": "Pending Trade Flag", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}, {"source": {"name": "Account Identifier", "type": "String", "physicalType": "String"}, "sink": {"name": "Account Identifier", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}, {"source": {"name": "DataDate", "type": "String"}, "sink": {"name": "DataDate", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}], "typeConversion": true, "typeConversionSettings": {"allowDataTruncation": true, "treatBooleanAsNumber": false}}}, "inputs": [{"referenceName": "ADL_Delimited_File", "type": "DatasetReference", "parameters": {"FilePath": {"value": "@concat(pipeline().parameters.pADLPath,'/IN')", "type": "Expression"}, "FileName": {"value": "@concat(pipeline().parameters.pDate,'USBTrans.csv')", "type": "Expression"}, "ColDelimiter": ",", "FirstRowHeader": true}}], "outputs": [{"referenceName": "MOW_PARAM", "type": "DatasetReference", "parameters": {"DBName": "Integration", "SchemaName": "Recon", "TableName": "USBankTransactionsStaging"}}]}, {"name": "Stage Positions", "type": "Copy", "dependsOn": [{"activity": "Copy Positions File from SFTP to ADL", "dependencyConditions": ["Succeeded"]}], "policy": {"timeout": "0.12:00:00", "retry": 0, "retryIntervalInSeconds": 30, "secureOutput": false, "secureInput": false}, "userProperties": [], "typeProperties": {"source": {"type": "DelimitedTextSource", "additionalColumns": [{"name": "DataDate", "value": {"value": "@pipeline().parameters.pDate", "type": "Expression"}}], "storeSettings": {"type": "AzureBlobFSReadSettings", "recursive": false, "enablePartitionDiscovery": false}, "formatSettings": {"type": "DelimitedTextReadSettings", "skipLineCount": 1}}, "sink": {"type": "SqlMISink", "preCopyScript": {"value": "@{concat('DELETE FROM [Integration].[Recon].[USBankPositionsStaging] \nWHERE [DataDate] = ''',pipeline().parameters.pDate,'''')}", "type": "Expression"}, "writeBehavior": "insert", "sqlWriterUseTableLock": false}, "enableStaging": false, "translator": {"type": "TabularTranslator", "mappings": [{"source": {"name": "Account Number", "type": "String", "physicalType": "String"}, "sink": {"name": "Account Number", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}, {"source": {"name": "Account Name", "type": "String", "physicalType": "String"}, "sink": {"name": "Account Name", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}, {"source": {"name": "State of Residence Code", "type": "String", "physicalType": "String"}, "sink": {"name": "State of Residence Code", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}, {"source": {"name": "Portfolio", "type": "String", "physicalType": "String"}, "sink": {"name": "Portfolio", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}, {"source": {"name": "Portfolio Name", "type": "String", "physicalType": "String"}, "sink": {"name": "Portfolio Name", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}, {"source": {"name": "CUSIP", "type": "String", "physicalType": "String"}, "sink": {"name": "CUSIP", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}, {"source": {"name": "Ticker", "type": "String", "physicalType": "String"}, "sink": {"name": "Ticker", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}, {"source": {"name": "SEDOL", "type": "String", "physicalType": "String"}, "sink": {"name": "SEDOL", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}, {"source": {"name": "ISIN", "type": "String", "physicalType": "String"}, "sink": {"name": "ISIN", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}, {"source": {"name": "Option Symbol", "type": "String", "physicalType": "String"}, "sink": {"name": "Option Symbol", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}, {"source": {"name": "Asset Name", "type": "String", "physicalType": "String"}, "sink": {"name": "Asset Name", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}, {"source": {"name": "<PERSON><PERSON> Long Name", "type": "String", "physicalType": "String"}, "sink": {"name": "<PERSON><PERSON> Long Name", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}, {"source": {"name": "Currency of Issue", "type": "String", "physicalType": "String"}, "sink": {"name": "Currency of Issue", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}, {"source": {"name": "Country of Issue", "type": "String", "physicalType": "String"}, "sink": {"name": "Country of Issue", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}, {"source": {"name": "Security Type", "type": "String", "physicalType": "String"}, "sink": {"name": "Security Type", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}, {"source": {"name": "Security Type Name", "type": "String", "physicalType": "String"}, "sink": {"name": "Security Type Name", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}, {"source": {"name": "Minor Asset Class", "type": "String", "physicalType": "String"}, "sink": {"name": "Minor Asset Class", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}, {"source": {"name": "Minor Asset Class Name", "type": "String", "physicalType": "String"}, "sink": {"name": "Minor Asset Class Name", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}, {"source": {"name": "Minor Industry Code", "type": "String", "physicalType": "String"}, "sink": {"name": "Minor Industry Code", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}, {"source": {"name": "Minor Industry Code Name", "type": "String", "physicalType": "String"}, "sink": {"name": "Minor Industry Code Name", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}, {"source": {"name": "S&P Rating", "type": "String", "physicalType": "String"}, "sink": {"name": "S&P Rating", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}, {"source": {"name": "<PERSON><PERSON>ing", "type": "String", "physicalType": "String"}, "sink": {"name": "<PERSON><PERSON>ing", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}, {"source": {"name": "Frequency of Payment", "type": "String", "physicalType": "String"}, "sink": {"name": "Frequency of Payment", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}, {"source": {"name": "Interest Rate", "type": "String", "physicalType": "String"}, "sink": {"name": "Interest Rate", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}, {"source": {"name": "Local Interest Rate", "type": "String", "physicalType": "String"}, "sink": {"name": "Local Interest Rate", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}, {"source": {"name": "Rate of Next Income Payment", "type": "String", "physicalType": "String"}, "sink": {"name": "Rate of Next Income Payment", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}, {"source": {"name": "Local Rate of Next Income Payment", "type": "String", "physicalType": "String"}, "sink": {"name": "Local Rate of Next Income Payment", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}, {"source": {"name": "Next Two Income Payments 1", "type": "String", "physicalType": "String"}, "sink": {"name": "Next Two Income Payments 1", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}, {"source": {"name": "Next Two Income Payments 2", "type": "String", "physicalType": "String"}, "sink": {"name": "Next Two Income Payments 2", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}, {"source": {"name": "Maturity Date", "type": "String", "physicalType": "String"}, "sink": {"name": "Maturity Date", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}, {"source": {"name": "Ex Dividend Date", "type": "String", "physicalType": "String"}, "sink": {"name": "Ex Dividend Date", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}, {"source": {"name": "Record Date", "type": "String", "physicalType": "String"}, "sink": {"name": "Record Date", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}, {"source": {"name": "Shares", "type": "String", "physicalType": "String"}, "sink": {"name": "Shares", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}, {"source": {"name": "Pricing Date", "type": "String", "physicalType": "String"}, "sink": {"name": "Pricing Date", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}, {"source": {"name": "Price", "type": "String", "physicalType": "String"}, "sink": {"name": "Price", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}, {"source": {"name": "Local Pricing Date", "type": "String", "physicalType": "String"}, "sink": {"name": "Local Pricing Date", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}, {"source": {"name": "Local Price", "type": "String", "physicalType": "String"}, "sink": {"name": "Local Price", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}, {"source": {"name": "Price Factor", "type": "String", "physicalType": "String"}, "sink": {"name": "Price Factor", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}, {"source": {"name": "Market Value", "type": "String", "physicalType": "String"}, "sink": {"name": "Market Value", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}, {"source": {"name": "Local Market Value", "type": "String", "physicalType": "String"}, "sink": {"name": "Local Market Value", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}, {"source": {"name": "Federal Tax Cost", "type": "String", "physicalType": "String"}, "sink": {"name": "Federal Tax Cost", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}, {"source": {"name": "Local Federal Tax Cost", "type": "String", "physicalType": "String"}, "sink": {"name": "Local Federal Tax Cost", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}, {"source": {"name": "Unrealized Gain Loss Cost", "type": "String", "physicalType": "String"}, "sink": {"name": "Unrealized Gain Loss Cost", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}, {"source": {"name": "Local Unrealized Gain Loss Cost", "type": "String", "physicalType": "String"}, "sink": {"name": "Local Unrealized Gain Loss Cost", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}, {"source": {"name": "Book Value", "type": "String", "physicalType": "String"}, "sink": {"name": "Book Value", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}, {"source": {"name": "Local Book Value", "type": "String", "physicalType": "String"}, "sink": {"name": "Local Book Value", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}, {"source": {"name": "Unrealized Gain Loss Book", "type": "String", "physicalType": "String"}, "sink": {"name": "Unrealized Gain Loss Book", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}, {"source": {"name": "Local Unrealized Gain Loss Book", "type": "String", "physicalType": "String"}, "sink": {"name": "Local Unrealized Gain Loss Book", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}, {"source": {"name": "Original Face Value", "type": "String", "physicalType": "String"}, "sink": {"name": "Original Face Value", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}, {"source": {"name": "Estimated Annual Income", "type": "String", "physicalType": "String"}, "sink": {"name": "Estimated Annual Income", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}, {"source": {"name": "Current Yield", "type": "String", "physicalType": "String"}, "sink": {"name": "Current Yield", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}, {"source": {"name": "Pending Item Number", "type": "String", "physicalType": "String"}, "sink": {"name": "Pending Item Number", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}, {"source": {"name": "Trade Type", "type": "String", "physicalType": "String"}, "sink": {"name": "Trade Type", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}, {"source": {"name": "FX Trade Indicator", "type": "String", "physicalType": "String"}, "sink": {"name": "FX Trade Indicator", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}, {"source": {"name": "Trade Date", "type": "String", "physicalType": "String"}, "sink": {"name": "Trade Date", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}, {"source": {"name": "Settlement Date", "type": "String", "physicalType": "String"}, "sink": {"name": "Settlement Date", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}, {"source": {"name": "Settlement Currency", "type": "String", "physicalType": "String"}, "sink": {"name": "Settlement Currency", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}, {"source": {"name": "Net Proceeds", "type": "String", "physicalType": "String"}, "sink": {"name": "Net Proceeds", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}, {"source": {"name": "Account Identifier", "type": "String", "physicalType": "String"}, "sink": {"name": "Account Identifier", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}, {"source": {"name": "DataDate", "type": "String"}, "sink": {"name": "DataDate", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}], "typeConversion": true, "typeConversionSettings": {"allowDataTruncation": true, "treatBooleanAsNumber": false}}}, "inputs": [{"referenceName": "ADL_Delimited_File", "type": "DatasetReference", "parameters": {"FilePath": {"value": "@concat(pipeline().parameters.pADLPath,'/IN')", "type": "Expression"}, "FileName": {"value": "@concat(pipeline().parameters.pDate,'USBPstns.csv')", "type": "Expression"}, "ColDelimiter": ",", "FirstRowHeader": true}}], "outputs": [{"referenceName": "MOW_PARAM", "type": "DatasetReference", "parameters": {"DBName": "Integration", "SchemaName": "Recon", "TableName": "USBankPositionsStaging"}}]}, {"name": "Create Transactions File", "type": "Copy", "dependsOn": [{"activity": "Stage Transactions", "dependencyConditions": ["Succeeded"]}, {"activity": "Stage Positions", "dependencyConditions": ["Succeeded"]}], "policy": {"timeout": "7.00:00:00", "retry": 0, "retryIntervalInSeconds": 30, "secureOutput": false, "secureInput": false}, "userProperties": [], "typeProperties": {"source": {"type": "SqlMISource", "sqlReaderStoredProcedureName": "[Integration].[Recon].[sp_USBankTransactions]", "storedProcedureParameters": {"DataDate": {"type": "String", "value": {"value": "@pipeline().parameters.pDate", "type": "Expression"}}}, "partitionOption": "None"}, "sink": {"type": "DelimitedTextSink", "storeSettings": {"type": "AzureBlobFSWriteSettings"}, "formatSettings": {"type": "DelimitedTextWriteSettings", "quoteAllText": true, "fileExtension": ".txt"}}, "enableStaging": false, "translator": {"type": "TabularTranslator", "typeConversion": true, "typeConversionSettings": {"allowDataTruncation": true, "treatBooleanAsNumber": false}}}, "inputs": [{"referenceName": "MOW_PARAM", "type": "DatasetReference", "parameters": {"DBName": "Integration", "SchemaName": "Recon", "TableName": "sp_USBankTransactions"}}], "outputs": [{"referenceName": "ADL_Delimited_File", "type": "DatasetReference", "parameters": {"FilePath": {"value": "@concat(pipeline().parameters.pADLPath,'/OUT')", "type": "Expression"}, "FileName": {"value": "@concat(pipeline().parameters.pDate,'_USB_PREC.csv')", "type": "Expression"}, "ColDelimiter": ",", "FirstRowHeader": true}}]}, {"name": "Create Positions File", "type": "Copy", "dependsOn": [{"activity": "Stage Positions", "dependencyConditions": ["Succeeded"]}], "policy": {"timeout": "7.00:00:00", "retry": 0, "retryIntervalInSeconds": 30, "secureOutput": false, "secureInput": false}, "userProperties": [], "typeProperties": {"source": {"type": "SqlMISource", "sqlReaderStoredProcedureName": "[Integration].[Recon].[sp_USBankPositions]", "storedProcedureParameters": {"DataDate": {"type": "String", "value": {"value": "@pipeline().parameters.pDate", "type": "Expression"}}}, "partitionOption": "None"}, "sink": {"type": "DelimitedTextSink", "storeSettings": {"type": "AzureBlobFSWriteSettings"}, "formatSettings": {"type": "DelimitedTextWriteSettings", "quoteAllText": true, "fileExtension": ".txt"}}, "enableStaging": false, "translator": {"type": "TabularTranslator", "typeConversion": true, "typeConversionSettings": {"allowDataTruncation": true, "treatBooleanAsNumber": false}}}, "inputs": [{"referenceName": "MOW_PARAM", "type": "DatasetReference", "parameters": {"DBName": "Integration", "SchemaName": "Recon", "TableName": "sp_USBankPositions"}}], "outputs": [{"referenceName": "ADL_Delimited_File", "type": "DatasetReference", "parameters": {"FilePath": {"value": "@concat(pipeline().parameters.pADLPath,'/OUT')", "type": "Expression"}, "FileName": {"value": "@concat(pipeline().parameters.pDate,'_USB_HREC.csv')", "type": "Expression"}, "ColDelimiter": ",", "FirstRowHeader": true}}]}], "parameters": {"pDate": {"type": "string"}, "pADLPath": {"type": "string", "defaultValue": "Custodian Data/USBank/USBank Recon"}, "pADLContainer": {"type": "string", "defaultValue": "scdintegration"}, "pSendToSCD": {"type": "bool", "defaultValue": false}, "pSCDsftpPath": {"type": "string", "defaultValue": "X"}, "pDataType": {"type": "string", "defaultValue": "X"}}, "folder": {"name": "Custodians and Vendors/USBank/USBank Recon"}, "annotations": [], "lastPublishTime": "2024-08-14T21:31:33Z"}, "type": "Microsoft.DataFactory/factories/pipelines"}