{"name": "SFTP_SCD_QFM", "properties": {"linkedServiceName": {"referenceName": "SCD_SFTP", "type": "LinkedServiceReference"}, "parameters": {"FileName": {"type": "string"}, "FilePath": {"type": "string"}, "Delimiter": {"type": "string"}}, "folder": {"name": "Inbound/Billing and QFM"}, "annotations": [], "type": "DelimitedText", "typeProperties": {"location": {"type": "SftpLocation", "fileName": {"value": "@dataset().FileName", "type": "Expression"}, "folderPath": {"value": "@dataset().FilePath", "type": "Expression"}}, "columnDelimiter": {"value": "@dataset().Delimiter", "type": "Expression"}, "escapeChar": "\\", "firstRowAsHeader": true, "quoteChar": ""}, "schema": []}, "type": "Microsoft.DataFactory/factories/datasets"}