{"name": "SSCNetSFTP", "properties": {"linkedServiceName": {"referenceName": "SSCNetIR", "type": "LinkedServiceReference"}, "parameters": {"FileName": {"type": "string"}}, "folder": {"name": "Outbound/SSCNet"}, "annotations": [], "type": "DelimitedText", "typeProperties": {"location": {"type": "SftpLocation", "fileName": {"value": "@dataset().FileName", "type": "Expression"}}, "columnDelimiter": ",", "escapeChar": "\\", "quoteChar": "\""}, "schema": []}, "type": "Microsoft.DataFactory/factories/datasets"}