{"name": "DECOM- StateStreet GLA - EOD", "properties": {"activities": [{"name": "Set Date Variable", "type": "SetVariable", "dependsOn": [{"activity": "Only Run in PROD ADF", "dependencyConditions": ["Succeeded"]}], "policy": {"secureOutput": false, "secureInput": false}, "userProperties": [], "typeProperties": {"variableName": "Date", "value": {"value": "@formatDateTime(convertFromUtc(utcnow(),'Mountain Standard Time'),'yyyyMMdd')", "type": "Expression"}}}, {"name": "Read from State Street FileShare", "type": "GetMetadata", "dependsOn": [{"activity": "Set Date Variable", "dependencyConditions": ["Succeeded"]}], "policy": {"timeout": "7.00:00:00", "retry": 0, "retryIntervalInSeconds": 30, "secureOutput": false, "secureInput": false}, "userProperties": [], "typeProperties": {"dataset": {"referenceName": "FileShare_StateStreetBilling", "type": "DatasetReference"}, "fieldList": ["childItems"], "storeSettings": {"type": "AzureFileStorageReadSettings", "recursive": true, "enablePartitionDiscovery": false}, "formatSettings": {"type": "DelimitedTextReadSettings"}}}, {"name": "Filter Out EODGLA Files", "type": "Filter", "dependsOn": [{"activity": "Read from State Street FileShare", "dependencyConditions": ["Succeeded"]}], "userProperties": [], "typeProperties": {"items": {"value": "@activity('Read from State Street FileShare').output.childItems", "type": "Expression"}, "condition": {"value": "@AND(contains(item().name, variables('Date')),contains(item().name, 'EODGLA'))", "type": "Expression"}}}, {"name": "Check If INTRADAYGLA Exists", "description": "If INTRADAYGLA file does not exists then use EODGLA file \n", "type": "IfCondition", "dependsOn": [{"activity": "Check if INTRADAYGLA Ran", "dependencyConditions": ["Succeeded"]}], "userProperties": [], "typeProperties": {"expression": {"value": "@equals(activity('Check if INTRADAYGLA Ran').output.firstRow,0)", "type": "Expression"}, "ifTrueActivities": [{"name": "Run SS GLA - Child Pipeline", "type": "ExecutePipeline", "dependsOn": [], "policy": {"secureInput": false}, "userProperties": [], "typeProperties": {"pipeline": {"referenceName": "DECOM- StateStreet GLA - Child", "type": "PipelineReference"}, "waitOnCompletion": true, "parameters": {"InputFileName": {"value": "@activity('Filter Out EODGLA Files').output.Value[0].name", "type": "Expression"}, "Date": {"value": "@variables('Date')", "type": "Expression"}}}}]}}, {"name": "Check if INTRADAYGLA Ran", "type": "Lookup", "dependsOn": [{"activity": "Filter Out EODGLA Files", "dependencyConditions": ["Succeeded"]}], "policy": {"timeout": "7.00:00:00", "retry": 0, "retryIntervalInSeconds": 30, "secureOutput": false, "secureInput": false}, "userProperties": [], "typeProperties": {"source": {"type": "SqlMISource", "sqlReaderQuery": {"value": "@concat('select Count(*) as IsProcessed from  Billing.StateStreet.GLAFileLogs\nwhere FileName like ''%',variables('Date'),'%''and IsProcessed = 1 and FileName like ''%',variables('Date'),'%''')", "type": "Expression"}, "partitionOption": "None"}, "dataset": {"referenceName": "SQLMI_MOW_PARAM", "type": "DatasetReference", "parameters": {"DBName": "Billing", "SchemaName": "StateStreet", "TableName": "GLAFileLogs"}}}}, {"name": "Only Run in PROD ADF", "type": "IfCondition", "dependsOn": [], "userProperties": [], "typeProperties": {"expression": {"value": "@equals(pipeline().DataFactory,'AZ-PRD-UW-DF-SCD' )", "type": "Expression"}, "ifFalseActivities": [{"name": "ERROR ONLY RUN IN PROD", "type": "Fail", "dependsOn": [], "userProperties": [], "typeProperties": {"message": {"value": "@concat('ONLY RUN IN PROD')", "type": "Expression"}, "errorCode": "500"}}]}}], "variables": {"Date": {"type": "String", "defaultValue": "202"}}, "folder": {"name": "ZzzInternal\\Billing and QFM"}, "annotations": [], "lastPublishTime": "2024-01-25T21:38:20Z"}, "type": "Microsoft.DataFactory/factories/pipelines"}