{"name": "IN-166 StateStreet FX - CHILD", "properties": {"activities": [{"name": "Generate CSV", "type": "ExecuteDataFlow", "dependsOn": [{"activity": "Set FundMapping File Name", "dependencyConditions": ["Succeeded"]}], "policy": {"timeout": "7.00:00:00", "retry": 1, "retryIntervalInSeconds": 30, "secureOutput": false, "secureInput": false}, "userProperties": [], "typeProperties": {"dataflow": {"referenceName": "IN-166 StateStreet FX", "type": "DataFlowReference", "parameters": {"inputFileName": {"value": "'@{pipeline().parameters.inputFXFileName}'", "type": "Expression"}, "inputFileName2": {"value": "'@{variables('FundFileName')}'", "type": "Expression"}, "outputFileName": {"value": "'@{variables('CSVOutFileName')}'", "type": "Expression"}, "StaticFileDir": {"value": "'@{pipeline().globalParameters.ADL_STATIC}'", "type": "Expression"}}}, "compute": {"coreCount": 144, "computeType": "General"}, "traceLevel": "Fine"}}, {"name": "Create CSV with APL Schema", "type": "Copy", "dependsOn": [{"activity": "Generate CSV", "dependencyConditions": ["Succeeded"]}], "policy": {"timeout": "7.00:00:00", "retry": 0, "retryIntervalInSeconds": 30, "secureOutput": false, "secureInput": false}, "userProperties": [], "typeProperties": {"source": {"type": "DelimitedTextSource", "additionalColumns": [{"name": "Instrument_Type", "value": {"value": "FX Spot", "type": "Expression"}}, {"name": "Transaction_Cancellation_Flag", "value": {"value": "N", "type": "Expression"}}, {"name": "Block_Trade_ID", "value": "B_000122_B"}, {"name": "Security_ID", "value": "FXS"}, {"name": "Security_ID_Type", "value": "SCD Sec ID"}, {"name": "Purpose", "value": ""}, {"name": "Counterparty", "value": "SSCA"}, {"name": "Counterpartys_dealer", "value": ""}, {"name": "Broker", "value": ""}, {"name": "Trans_code", "value": "BuyFXFwdDbd"}, {"name": "Open_close", "value": ""}, {"name": "Price", "value": ""}, {"name": "Quotation", "value": ""}, {"name": "Exchange", "value": ""}, {"name": "Margin_clearer", "value": ""}, {"name": "Type_of_settlement", "value": ""}, {"name": "Trade_time", "value": ""}, {"name": "Execution_time", "value": ""}, {"name": "Clearing_time", "value": ""}, {"name": "Confirmation_time", "value": ""}, {"name": "Settlement_comment", "value": ""}, {"name": "Instruction_comment", "value": ""}, {"name": "Dealer", "value": ""}, {"name": "Blocking_type", "value": ""}, {"name": "Blocked_until", "value": ""}, {"name": "IFRS_9_purpose", "value": ""}, {"name": "Special_holding_mark", "value": ""}, {"name": "Client", "value": ""}, {"name": "Compound", "value": ""}, {"name": "Commission", "value": ""}, {"name": "Keep_amount_in_base_Ccy", "value": "TRUE"}, {"name": "FX_tics", "value": ""}, {"name": "Place_PL_on_price_Ccy", "value": ""}, {"name": "First_fixing_date", "value": ""}, {"name": "Fixing_date", "value": ""}, {"name": "Time_option_start_date", "value": ""}, {"name": "Nominal_far", "value": ""}, {"name": "Nominal_L2_far", "value": ""}, {"name": "Maturity_date_drawn_bonds", "value": ""}, {"name": "Maturity_price_drawn_bonds", "value": ""}, {"name": "Collateral_pool", "value": ""}, {"name": "Currency_overlay_model_portfolio", "value": ""}, {"name": "Currency_overlay_model_portfolio_L2", "value": ""}, {"name": "Settlement_currency", "value": ""}, {"name": "FX_rate_QS", "value": ""}, {"name": "Transaction_origin", "value": "INTERNAL"}, {"name": "Cost", "value": ""}, {"name": "Amount_cost_currency", "value": ""}, {"name": "Cost_L2", "value": ""}, {"name": "Amount_cost_currency_L2", "value": ""}, {"name": "Underlying_Security_ID", "value": ""}, {"name": "Requirement_percentage", "value": ""}, {"name": "Dividend_all_in_percentage", "value": ""}, {"name": "Rate", "value": ""}, {"name": "Fee_type", "value": ""}, {"name": "Split_trade", "value": ""}, {"name": "Underlying_price", "value": ""}, {"name": "Underlying_holding_portfolio", "value": ""}, {"name": "Underlying_holding_nominal", "value": ""}, {"name": "Pledge_Security_ID", "value": ""}, {"name": "Account_ID", "value": ""}, {"name": "Execution_reference", "value": ""}, {"name": "Quote_currency", "value": ""}, {"name": "Current_face", "value": ""}, {"name": "Daylight_indicator", "value": ""}, {"name": "Collateral_result_ID", "value": ""}, {"name": "Destination_custody", "value": ""}, {"name": "Delivery_type", "value": ""}, {"name": "Transaction_free_code_1", "value": ""}, {"name": "Transaction_free_code_2", "value": ""}, {"name": "French_insurance_code", "value": ""}, {"name": "Transaction_free_code_4", "value": ""}, {"name": "Hedging_strategy", "value": ""}, {"name": "Transaction_free_code_42", "value": ""}, {"name": "Transaction_free_code_43", "value": ""}, {"name": "Transaction_free_code_44", "value": ""}, {"name": "Transaction_free_code_45", "value": ""}, {"name": "Transaction_free_code_46", "value": ""}, {"name": "Transaction_free_code_47", "value": ""}, {"name": "Transaction_free_code_48", "value": ""}, {"name": "Transaction_free_code_49", "value": ""}, {"name": "Transaction_free_code_50", "value": ""}, {"name": "NAIC_pledged_securities", "value": ""}, {"name": "Transaction_free_code_6", "value": ""}, {"name": "Transaction_free_code_7", "value": ""}, {"name": "Transaction_free_code_8", "value": ""}, {"name": "Transaction_free_code_9", "value": ""}, {"name": "Transaction_free_code_10", "value": ""}, {"name": "Transaction_free_code_11", "value": ""}, {"name": "Centra_date", "value": ""}, {"name": "Transaction_free_code_13", "value": ""}, {"name": "Transaction_free_code_14", "value": ""}, {"name": "Transaction_netting", "value": ""}, {"name": "CLS_bypass", "value": ""}, {"name": "NAV_date", "value": ""}, {"name": "Transaction_free_code_20", "value": ""}, {"name": "Linked_Trade_ID", "value": ""}, {"name": "Transaction_free_code_22", "value": ""}, {"name": "Transaction_free_code_23", "value": ""}, {"name": "Transaction_free_code_24", "value": ""}, {"name": "Transaction_free_code_25", "value": ""}, {"name": "Transaction_free_code_26", "value": ""}, {"name": "Status_update", "value": ""}, {"name": "CTM_status_broker", "value": ""}, {"name": "MSC_novation_fee_cpty", "value": ""}, {"name": "SWIFT_bypass", "value": ""}, {"name": "Trade_confirmation_bypass", "value": ""}, {"name": "Confirmations_means", "value": ""}, {"name": "SWIFT_MT_type", "value": ""}, {"name": "Transaction_free_code_34", "value": ""}, {"name": "Transaction_free_code_35", "value": ""}, {"name": "Transaction_free_code_36", "value": ""}, {"name": "Transaction_free_code_37", "value": ""}, {"name": "Transaction_free_code_38", "value": ""}, {"name": "Transaction_free_code_39", "value": ""}, {"name": "Transaction_free_code_40", "value": ""}, {"name": "Transaction_free_code_41", "value": ""}, {"name": "Transaction_free_code_52", "value": ""}, {"name": "MiFIR_relevant_modification", "value": ""}, {"name": "MMSC_alt_trans_type", "value": ""}, {"name": "MMSC_MTM_status_APL", "value": ""}, {"name": "MSC_clearing_status_APL", "value": ""}, {"name": "Transaction_free_code_57", "value": ""}, {"name": "Transaction_free_code_58", "value": ""}, {"name": "Transaction_free_code_59", "value": ""}, {"name": "Transaction_free_code_60", "value": ""}, {"name": "Transaction_free_code_61", "value": ""}, {"name": "Transaction_free_code_62", "value": ""}, {"name": "Transaction_free_code_63", "value": ""}, {"name": "Transaction_free_code_64", "value": ""}, {"name": "Transaction_free_code_65", "value": ""}, {"name": "Transaction_free_code_66", "value": ""}, {"name": "Transaction_free_code_67", "value": ""}, {"name": "Transaction_free_code_68", "value": ""}, {"name": "Subscription_mode", "value": ""}, {"name": "Transaction_free_code_70", "value": ""}, {"name": "Transaction_free_code_71", "value": ""}, {"name": "Transaction_free_code_72", "value": ""}, {"name": "Transaction_free_code_73", "value": ""}, {"name": "MSC_independent_val", "value": ""}, {"name": "CTM_forward_payment_amount", "value": ""}, {"name": "CTM_total_repo_interest", "value": ""}, {"name": "Transaction_free_code_77", "value": ""}, {"name": "Transaction_free_code_78", "value": ""}, {"name": "Transaction_free_code_79", "value": ""}, {"name": "Transaction_free_code_80", "value": ""}, {"name": "Transaction_free_code_81", "value": ""}, {"name": "Transaction_free_code_82", "value": ""}, {"name": "Transaction_free_code_83", "value": ""}, {"name": "Transaction_free_code_84", "value": ""}, {"name": "Transaction_free_code_85", "value": ""}, {"name": "Transaction_free_code_86", "value": ""}, {"name": "Transaction_free_code_87", "value": ""}, {"name": "Transaction_free_code_88", "value": ""}, {"name": "Transaction_free_code_89", "value": ""}, {"name": "Transaction_free_code_90", "value": ""}, {"name": "Transaction_free_code_91", "value": ""}, {"name": "Transaction_free_code_92", "value": ""}, {"name": "Transaction_free_code_93", "value": ""}, {"name": "ETD_position_UTI", "value": ""}, {"name": "CTM_transaction_no_on_allocation", "value": ""}, {"name": "Wash_sales", "value": ""}, {"name": "NAIC_purpose_derivatives", "value": ""}, {"name": "NAIC_transaction_purpose", "value": ""}, {"name": "CTM_type_of_repo_contract", "value": ""}, {"name": "Transaction_free_code_101", "value": ""}, {"name": "Transaction_free_code_96", "value": ""}, {"name": "ExpansionPlaceHolder83", "value": ""}, {"name": "ExpansionPlaceHolder84", "value": ""}, {"name": "ExpansionPlaceHolder85", "value": ""}, {"name": "ExpansionPlaceHolder86", "value": ""}, {"name": "ExpansionPlaceHolder87", "value": ""}, {"name": "ExpansionPlaceHolder88", "value": ""}, {"name": "ExpansionPlaceHolder89", "value": ""}, {"name": "ExpansionPlaceHolder90", "value": ""}, {"name": "ExpansionPlaceHolder91", "value": ""}, {"name": "ExpansionPlaceHolder92", "value": ""}, {"name": "ExpansionPlaceHolder93", "value": ""}, {"name": "ExpansionPlaceHolder94", "value": ""}, {"name": "ExpansionPlaceHolder95", "value": ""}, {"name": "ExpansionPlaceHolder96", "value": ""}, {"name": "ExpansionPlaceHolder97", "value": ""}, {"name": "ExpansionPlaceHolder98", "value": ""}, {"name": "ExpansionPlaceHolder99", "value": ""}, {"name": "ExpansionPlaceHolder100", "value": ""}, {"name": "ExpansionPlaceHolder101", "value": ""}, {"name": "ExpansionPlaceHolder102", "value": ""}, {"name": "ExpansionPlaceHolder103", "value": ""}, {"name": "ExpansionPlaceHolder104", "value": ""}, {"name": "ExpansionPlaceHolder105", "value": ""}, {"name": "ExpansionPlaceHolder106", "value": ""}, {"name": "ExpansionPlaceHolder107", "value": ""}, {"name": "ExpansionPlaceHolder108", "value": ""}, {"name": "ExpansionPlaceHolder109", "value": ""}, {"name": "ExpansionPlaceHolder110", "value": ""}, {"name": "ExpansionPlaceHolder111", "value": ""}, {"name": "ExpansionPlaceHolder112", "value": ""}, {"name": "ExpansionPlaceHolder113", "value": ""}, {"name": "ExpansionPlaceHolder114", "value": ""}, {"name": "ExpansionPlaceHolder115", "value": ""}, {"name": "ExpansionPlaceHolder116", "value": ""}, {"name": "ExpansionPlaceHolder117", "value": ""}, {"name": "ExpansionPlaceHolder118", "value": ""}, {"name": "ExpansionPlaceHolder119", "value": ""}, {"name": "ExpansionPlaceHolder120", "value": ""}, {"name": "ExpansionPlaceHolder121", "value": ""}, {"name": "ExpansionPlaceHolder122", "value": ""}, {"name": "ExpansionPlaceHolder123", "value": ""}, {"name": "ExpansionPlaceHolder124", "value": ""}, {"name": "ExpansionPlaceHolder125", "value": ""}, {"name": "ExpansionPlaceHolder126", "value": ""}, {"name": "ExpansionPlaceHolder127", "value": ""}, {"name": "ExpansionPlaceHolder128", "value": ""}, {"name": "ExpansionPlaceHolder129", "value": ""}, {"name": "ExpansionPlaceHolder130", "value": ""}, {"name": "ExpansionPlaceHolder131", "value": ""}, {"name": "ExpansionPlaceHolder132", "value": ""}, {"name": "ExpansionPlaceHolder133", "value": ""}, {"name": "ExpansionPlaceHolder134", "value": ""}, {"name": "ExpansionPlaceHolder135", "value": ""}, {"name": "ExpansionPlaceHolder136", "value": ""}, {"name": "ExpansionPlaceHolder137", "value": ""}, {"name": "ExpansionPlaceHolder138", "value": ""}, {"name": "ExpansionPlaceHolder139", "value": ""}, {"name": "ExpansionPlaceHolder140", "value": ""}, {"name": "ExpansionPlaceHolder141", "value": ""}, {"name": "ExpansionPlaceHolder142", "value": ""}, {"name": "ExpansionPlaceHolder143", "value": ""}, {"name": "ExpansionPlaceHolder144", "value": ""}, {"name": "ExpansionPlaceHolder145", "value": ""}, {"name": "ExpansionPlaceHolder146", "value": ""}, {"name": "ExpansionPlaceHolder147", "value": ""}, {"name": "ExpansionPlaceHolder148", "value": ""}, {"name": "ExpansionPlaceHolder149", "value": ""}, {"name": "ExpansionPlaceHolder150", "value": ""}, {"name": "ExpansionPlaceHolder151", "value": ""}, {"name": "ExpansionPlaceHolder152", "value": ""}, {"name": "ExpansionPlaceHolder153", "value": ""}, {"name": "ExpansionPlaceHolder154", "value": ""}, {"name": "ExpansionPlaceHolder155", "value": ""}, {"name": "ExpansionPlaceHolder156", "value": ""}, {"name": "ExpansionPlaceHolder157", "value": ""}, {"name": "ExpansionPlaceHolder158", "value": ""}, {"name": "ExpansionPlaceHolder159", "value": ""}, {"name": "ExpansionPlaceHolder160", "value": ""}, {"name": "ExpansionPlaceHolder161", "value": ""}, {"name": "ExpansionPlaceHolder162", "value": ""}, {"name": "ExpansionPlaceHolder163", "value": ""}, {"name": "ExpansionPlaceHolder164", "value": ""}, {"name": "ExpansionPlaceHolder165", "value": ""}, {"name": "ExpansionPlaceHolder166", "value": ""}, {"name": "ExpansionPlaceHolder167", "value": ""}, {"name": "ExpansionPlaceHolder168", "value": ""}, {"name": "ExpansionPlaceHolder169", "value": ""}, {"name": "ExpansionPlaceHolder170", "value": ""}, {"name": "ExpansionPlaceHolder171", "value": ""}, {"name": "ExpansionPlaceHolder172", "value": ""}, {"name": "ExpansionPlaceHolder173", "value": ""}, {"name": "ExpansionPlaceHolder174", "value": ""}, {"name": "ExpansionPlaceHolder175", "value": ""}, {"name": "ExpansionPlaceHolder176", "value": ""}, {"name": "ExpansionPlaceHolder177", "value": ""}, {"name": "ExpansionPlaceHolder178", "value": ""}, {"name": "ExpansionPlaceHolder179", "value": ""}], "storeSettings": {"type": "AzureBlobFSReadSettings", "recursive": true, "wildcardFolderPath": "FX/Statestreet/OUT", "wildcardFileName": {"value": "@variables('CSVOutFileName')", "type": "Expression"}, "enablePartitionDiscovery": false}, "formatSettings": {"type": "DelimitedTextReadSettings"}}, "sink": {"type": "DelimitedTextSink", "storeSettings": {"type": "AzureBlobFSWriteSettings"}, "formatSettings": {"type": "DelimitedTextWriteSettings", "quoteAllText": true, "fileExtension": ".csv"}}, "enableStaging": false, "translator": {"type": "TabularTranslator", "mappings": [{"source": {"name": "Instrument_Type", "type": "String"}, "sink": {"name": "1"}}, {"source": {"name": "Transaction_Cancellation_Flag", "type": "String"}, "sink": {"name": "2"}}, {"source": {"name": "Block_Trade_ID", "type": "String"}, "sink": {"name": "3"}}, {"source": {"name": "Transaction_origin_Trade_ID", "type": "String", "physicalType": "String"}, "sink": {"name": "4"}}, {"source": {"name": "Security_ID", "type": "String"}, "sink": {"name": "5"}}, {"source": {"name": "Security_ID_Type", "type": "String"}, "sink": {"name": "6"}}, {"source": {"name": "Portfolio", "type": "String", "physicalType": "String"}, "sink": {"name": "7"}}, {"source": {"name": "Model_portfolio", "type": "String"}, "sink": {"name": "8"}}, {"source": {"name": "Purpose", "type": "String"}, "sink": {"name": "9"}}, {"source": {"name": "Counterparty", "type": "String"}, "sink": {"name": "10"}}, {"source": {"name": "Counterpartys_dealer", "type": "String"}, "sink": {"name": "11"}}, {"source": {"name": "Broker", "type": "String"}, "sink": {"name": "12"}}, {"source": {"name": "Trans_code", "type": "String"}, "sink": {"name": "13"}}, {"source": {"name": "Open_close", "type": "String"}, "sink": {"name": "14"}}, {"source": {"name": "Nominal", "type": "String", "physicalType": "String"}, "sink": {"name": "15"}}, {"source": {"name": "Price", "type": "String"}, "sink": {"name": "16"}}, {"source": {"name": "Quotation", "type": "String"}, "sink": {"name": "17"}}, {"source": {"name": "Exchange", "type": "String"}, "sink": {"name": "18"}}, {"source": {"name": "Margin_clearer", "type": "String"}, "sink": {"name": "19"}}, {"source": {"name": "Trade_date", "type": "String", "physicalType": "String"}, "sink": {"name": "20"}}, {"source": {"name": "Payment_date", "type": "String", "physicalType": "String"}, "sink": {"name": "21"}}, {"source": {"name": "Type_of_settlement", "type": "String"}, "sink": {"name": "22"}}, {"source": {"name": "Trade_time", "type": "String"}, "sink": {"name": "23"}}, {"source": {"name": "Execution_time", "type": "String"}, "sink": {"name": "24"}}, {"source": {"name": "Clearing_time", "type": "String"}, "sink": {"name": "25"}}, {"source": {"name": "Confirmation_time", "type": "String"}, "sink": {"name": "26"}}, {"source": {"name": "Settlement_comment", "type": "String"}, "sink": {"name": "27"}}, {"source": {"name": "Instruction_comment", "type": "String"}, "sink": {"name": "28"}}, {"source": {"name": "Dealer", "type": "String"}, "sink": {"name": "29"}}, {"source": {"name": "Blocking_type", "type": "String"}, "sink": {"name": "30"}}, {"source": {"name": "Blocked_until", "type": "String"}, "sink": {"name": "31"}}, {"source": {"name": "IFRS_9_purpose", "type": "String"}, "sink": {"name": "32"}}, {"source": {"name": "Special_holding_mark", "type": "String"}, "sink": {"name": "33"}}, {"source": {"name": "Client", "type": "String"}, "sink": {"name": "34"}}, {"source": {"name": "Compound", "type": "String"}, "sink": {"name": "35"}}, {"source": {"name": "Commission", "type": "String"}, "sink": {"name": "36"}}, {"source": {"name": "Nominal_currency", "type": "String", "physicalType": "String"}, "sink": {"name": "37"}}, {"source": {"name": "Nominal_L2", "type": "String", "physicalType": "String"}, "sink": {"name": "38"}}, {"source": {"name": "Keep_amount_in_base_Ccy", "type": "String"}, "sink": {"name": "39"}}, {"source": {"name": "Nominal_L2_currency", "type": "String", "physicalType": "String"}, "sink": {"name": "40"}}, {"source": {"name": "FX_rate_spot", "type": "String", "physicalType": "String"}, "sink": {"name": "41"}}, {"source": {"name": "FX_tics", "type": "String"}, "sink": {"name": "42"}}, {"source": {"name": "FX_rate_forward", "type": "String", "physicalType": "String"}, "sink": {"name": "43"}}, {"source": {"name": "Place_PL_on_price_Ccy", "type": "String"}, "sink": {"name": "44"}}, {"source": {"name": "Maturity_date", "type": "String", "physicalType": "String"}, "sink": {"name": "45"}}, {"source": {"name": "First_fixing_date", "type": "String"}, "sink": {"name": "46"}}, {"source": {"name": "Fixing_date", "type": "String"}, "sink": {"name": "47"}}, {"source": {"name": "Time_option_start_date", "type": "String"}, "sink": {"name": "48"}}, {"source": {"name": "Nominal_far", "type": "String"}, "sink": {"name": "49"}}, {"source": {"name": "Nominal_L2_far", "type": "String"}, "sink": {"name": "50"}}, {"source": {"name": "Maturity_date_drawn_bonds", "type": "String"}, "sink": {"name": "51"}}, {"source": {"name": "Maturity_price_drawn_bonds", "type": "String"}, "sink": {"name": "52"}}, {"source": {"name": "Collateral_pool", "type": "String"}, "sink": {"name": "53"}}, {"source": {"name": "Currency_overlay_model_portfolio", "type": "String"}, "sink": {"name": "54"}}, {"source": {"name": "Currency_overlay_model_portfolio_L2", "type": "String"}, "sink": {"name": "55"}}, {"source": {"name": "Settlement_currency", "type": "String"}, "sink": {"name": "56"}}, {"source": {"name": "FX_rate_QS", "type": "String"}, "sink": {"name": "57"}}, {"source": {"name": "Transaction_origin", "type": "String"}, "sink": {"name": "58"}}, {"source": {"name": "Cost", "type": "String"}, "sink": {"name": "59"}}, {"source": {"name": "<PERSON><PERSON><PERSON><PERSON>", "type": "String", "physicalType": "String"}, "sink": {"name": "60"}}, {"source": {"name": "Amount_cost_currency", "type": "String"}, "sink": {"name": "61"}}, {"source": {"name": "Cost_L2", "type": "String"}, "sink": {"name": "62"}}, {"source": {"name": "Currency_L2", "type": "String", "physicalType": "String"}, "sink": {"name": "63"}}, {"source": {"name": "Amount_cost_currency_L2", "type": "String"}, "sink": {"name": "64"}}, {"source": {"name": "Underlying_Security_ID", "type": "String"}, "sink": {"name": "65"}}, {"source": {"name": "Requirement_percentage", "type": "String"}, "sink": {"name": "66"}}, {"source": {"name": "Dividend_all_in_percentage", "type": "String"}, "sink": {"name": "67"}}, {"source": {"name": "Rate", "type": "String"}, "sink": {"name": "68"}}, {"source": {"name": "Fee_type", "type": "String"}, "sink": {"name": "69"}}, {"source": {"name": "Split_trade", "type": "String"}, "sink": {"name": "70"}}, {"source": {"name": "Underlying_price", "type": "String"}, "sink": {"name": "71"}}, {"source": {"name": "Underlying_holding_portfolio", "type": "String"}, "sink": {"name": "72"}}, {"source": {"name": "Underlying_holding_nominal", "type": "String"}, "sink": {"name": "73"}}, {"source": {"name": "Pledge_Security_ID", "type": "String"}, "sink": {"name": "74"}}, {"source": {"name": "Account_ID", "type": "String"}, "sink": {"name": "75"}}, {"source": {"name": "Execution_reference", "type": "String"}, "sink": {"name": "76"}}, {"source": {"name": "Quote_currency", "type": "String"}, "sink": {"name": "77"}}, {"source": {"name": "Current_face", "type": "String"}, "sink": {"name": "78"}}, {"source": {"name": "Daylight_indicator", "type": "String"}, "sink": {"name": "79"}}, {"source": {"name": "Collateral_result_ID", "type": "String"}, "sink": {"name": "80"}}, {"source": {"name": "Destination_custody", "type": "String"}, "sink": {"name": "81"}}, {"source": {"name": "Delivery_type", "type": "String"}, "sink": {"name": "82"}}, {"source": {"name": "ExpansionPlaceHolder83", "type": "String"}, "sink": {"name": "83"}}, {"source": {"name": "ExpansionPlaceHolder84", "type": "String"}, "sink": {"name": "84"}}, {"source": {"name": "ExpansionPlaceHolder85", "type": "String"}, "sink": {"name": "85"}}, {"source": {"name": "ExpansionPlaceHolder86", "type": "String"}, "sink": {"name": "86"}}, {"source": {"name": "ExpansionPlaceHolder87", "type": "String"}, "sink": {"name": "87"}}, {"source": {"name": "ExpansionPlaceHolder88", "type": "String"}, "sink": {"name": "88"}}, {"source": {"name": "ExpansionPlaceHolder89", "type": "String"}, "sink": {"name": "89"}}, {"source": {"name": "ExpansionPlaceHolder90", "type": "String"}, "sink": {"name": "90"}}, {"source": {"name": "ExpansionPlaceHolder91", "type": "String"}, "sink": {"name": "91"}}, {"source": {"name": "ExpansionPlaceHolder92", "type": "String"}, "sink": {"name": "92"}}, {"source": {"name": "ExpansionPlaceHolder93", "type": "String"}, "sink": {"name": "93"}}, {"source": {"name": "ExpansionPlaceHolder94", "type": "String"}, "sink": {"name": "94"}}, {"source": {"name": "ExpansionPlaceHolder95", "type": "String"}, "sink": {"name": "95"}}, {"source": {"name": "ExpansionPlaceHolder96", "type": "String"}, "sink": {"name": "96"}}, {"source": {"name": "ExpansionPlaceHolder97", "type": "String"}, "sink": {"name": "97"}}, {"source": {"name": "ExpansionPlaceHolder98", "type": "String"}, "sink": {"name": "98"}}, {"source": {"name": "ExpansionPlaceHolder99", "type": "String"}, "sink": {"name": "99"}}, {"source": {"name": "ExpansionPlaceHolder100", "type": "String"}, "sink": {"name": "100"}}, {"source": {"name": "ExpansionPlaceHolder101", "type": "String"}, "sink": {"name": "101"}}, {"source": {"name": "ExpansionPlaceHolder102", "type": "String"}, "sink": {"name": "102"}}, {"source": {"name": "ExpansionPlaceHolder103", "type": "String"}, "sink": {"name": "103"}}, {"source": {"name": "ExpansionPlaceHolder104", "type": "String"}, "sink": {"name": "104"}}, {"source": {"name": "ExpansionPlaceHolder105", "type": "String"}, "sink": {"name": "105"}}, {"source": {"name": "ExpansionPlaceHolder106", "type": "String"}, "sink": {"name": "106"}}, {"source": {"name": "ExpansionPlaceHolder107", "type": "String"}, "sink": {"name": "107"}}, {"source": {"name": "ExpansionPlaceHolder108", "type": "String"}, "sink": {"name": "108"}}, {"source": {"name": "ExpansionPlaceHolder109", "type": "String"}, "sink": {"name": "109"}}, {"source": {"name": "ExpansionPlaceHolder110", "type": "String"}, "sink": {"name": "110"}}, {"source": {"name": "ExpansionPlaceHolder111", "type": "String"}, "sink": {"name": "111"}}, {"source": {"name": "ExpansionPlaceHolder112", "type": "String"}, "sink": {"name": "112"}}, {"source": {"name": "ExpansionPlaceHolder113", "type": "String"}, "sink": {"name": "113"}}, {"source": {"name": "ExpansionPlaceHolder114", "type": "String"}, "sink": {"name": "114"}}, {"source": {"name": "ExpansionPlaceHolder115", "type": "String"}, "sink": {"name": "115"}}, {"source": {"name": "ExpansionPlaceHolder116", "type": "String"}, "sink": {"name": "116"}}, {"source": {"name": "ExpansionPlaceHolder117", "type": "String"}, "sink": {"name": "117"}}, {"source": {"name": "ExpansionPlaceHolder118", "type": "String"}, "sink": {"name": "118"}}, {"source": {"name": "ExpansionPlaceHolder119", "type": "String"}, "sink": {"name": "119"}}, {"source": {"name": "ExpansionPlaceHolder120", "type": "String"}, "sink": {"name": "120"}}, {"source": {"name": "ExpansionPlaceHolder121", "type": "String"}, "sink": {"name": "121"}}, {"source": {"name": "ExpansionPlaceHolder122", "type": "String"}, "sink": {"name": "122"}}, {"source": {"name": "ExpansionPlaceHolder123", "type": "String"}, "sink": {"name": "123"}}, {"source": {"name": "ExpansionPlaceHolder124", "type": "String"}, "sink": {"name": "124"}}, {"source": {"name": "ExpansionPlaceHolder125", "type": "String"}, "sink": {"name": "125"}}, {"source": {"name": "ExpansionPlaceHolder126", "type": "String"}, "sink": {"name": "126"}}, {"source": {"name": "ExpansionPlaceHolder127", "type": "String"}, "sink": {"name": "127"}}, {"source": {"name": "ExpansionPlaceHolder128", "type": "String"}, "sink": {"name": "128"}}, {"source": {"name": "ExpansionPlaceHolder129", "type": "String"}, "sink": {"name": "129"}}, {"source": {"name": "ExpansionPlaceHolder130", "type": "String"}, "sink": {"name": "130"}}, {"source": {"name": "ExpansionPlaceHolder131", "type": "String"}, "sink": {"name": "131"}}, {"source": {"name": "ExpansionPlaceHolder132", "type": "String"}, "sink": {"name": "132"}}, {"source": {"name": "ExpansionPlaceHolder133", "type": "String"}, "sink": {"name": "133"}}, {"source": {"name": "ExpansionPlaceHolder134", "type": "String"}, "sink": {"name": "134"}}, {"source": {"name": "ExpansionPlaceHolder135", "type": "String"}, "sink": {"name": "135"}}, {"source": {"name": "ExpansionPlaceHolder136", "type": "String"}, "sink": {"name": "136"}}, {"source": {"name": "ExpansionPlaceHolder137", "type": "String"}, "sink": {"name": "137"}}, {"source": {"name": "ExpansionPlaceHolder138", "type": "String"}, "sink": {"name": "138"}}, {"source": {"name": "ExpansionPlaceHolder139", "type": "String"}, "sink": {"name": "139"}}, {"source": {"name": "ExpansionPlaceHolder140", "type": "String"}, "sink": {"name": "140"}}, {"source": {"name": "ExpansionPlaceHolder141", "type": "String"}, "sink": {"name": "141"}}, {"source": {"name": "ExpansionPlaceHolder142", "type": "String"}, "sink": {"name": "142"}}, {"source": {"name": "ExpansionPlaceHolder143", "type": "String"}, "sink": {"name": "143"}}, {"source": {"name": "ExpansionPlaceHolder144", "type": "String"}, "sink": {"name": "144"}}, {"source": {"name": "ExpansionPlaceHolder145", "type": "String"}, "sink": {"name": "145"}}, {"source": {"name": "ExpansionPlaceHolder146", "type": "String"}, "sink": {"name": "146"}}, {"source": {"name": "ExpansionPlaceHolder147", "type": "String"}, "sink": {"name": "147"}}, {"source": {"name": "ExpansionPlaceHolder148", "type": "String"}, "sink": {"name": "148"}}, {"source": {"name": "ExpansionPlaceHolder149", "type": "String"}, "sink": {"name": "149"}}, {"source": {"name": "ExpansionPlaceHolder150", "type": "String"}, "sink": {"name": "150"}}, {"source": {"name": "ExpansionPlaceHolder151", "type": "String"}, "sink": {"name": "151"}}, {"source": {"name": "ExpansionPlaceHolder152", "type": "String"}, "sink": {"name": "152"}}, {"source": {"name": "ExpansionPlaceHolder153", "type": "String"}, "sink": {"name": "153"}}, {"source": {"name": "ExpansionPlaceHolder154", "type": "String"}, "sink": {"name": "154"}}, {"source": {"name": "ExpansionPlaceHolder155", "type": "String"}, "sink": {"name": "155"}}, {"source": {"name": "ExpansionPlaceHolder156", "type": "String"}, "sink": {"name": "156"}}, {"source": {"name": "ExpansionPlaceHolder157", "type": "String"}, "sink": {"name": "157"}}, {"source": {"name": "ExpansionPlaceHolder158", "type": "String"}, "sink": {"name": "158"}}, {"source": {"name": "ExpansionPlaceHolder159", "type": "String"}, "sink": {"name": "159"}}, {"source": {"name": "ExpansionPlaceHolder160", "type": "String"}, "sink": {"name": "160"}}, {"source": {"name": "ExpansionPlaceHolder161", "type": "String"}, "sink": {"name": "161"}}, {"source": {"name": "ExpansionPlaceHolder162", "type": "String"}, "sink": {"name": "162"}}, {"source": {"name": "ExpansionPlaceHolder163", "type": "String"}, "sink": {"name": "163"}}, {"source": {"name": "ExpansionPlaceHolder164", "type": "String"}, "sink": {"name": "164"}}, {"source": {"name": "ExpansionPlaceHolder165", "type": "String"}, "sink": {"name": "165"}}, {"source": {"name": "ExpansionPlaceHolder166", "type": "String"}, "sink": {"name": "166"}}, {"source": {"name": "ExpansionPlaceHolder167", "type": "String"}, "sink": {"name": "167"}}, {"source": {"name": "ExpansionPlaceHolder168", "type": "String"}, "sink": {"name": "168"}}, {"source": {"name": "ExpansionPlaceHolder169", "type": "String"}, "sink": {"name": "169"}}, {"source": {"name": "ExpansionPlaceHolder170", "type": "String"}, "sink": {"name": "170"}}, {"source": {"name": "ExpansionPlaceHolder171", "type": "String"}, "sink": {"name": "171"}}, {"source": {"name": "ExpansionPlaceHolder172", "type": "String"}, "sink": {"name": "172"}}, {"source": {"name": "ExpansionPlaceHolder173", "type": "String"}, "sink": {"name": "173"}}, {"source": {"name": "ExpansionPlaceHolder174", "type": "String"}, "sink": {"name": "174"}}, {"source": {"name": "ExpansionPlaceHolder175", "type": "String"}, "sink": {"name": "175"}}, {"source": {"name": "ExpansionPlaceHolder176", "type": "String"}, "sink": {"name": "176"}}, {"source": {"name": "ExpansionPlaceHolder177", "type": "String"}, "sink": {"name": "177"}}, {"source": {"name": "ExpansionPlaceHolder178", "type": "String"}, "sink": {"name": "178"}}, {"source": {"name": "ExpansionPlaceHolder179", "type": "String"}, "sink": {"name": "179"}}, {"source": {"name": "Transaction_free_code_1", "type": "String"}, "sink": {"name": "180"}}, {"source": {"name": "Transaction_free_code_2", "type": "String"}, "sink": {"name": "181"}}, {"source": {"name": "French_insurance_code", "type": "String"}, "sink": {"name": "182"}}, {"source": {"name": "Transaction_free_code_4", "type": "String"}, "sink": {"name": "183"}}, {"source": {"name": "Hedging_strategy", "type": "String"}, "sink": {"name": "184"}}, {"source": {"name": "Transaction_free_code_42", "type": "String"}, "sink": {"name": "185"}}, {"source": {"name": "Transaction_free_code_43", "type": "String"}, "sink": {"name": "186"}}, {"source": {"name": "Transaction_free_code_44", "type": "String"}, "sink": {"name": "187"}}, {"source": {"name": "Transaction_free_code_45", "type": "String"}, "sink": {"name": "188"}}, {"source": {"name": "Transaction_free_code_46", "type": "String"}, "sink": {"name": "189"}}, {"source": {"name": "Transaction_free_code_47", "type": "String"}, "sink": {"name": "190"}}, {"source": {"name": "Transaction_free_code_48", "type": "String"}, "sink": {"name": "191"}}, {"source": {"name": "Transaction_free_code_49", "type": "String"}, "sink": {"name": "192"}}, {"source": {"name": "Transaction_free_code_50", "type": "String"}, "sink": {"name": "193"}}, {"source": {"name": "NAIC_pledged_securities", "type": "String"}, "sink": {"name": "194"}}, {"source": {"name": "Transaction_free_code_6", "type": "String"}, "sink": {"name": "195"}}, {"source": {"name": "Transaction_free_code_7", "type": "String"}, "sink": {"name": "196"}}, {"source": {"name": "Transaction_free_code_8", "type": "String"}, "sink": {"name": "197"}}, {"source": {"name": "Transaction_free_code_9", "type": "String"}, "sink": {"name": "198"}}, {"source": {"name": "Transaction_free_code_10", "type": "String"}, "sink": {"name": "199"}}, {"source": {"name": "Transaction_free_code_11", "type": "String"}, "sink": {"name": "200"}}, {"source": {"name": "Centra_date", "type": "String"}, "sink": {"name": "201"}}, {"source": {"name": "Transaction_free_code_13", "type": "String"}, "sink": {"name": "202"}}, {"source": {"name": "Transaction_free_code_14", "type": "String"}, "sink": {"name": "203"}}, {"source": {"name": "Transaction_netting", "type": "String"}, "sink": {"name": "204"}}, {"source": {"name": "CLS_bypass", "type": "String"}, "sink": {"name": "205"}}, {"source": {"name": "NAV_date", "type": "String"}, "sink": {"name": "206"}}, {"source": {"name": "Transaction_free_code_20", "type": "String"}, "sink": {"name": "207"}}, {"source": {"name": "Linked_Trade_ID", "type": "String"}, "sink": {"name": "208"}}, {"source": {"name": "Transaction_free_code_22", "type": "String"}, "sink": {"name": "209"}}, {"source": {"name": "Transaction_free_code_23", "type": "String"}, "sink": {"name": "210"}}, {"source": {"name": "Transaction_free_code_24", "type": "String"}, "sink": {"name": "211"}}, {"source": {"name": "Transaction_free_code_25", "type": "String"}, "sink": {"name": "212"}}, {"source": {"name": "Transaction_free_code_26", "type": "String"}, "sink": {"name": "213"}}, {"source": {"name": "Status_update", "type": "String"}, "sink": {"name": "214"}}, {"source": {"name": "CTM_status_broker", "type": "String"}, "sink": {"name": "215"}}, {"source": {"name": "MSC_novation_fee_cpty", "type": "String"}, "sink": {"name": "216"}}, {"source": {"name": "SWIFT_bypass", "type": "String"}, "sink": {"name": "217"}}, {"source": {"name": "Trade_confirmation_bypass", "type": "String"}, "sink": {"name": "218"}}, {"source": {"name": "Confirmations_means", "type": "String"}, "sink": {"name": "219"}}, {"source": {"name": "SWIFT_MT_type", "type": "String"}, "sink": {"name": "220"}}, {"source": {"name": "Transaction_free_code_34", "type": "String"}, "sink": {"name": "221"}}, {"source": {"name": "Transaction_free_code_35", "type": "String"}, "sink": {"name": "222"}}, {"source": {"name": "Transaction_free_code_36", "type": "String"}, "sink": {"name": "223"}}, {"source": {"name": "Transaction_free_code_37", "type": "String"}, "sink": {"name": "224"}}, {"source": {"name": "Transaction_free_code_38", "type": "String"}, "sink": {"name": "225"}}, {"source": {"name": "Transaction_free_code_39", "type": "String"}, "sink": {"name": "226"}}, {"source": {"name": "Transaction_free_code_40", "type": "String"}, "sink": {"name": "227"}}, {"source": {"name": "Transaction_free_code_41", "type": "String"}, "sink": {"name": "228"}}, {"source": {"name": "Transaction_free_code_52", "type": "String"}, "sink": {"name": "229"}}, {"source": {"name": "MiFIR_relevant_modification", "type": "String"}, "sink": {"name": "230"}}, {"source": {"name": "MMSC_alt_trans_type", "type": "String"}, "sink": {"name": "231"}}, {"source": {"name": "MMSC_MTM_status_APL", "type": "String"}, "sink": {"name": "232"}}, {"source": {"name": "MSC_clearing_status_APL", "type": "String"}, "sink": {"name": "233"}}, {"source": {"name": "Transaction_free_code_57", "type": "String"}, "sink": {"name": "234"}}, {"source": {"name": "Transaction_free_code_58", "type": "String"}, "sink": {"name": "235"}}, {"source": {"name": "Transaction_free_code_59", "type": "String"}, "sink": {"name": "236"}}, {"source": {"name": "Transaction_free_code_60", "type": "String"}, "sink": {"name": "237"}}, {"source": {"name": "Transaction_free_code_61", "type": "String"}, "sink": {"name": "238"}}, {"source": {"name": "Transaction_free_code_62", "type": "String"}, "sink": {"name": "239"}}, {"source": {"name": "Transaction_free_code_63", "type": "String"}, "sink": {"name": "240"}}, {"source": {"name": "Transaction_free_code_64", "type": "String"}, "sink": {"name": "241"}}, {"source": {"name": "Transaction_free_code_65", "type": "String"}, "sink": {"name": "242"}}, {"source": {"name": "Transaction_free_code_66", "type": "String"}, "sink": {"name": "243"}}, {"source": {"name": "Transaction_free_code_67", "type": "String"}, "sink": {"name": "244"}}, {"source": {"name": "Transaction_free_code_68", "type": "String"}, "sink": {"name": "245"}}, {"source": {"name": "Subscription_mode", "type": "String"}, "sink": {"name": "246"}}, {"source": {"name": "Transaction_free_code_70", "type": "String"}, "sink": {"name": "247"}}, {"source": {"name": "Transaction_free_code_71", "type": "String"}, "sink": {"name": "248"}}, {"source": {"name": "Transaction_free_code_72", "type": "String"}, "sink": {"name": "249"}}, {"source": {"name": "Transaction_free_code_73", "type": "String"}, "sink": {"name": "250"}}, {"source": {"name": "MSC_independent_val", "type": "String"}, "sink": {"name": "251"}}, {"source": {"name": "CTM_forward_payment_amount", "type": "String"}, "sink": {"name": "252"}}, {"source": {"name": "CTM_total_repo_interest", "type": "String"}, "sink": {"name": "253"}}, {"source": {"name": "Transaction_free_code_77", "type": "String"}, "sink": {"name": "254"}}, {"source": {"name": "Transaction_free_code_78", "type": "String"}, "sink": {"name": "255"}}, {"source": {"name": "Transaction_free_code_79", "type": "String"}, "sink": {"name": "256"}}, {"source": {"name": "Transaction_free_code_80", "type": "String"}, "sink": {"name": "257"}}, {"source": {"name": "Transaction_free_code_81", "type": "String"}, "sink": {"name": "258"}}, {"source": {"name": "Transaction_free_code_82", "type": "String"}, "sink": {"name": "259"}}, {"source": {"name": "Transaction_free_code_83", "type": "String"}, "sink": {"name": "260"}}, {"source": {"name": "Transaction_free_code_84", "type": "String"}, "sink": {"name": "261"}}, {"source": {"name": "Transaction_free_code_85", "type": "String"}, "sink": {"name": "262"}}, {"source": {"name": "Transaction_free_code_86", "type": "String"}, "sink": {"name": "263"}}, {"source": {"name": "Transaction_free_code_87", "type": "String"}, "sink": {"name": "264"}}, {"source": {"name": "Transaction_free_code_88", "type": "String"}, "sink": {"name": "265"}}, {"source": {"name": "Transaction_free_code_89", "type": "String"}, "sink": {"name": "266"}}, {"source": {"name": "Transaction_free_code_90", "type": "String"}, "sink": {"name": "267"}}, {"source": {"name": "Transaction_free_code_91", "type": "String"}, "sink": {"name": "268"}}, {"source": {"name": "Transaction_free_code_92", "type": "String"}, "sink": {"name": "269"}}, {"source": {"name": "Transaction_free_code_93", "type": "String"}, "sink": {"name": "270"}}, {"source": {"name": "ETD_position_UTI", "type": "String"}, "sink": {"name": "271"}}, {"source": {"name": "CTM_transaction_no_on_allocation", "type": "String"}, "sink": {"name": "272"}}, {"source": {"name": "Transaction_free_code_96", "type": "String"}, "sink": {"name": "273"}}, {"source": {"name": "Wash_sales", "type": "String"}, "sink": {"name": "274"}}, {"source": {"name": "NAIC_purpose_derivatives", "type": "String"}, "sink": {"name": "275"}}, {"source": {"name": "NAIC_transaction_purpose", "type": "String"}, "sink": {"name": "276"}}, {"source": {"name": "CTM_type_of_repo_contract", "type": "String"}, "sink": {"name": "277"}}, {"source": {"name": "Transaction_free_code_101", "type": "String"}, "sink": {"name": "278"}}], "typeConversion": true, "typeConversionSettings": {"allowDataTruncation": true, "treatBooleanAsNumber": false}}}, "inputs": [{"referenceName": "AzureDataLakeStorage_FX_Statestreet_OUT_01", "type": "DatasetReference"}], "outputs": [{"referenceName": "AzureDataLakeStorage_FX_DIR", "type": "DatasetReference", "parameters": {"Directory": "FX/Statestreet/OUT"}}]}, {"name": "Set FX Out File Name", "type": "SetVariable", "dependsOn": [], "policy": {"secureOutput": false, "secureInput": false}, "userProperties": [], "typeProperties": {"variableName": "CSVOutFileName", "value": {"value": "@pipeline().parameters.CSVOutFileName", "type": "Expression"}}}, {"name": "FX_AddAdditionalHeaderRows", "type": "ExecuteDataFlow", "dependsOn": [{"activity": "Create CSV with APL Schema", "dependencyConditions": ["Succeeded"]}], "policy": {"timeout": "7.00:00:00", "retry": 1, "retryIntervalInSeconds": 30, "secureOutput": false, "secureInput": false}, "userProperties": [], "typeProperties": {"dataflow": {"referenceName": "IN-218 AddAdditionalHeaderRows FX", "type": "DataFlowReference", "parameters": {"outputFileName": {"value": "'@{variables('CSVOutFileName')}'", "type": "Expression"}, "outputDirectory": {"value": "'@{variables('outDirectory')}'", "type": "Expression"}}}, "compute": {"coreCount": 16, "computeType": "General"}, "traceLevel": "Fine"}}, {"name": "Set FundMapping File Name", "type": "SetVariable", "dependsOn": [{"activity": "Set FX Out File Name", "dependencyConditions": ["Succeeded"]}], "policy": {"secureOutput": false, "secureInput": false}, "userProperties": [], "typeProperties": {"variableName": "FundFileName", "value": {"value": "@concat('FundPortfolioCodeMapping','.csv')", "type": "Expression"}}}, {"name": "If Send to SCD", "type": "IfCondition", "dependsOn": [{"activity": "FX_AddAdditionalHeaderRows", "dependencyConditions": ["Succeeded"]}], "userProperties": [], "typeProperties": {"expression": {"value": "@pipeline().globalParameters.SendToSCD_FX", "type": "Expression"}, "ifTrueActivities": [{"name": "Copy XML to SCD SFTP", "type": "Copy", "dependsOn": [], "policy": {"timeout": "7.00:00:00", "retry": 0, "retryIntervalInSeconds": 30, "secureOutput": false, "secureInput": false}, "userProperties": [], "typeProperties": {"source": {"type": "BinarySource", "storeSettings": {"type": "AzureBlobFSReadSettings", "recursive": true, "wildcardFolderPath": "FX/Statestreet/OUT", "wildcardFileName": "*.csv", "deleteFilesAfterCompletion": false}, "formatSettings": {"type": "BinaryReadSettings"}}, "sink": {"type": "BinarySink", "storeSettings": {"type": "SftpWriteSettings", "operationTimeout": "01:00:00", "useTempFileRename": true}}, "enableStaging": false}, "inputs": [{"referenceName": "AzureDataLakeStorage_FX_Binary", "type": "DatasetReference", "parameters": {"SourceDirectory": "FX/Statestreet/OUT"}}], "outputs": [{"referenceName": "SCDSFTP_TradeCapture_01", "type": "DatasetReference", "parameters": {"SCD_SFTP_FX": {"value": "@concat(pipeline().globalParameters.SCD_ENV, pipeline().globalParameters.SCD_SFTP_FX)", "type": "Expression"}}}]}]}}, {"name": "Archive IN", "type": "Copy", "dependsOn": [{"activity": "If Send to SCD", "dependencyConditions": ["Succeeded"]}], "policy": {"timeout": "7.00:00:00", "retry": 0, "retryIntervalInSeconds": 30, "secureOutput": false, "secureInput": false}, "userProperties": [], "typeProperties": {"source": {"type": "BinarySource", "storeSettings": {"type": "AzureBlobFSReadSettings", "recursive": true, "deleteFilesAfterCompletion": true}, "formatSettings": {"type": "BinaryReadSettings"}}, "sink": {"type": "BinarySink", "storeSettings": {"type": "AzureBlobFSWriteSettings"}}, "enableStaging": false}, "inputs": [{"referenceName": "ADL_Binary_Dir", "type": "DatasetReference", "parameters": {"Path": "FX/Statestreet/IN"}}], "outputs": [{"referenceName": "ADL_Binary_Dir", "type": "DatasetReference", "parameters": {"Path": {"value": "@concat('FX/Statestreet/ARCHIVE/',pipeline().parameters.Date,'/IN')", "type": "Expression"}}}]}, {"name": "Archive OUT", "type": "Copy", "dependsOn": [{"activity": "If Send to SCD", "dependencyConditions": ["Succeeded"]}], "policy": {"timeout": "7.00:00:00", "retry": 0, "retryIntervalInSeconds": 30, "secureOutput": false, "secureInput": false}, "userProperties": [], "typeProperties": {"source": {"type": "BinarySource", "storeSettings": {"type": "AzureBlobFSReadSettings", "recursive": true, "deleteFilesAfterCompletion": true}, "formatSettings": {"type": "BinaryReadSettings"}}, "sink": {"type": "BinarySink", "storeSettings": {"type": "AzureBlobFSWriteSettings"}}, "enableStaging": false}, "inputs": [{"referenceName": "ADL_Binary_Dir", "type": "DatasetReference", "parameters": {"Path": "FX/Statestreet/OUT"}}], "outputs": [{"referenceName": "ADL_Binary_Dir", "type": "DatasetReference", "parameters": {"Path": {"value": "@concat('FX/Statestreet/ARCHIVE/',pipeline().parameters.Date,'/OUT')", "type": "Expression"}}}]}], "parameters": {"inputFXFileName": {"type": "string"}, "CSVOutFileName": {"type": "string"}, "Date": {"type": "string"}}, "variables": {"CSVOutFileName": {"type": "String", "defaultValue": "StateStreet_FX.csv"}, "outDirectory": {"type": "String", "defaultValue": "FX/Statestreet/OUT/"}, "FundFileName": {"type": "String"}}, "folder": {"name": "State Street/SSCA FX"}, "annotations": [], "lastPublishTime": "2023-12-11T22:28:54Z"}, "type": "Microsoft.DataFactory/factories/pipelines"}