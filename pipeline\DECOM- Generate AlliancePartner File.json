{"name": "DECOM- Generate AlliancePartner File", "properties": {"activities": [{"name": "Generate Portfolio Template", "type": "Copy", "dependsOn": [], "policy": {"timeout": "7.00:00:00", "retry": 0, "retryIntervalInSeconds": 30, "secureOutput": false, "secureInput": false}, "userProperties": [], "typeProperties": {"source": {"type": "SqlMISource", "sqlReaderQuery": "SELECT *  FROM Billing.QFM.vw_AlliancePartnerPortfolio", "partitionOption": "None"}, "sink": {"type": "DelimitedTextSink", "storeSettings": {"type": "AzureBlobFSWriteSettings"}, "formatSettings": {"type": "DelimitedTextWriteSettings", "quoteAllText": true, "fileExtension": ""}}, "enableStaging": false, "translator": {"type": "TabularTranslator", "typeConversion": true, "typeConversionSettings": {"allowDataTruncation": true, "treatBooleanAsNumber": false}}}, "inputs": [{"referenceName": "SQLMI_MOW_PARAM", "type": "DatasetReference", "parameters": {"DBName": "Billing", "SchemaName": "QFM", "TableName": "vw_AlliancePartnerPortfolio"}}], "outputs": [{"referenceName": "ADL_Billing_Parameterized_v2", "type": "DatasetReference", "parameters": {"FilePath": {"value": "@concat('Billing/QFM/SendToQFM/MOW/',pipeline().parameters.RunDate)", "type": "Expression"}, "FileName": {"value": "@concat('273_AP_Portfolio_',pipeline().parameters.RunDate,'_',formatDateTime(convertFromUtc(utcnow(),'Mountain Standard Time' ),'HHmmss'),'.csv')\n", "type": "Expression"}}}]}, {"name": "Generate Portfolio Group Tempalate", "type": "Copy", "dependsOn": [{"activity": "Generate Portfolio Template", "dependencyConditions": ["Succeeded"]}], "policy": {"timeout": "7.00:00:00", "retry": 0, "retryIntervalInSeconds": 30, "secureOutput": false, "secureInput": false}, "userProperties": [], "typeProperties": {"source": {"type": "SqlMISource", "sqlReaderQuery": "SELECT  *\n  FROM Billing.QFM.vw_AlliancePartnerPortfolioGroup", "partitionOption": "None"}, "sink": {"type": "DelimitedTextSink", "storeSettings": {"type": "AzureBlobFSWriteSettings"}, "formatSettings": {"type": "DelimitedTextWriteSettings", "quoteAllText": true, "fileExtension": ""}}, "enableStaging": false, "translator": {"type": "TabularTranslator", "typeConversion": true, "typeConversionSettings": {"allowDataTruncation": true, "treatBooleanAsNumber": false}}}, "inputs": [{"referenceName": "SQLMI_MOW_PARAM", "type": "DatasetReference", "parameters": {"DBName": "Billing", "SchemaName": "QFM", "TableName": "vw_AlliancePartnerPortfolioGroup"}}], "outputs": [{"referenceName": "ADL_Billing_Parameterized_v2", "type": "DatasetReference", "parameters": {"FilePath": {"value": "@concat('Billing/QFM/SendToQFM/MOW/',pipeline().parameters.RunDate)", "type": "Expression"}, "FileName": {"value": "@concat('280_AP_PortfolioGroup_',pipeline().parameters.RunDate,'_',formatDateTime(convertFromUtc(utcnow(),'Mountain Standard Time' ),'HHmmss'),'.csv')\n", "type": "Expression"}}}]}, {"name": "Generate Tax Rule Template", "type": "Copy", "dependsOn": [{"activity": "Generate Portfolio Group Tempalate", "dependencyConditions": ["Succeeded"]}], "policy": {"timeout": "7.00:00:00", "retry": 0, "retryIntervalInSeconds": 30, "secureOutput": false, "secureInput": false}, "userProperties": [], "typeProperties": {"source": {"type": "SqlMISource", "sqlReaderQuery": "SELECT  * FROM Billing.QFM.vw_AlliancePartnerTaxRule", "partitionOption": "None"}, "sink": {"type": "DelimitedTextSink", "storeSettings": {"type": "AzureBlobFSWriteSettings"}, "formatSettings": {"type": "DelimitedTextWriteSettings", "quoteAllText": true, "fileExtension": ""}}, "enableStaging": false, "translator": {"type": "TabularTranslator", "typeConversion": true, "typeConversionSettings": {"allowDataTruncation": true, "treatBooleanAsNumber": false}}}, "inputs": [{"referenceName": "SQLMI_MOW_PARAM", "type": "DatasetReference", "parameters": {"DBName": "Billing", "SchemaName": "QFM", "TableName": "vw_AlliancePartnerTaxRule"}}], "outputs": [{"referenceName": "ADL_Billing_Parameterized_v2", "type": "DatasetReference", "parameters": {"FilePath": {"value": "@concat('Billing/QFM/SendToQFM/MOW/',pipeline().parameters.RunDate)", "type": "Expression"}, "FileName": {"value": "@concat('296_AP_TaxRule_',pipeline().parameters.RunDate,'_',formatDateTime(convertFromUtc(utcnow(),'Mountain Standard Time' ),'HHmmss'),'.csv')\n", "type": "Expression"}}}]}], "parameters": {"RunDate": {"type": "string"}}, "folder": {"name": "Internal/Billing and QFM/Alliance Partner"}, "annotations": [], "lastPublishTime": "2024-07-19T22:51:14Z"}, "type": "Microsoft.DataFactory/factories/pipelines"}