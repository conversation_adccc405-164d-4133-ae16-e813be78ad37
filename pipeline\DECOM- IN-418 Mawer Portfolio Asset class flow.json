{"name": "DECOM- IN-418 Mawer Portfolio Asset class flow", "properties": {"activities": [{"name": "Get List from SharePoint", "type": "Copy", "dependsOn": [], "policy": {"timeout": "7.00:00:00", "retry": 0, "retryIntervalInSeconds": 30, "secureOutput": false, "secureInput": false}, "userProperties": [], "typeProperties": {"source": {"type": "RestSource", "httpRequestTimeout": "00:01:40", "requestInterval": "00.00:00:00.010", "requestMethod": "GET"}, "sink": {"type": "DelimitedTextSink", "storeSettings": {"type": "AzureBlobFSWriteSettings"}, "formatSettings": {"type": "DelimitedTextWriteSettings", "quoteAllText": true, "fileExtension": ".txt"}}, "enableStaging": false}, "inputs": [{"referenceName": "WatsonResearchAPI_IHSMarkit_sharepoint", "type": "DatasetReference"}], "outputs": [{"referenceName": "AzureDataLakeStorage_MawerIHSmarkitsink_csv", "type": "DatasetReference", "parameters": {"FileName": "test.csv"}}]}, {"name": "Web1", "type": "WebActivity", "dependsOn": [], "policy": {"timeout": "7.00:00:00", "retry": 0, "retryIntervalInSeconds": 30, "secureOutput": false, "secureInput": false}, "userProperties": [], "typeProperties": {"method": "GET", "disableCertValidation": true, "url": "http://*********/", "authentication": {"type": "Basic", "username": "svc_watsonresearch", "password": {"type": "AzureKeyVaultSecret", "store": {"referenceName": "AzureKey<PERSON>ault", "type": "LinkedServiceReference"}, "secretName": "WatsonResearchSecret"}}}}], "folder": {"name": "Internal/DDI/IN-329 DDI Migration/IN-384 Mawer IHSMarkit - TransactionCostAnalysis - Workflow"}, "annotations": [], "lastPublishTime": "2023-12-01T04:12:15Z"}, "type": "Microsoft.DataFactory/factories/pipelines"}