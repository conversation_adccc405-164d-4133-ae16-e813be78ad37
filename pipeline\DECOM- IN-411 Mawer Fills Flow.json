{"name": "DECOM- IN-411 Mawer Fills Flow", "properties": {"activities": [{"name": "Set variable1", "type": "SetVariable", "dependsOn": [], "policy": {"secureOutput": false, "secureInput": false}, "userProperties": [], "typeProperties": {"variableName": "FileName", "value": {"value": "@concat('Mawer Fillls_',formatDateTime(convertFromUtc(adddays(utcnow(),-1),'Mountain Standard Time'),'MMddyy'),'.txt')", "type": "Expression"}}}, {"name": "Generate mawer Fills source file", "type": "Copy", "dependsOn": [{"activity": "Set variable1", "dependencyConditions": ["Succeeded"]}], "policy": {"timeout": "7.00:00:00", "retry": 0, "retryIntervalInSeconds": 30, "secureOutput": false, "secureInput": false}, "userProperties": [], "typeProperties": {"source": {"type": "SqlServerSource", "sqlReaderQuery": "Declare @startdate Datetime;\nset @Startdate=CAST(CURRENT_TIMESTAMP -1  AS DATE);\n\nSELECT        f.ORDER_ID AS orderid, f.PLACEMENT_ID AS placementid, f.CREATE_DATE AS fillcreatedate, f.FILL_DATE AS filldate, f.FILL_BROKER AS fillbroker, f.FILL_QTY AS fillqty, f.FILL_PRICE AS fillprice,\n                         f.FILL_AMT AS fillamount, f.FIX_EXEC_ID AS fixexecutionid, f.FIX_INCOMING_ID AS fixincomingid, f.FIX_PLACE_DATE AS fixplacedate, f.FIX_FILL_DATE AS fixfilldate, f.ALGO_STRATEGY AS algostrategy,\n                         o.TRADE_DATE AS tradedate\nFROM            [MWR-SRV-crSQL1A].[CRDMAINMAWIM].[dbo].[TS_FILL] AS f INNER JOIN\n                         [MWR-SRV-crSQL1A].[CRDMAINMAWIM].[dbo].[TS_ORDER] AS o ON f.ORDER_ID = o.ORDER_ID\nWHERE        (o.TRADE_DATE >= @startdate) AND (o.TRADE_DATE <= @startdate)", "queryTimeout": "02:00:00", "partitionOption": "None"}, "sink": {"type": "DelimitedTextSink", "storeSettings": {"type": "AzureBlobFSWriteSettings"}, "formatSettings": {"type": "DelimitedTextWriteSettings", "quoteAllText": true, "fileExtension": ".txt"}}, "enableStaging": false, "translator": {"type": "TabularTranslator", "typeConversion": true, "typeConversionSettings": {"allowDataTruncation": true, "treatBooleanAsNumber": false}}}, "inputs": [{"referenceName": "ODSExternalData_MawerIHSMarkit_sql", "type": "DatasetReference"}], "outputs": [{"referenceName": "AzureDataLakeStorage_MawerIHSmarkitsink_csv", "type": "DatasetReference", "parameters": {"FileName": {"value": "@variables('FileName')", "type": "Expression"}}}]}, {"name": "Archive", "type": "Copy", "dependsOn": [{"activity": "Generate mawer Fills source file", "dependencyConditions": ["Succeeded"]}], "policy": {"timeout": "7.00:00:00", "retry": 0, "retryIntervalInSeconds": 30, "secureOutput": false, "secureInput": false}, "userProperties": [], "typeProperties": {"source": {"type": "BinarySource", "storeSettings": {"type": "AzureBlobFSReadSettings", "recursive": false, "wildcardFolderPath": "DDI Migration/Mawer IHSMarkit - workflow/Outputfiles", "wildcardFileName": {"value": "@variables('FileName')", "type": "Expression"}, "deleteFilesAfterCompletion": true}, "formatSettings": {"type": "BinaryReadSettings"}}, "sink": {"type": "BinarySink", "storeSettings": {"type": "AzureBlobFSWriteSettings"}}, "enableStaging": false}, "inputs": [{"referenceName": "AzureDataLakeStorageMawerIHSMarkit_Binary_Source", "type": "DatasetReference"}], "outputs": [{"referenceName": "AzureDataLakeStorage_MawerIHSMarkit_Binary_Sink", "type": "DatasetReference"}]}], "variables": {"FileName": {"type": "String"}}, "folder": {"name": "Internal/DDI/IN-329 DDI Migration/IN-384 Mawer IHSMarkit - TransactionCostAnalysis - Workflow"}, "annotations": [], "lastPublishTime": "2023-12-01T04:11:29Z"}, "type": "Microsoft.DataFactory/factories/pipelines"}