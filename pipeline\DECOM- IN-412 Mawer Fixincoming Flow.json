{"name": "DECOM- IN-412 Mawer Fixincoming Flow", "properties": {"activities": [{"name": "Generate mawer FIxincoming source file", "type": "Copy", "dependsOn": [{"activity": "Set variable1", "dependencyConditions": ["Succeeded"]}], "policy": {"timeout": "7.00:00:00", "retry": 0, "retryIntervalInSeconds": 30, "secureOutput": false, "secureInput": false}, "userProperties": [], "typeProperties": {"source": {"type": "SqlServerSource", "sqlReaderQuery": "Declare @startdate Datetime;\nset @Startdate=CAST(CURRENT_TIMESTAMP -1 AS DATE);\n\nSELECT        o.ORDER_ID AS orderid, fih.FIX_INCOMING_ID AS fixincomingid, fih.RECV_TIME AS fixreceivetime, fih.LAST_UPD_DATE AS fixlastupdatedate, fih.FIX_MSG_TYPE AS fixmessagetype,\n                         fih.PRICE_TYPE AS fixpricetype, fih.FACTOR AS fixfactor, fih.USER_DEFINED AS fixuserdefined, fih.PLACE_ID AS fixplaceid, fih.MSGSEQNUM AS fixmessageseqnumber, fih.LEAVESQTY AS fixleavesqty,\n                         fih.CASHORDERQTY AS fixcashorderqty, fih.E<PERSON>ECTYPE AS fixexecutiontype, fih.AVGPX AS fixaverageprice, fih.SIDE AS fixside, fih.SYMBOL AS fixsymbol, fih.LASTPX AS fixlastprice,\n                         fih.LASTSHARES AS fixlastshares, fih.CUMQTY AS fixcumulativequantity, fih.ORDERQTY AS fixorderqty, fih.SENDER_SUB_ID AS fixsendersubid, fih.SENDER_ID AS fixsenderid,\n                         fih.TARGET_SUB_ID AS fixtargetsubid, fih.FIX_BROKER_ID AS fixbrokerid, fih.SENDING_TIME AS fixsendingtime, fih.FIX_SUPP_ID AS fixsuppid, fih.FIX_ORIG_ID AS fixorigid, fih.FIX_ID AS fixid,\n                         fih.STATUS AS fixstatus, fih.TM_SUPP_ID AS fixsuppid2, fih.FIX_ORD_STATUS AS fixorderstatus, fih.FIX_TRANS_TYPE AS fixtranstype, fih.LAST_MKT, fih.LAST_CAPACITY, fih.LAST_LIQUIDITY_IND,\n                         o.TRADE_DATE AS tradedate\nFROM            [MWR-SRV-crSQL1A].[CRDMAINMAWIM].[dbo].[TS_ORDER] AS o INNER JOIN\n                         [MWR-SRV-crSQL1A].[CRDMAINMAWIM].[dbo].[FIX_INCOMING_HIST] AS fih ON o.ORDER_ID = fih.TM_ORIG_ID\nWHERE        (o.TRADE_DATE >=@startdate) AND (o.TRADE_DATE <=@startdate)", "queryTimeout": "02:00:00", "partitionOption": "None"}, "sink": {"type": "DelimitedTextSink", "storeSettings": {"type": "AzureBlobFSWriteSettings"}, "formatSettings": {"type": "DelimitedTextWriteSettings", "quoteAllText": true, "fileExtension": ".txt"}}, "enableStaging": false, "translator": {"type": "TabularTranslator", "typeConversion": true, "typeConversionSettings": {"allowDataTruncation": true, "treatBooleanAsNumber": false}}}, "inputs": [{"referenceName": "ODSExternalData_MawerIHSMarkit_sql", "type": "DatasetReference"}], "outputs": [{"referenceName": "AzureDataLakeStorage_MawerIHSmarkitsink_csv", "type": "DatasetReference", "parameters": {"FileName": {"value": "@variables('FileName')", "type": "Expression"}}}]}, {"name": "Set variable1", "type": "SetVariable", "dependsOn": [], "policy": {"secureOutput": false, "secureInput": false}, "userProperties": [], "typeProperties": {"variableName": "FileName", "value": {"value": "@concat('Mawer fixincoming_',formatDateTime(convertFromUtc(adddays(utcnow(),-1),'Mountain Standard Time'),'MMddyy'),'.txt')", "type": "Expression"}}}, {"name": "Archive", "type": "Copy", "dependsOn": [{"activity": "Generate mawer FIxincoming source file", "dependencyConditions": ["Succeeded"]}], "policy": {"timeout": "7.00:00:00", "retry": 0, "retryIntervalInSeconds": 30, "secureOutput": false, "secureInput": false}, "userProperties": [], "typeProperties": {"source": {"type": "BinarySource", "storeSettings": {"type": "AzureBlobFSReadSettings", "recursive": false, "wildcardFolderPath": "DDI Migration/Mawer IHSMarkit - workflow/Outputfiles", "wildcardFileName": {"value": "@variables('FileName')", "type": "Expression"}, "deleteFilesAfterCompletion": true}, "formatSettings": {"type": "BinaryReadSettings"}}, "sink": {"type": "BinarySink", "storeSettings": {"type": "AzureBlobFSWriteSettings"}}, "enableStaging": false}, "inputs": [{"referenceName": "AzureDataLakeStorageMawerIHSMarkit_Binary_Source", "type": "DatasetReference"}], "outputs": [{"referenceName": "AzureDataLakeStorage_MawerIHSMarkit_Binary_Sink", "type": "DatasetReference"}]}], "variables": {"FileName": {"type": "String"}}, "folder": {"name": "Internal/DDI/IN-329 DDI Migration/IN-384 Mawer IHSMarkit - TransactionCostAnalysis - Workflow"}, "annotations": [], "lastPublishTime": "2023-12-01T04:11:17Z"}, "type": "Microsoft.DataFactory/factories/pipelines"}