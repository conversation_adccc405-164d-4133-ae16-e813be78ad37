{"name": "DECOM- IN-618 Power BI Reports - GRANDPARENT_WebHook", "properties": {"activities": [{"name": "Set Sink File Name", "type": "SetVariable", "dependsOn": [{"activity": "Set Sink File Path", "dependencyConditions": ["Succeeded"]}], "policy": {"secureOutput": false, "secureInput": false}, "userProperties": [], "typeProperties": {"variableName": "sinkFileName", "value": {"value": "@concat('PowerBIReport',formatDateTime(convertFromUtc(utcNow(),'Mountain Standard Time'),'yyyyMMddHHmmss'),'.pdf')", "type": "Expression"}}}, {"name": "Logic App for Copy PowerBI Reports To ADL", "type": "WebActivity", "dependsOn": [{"activity": "Set Sink File Name", "dependencyConditions": ["Succeeded"]}], "policy": {"timeout": "7.00:00:00", "retry": 0, "retryIntervalInSeconds": 30, "secureOutput": false, "secureInput": false}, "userProperties": [], "typeProperties": {"method": "POST", "url": {"value": "https://prod-164.westus.logic.azure.com:443/workflows/7eb7582d512e44c2904301ed16af4212/triggers/manual/paths/invoke?api-version=2016-10-01&sp=%2Ftriggers%2Fmanual%2Frun&sv=1.0&sig=DSpZoiLOm4D18NUBBkXUu4buLZOpNPgv4N_v3pjUOrM", "type": "Expression"}, "body": {"value": "{\n    \"ADLPath\":\"@{variables('sourceFilePath')}\",\n     \"ADLContainer\":\"@{variables('adlContainer')}\"\n}", "type": "Expression"}}}, {"name": "Function App for Merging PDF", "type": "WebActivity", "dependsOn": [{"activity": "Logic App for Copy PowerBI Reports To ADL", "dependencyConditions": ["Succeeded"]}], "policy": {"timeout": "7.00:00:00", "retry": 0, "retryIntervalInSeconds": 30, "secureOutput": false, "secureInput": false}, "userProperties": [], "typeProperties": {"method": "POST", "url": {"value": "https://az-dev-uw-fnc-powerbipdfmerger.azurewebsites.net/api/MergePDFFiles", "type": "Expression"}, "body": {"value": "{\n\"sourceFilePath\":\"@{variables('sourceFilePath')}\",\n\"sinkFileName\":\"@{variables('sinkFileName')}\",\n\"sinkFilePath\":\"@{variables('sinkFilePath')}\"\n}", "type": "Expression"}}}, {"name": "Logic App for Uploading PDF to Sharepoint", "type": "WebActivity", "dependsOn": [{"activity": "Function App for Merging PDF", "dependencyConditions": ["Succeeded"]}], "policy": {"timeout": "7.00:00:00", "retry": 0, "retryIntervalInSeconds": 30, "secureOutput": false, "secureInput": false}, "userProperties": [], "typeProperties": {"method": "POST", "url": {"value": "https://prod-43.westus.logic.azure.com:443/workflows/00ff3367af2342b79aa689fc0daa7a10/triggers/manual/paths/invoke?api-version=2016-10-01&sp=%2Ftriggers%2Fmanual%2Frun&sv=1.0&sig=BE2Bj_CHru2wksdqfSUFxT9f1Pr_fFze8jQ_-TnVOss", "type": "Expression"}, "body": {"value": "{\n\"ADLPath\":\"@{variables('sinkFilePath')}\",\n\"ADLFileName\":\"@{variables('sinkFileName')}\",\n \"ADLContainer\":\"@{variables('adlContainer')}\"\n}\n", "type": "Expression"}}}, {"name": "Set Source File Path", "type": "SetVariable", "dependsOn": [], "policy": {"secureOutput": false, "secureInput": false}, "userProperties": [], "typeProperties": {"variableName": "sourceFilePath", "value": {"value": "@concat('DDI Migration/PowerBI/IN/',formatDateTime(convertFromUtc(utcNow(),'Mountain Standard Time'),'yyyyMMdd'))", "type": "Expression"}}}, {"name": "Set Sink File Path", "type": "SetVariable", "dependsOn": [{"activity": "Set Source File Path", "dependencyConditions": ["Succeeded"]}], "policy": {"secureOutput": false, "secureInput": false}, "userProperties": [], "typeProperties": {"variableName": "sinkFilePath", "value": {"value": "@concat('DDI Migration/PowerBI/OUT/',formatDateTime(convertFromUtc(utcNow(),'Mountain Standard Time'),'yyyyMMdd'))", "type": "Expression"}}}], "variables": {"sourceFilePath": {"type": "String"}, "sinkFileName": {"type": "String"}, "sinkFilePath": {"type": "String"}, "adlContainer": {"type": "String", "defaultValue": "scdintegration"}}, "folder": {"name": "Internal/DDI/IN-329 DDI Migration/IN-618 Power BI Reports"}, "annotations": [], "lastPublishTime": "2023-12-01T04:09:39Z"}, "type": "Microsoft.DataFactory/factories/pipelines"}