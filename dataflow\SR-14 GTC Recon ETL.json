{"name": "SR-14 GTC Recon ETL", "properties": {"folder": {"name": "Inbound"}, "type": "MappingDataFlow", "typeProperties": {"sources": [{"dataset": {"referenceName": "ADL_GTC_IN_CSV", "type": "DatasetReference"}, "name": "GTCCSVSource"}], "sinks": [{"dataset": {"referenceName": "ADL_GTC_TEMP_CSV", "type": "DatasetReference"}, "name": "OutputToCSV"}], "transformations": [{"name": "SelectColumns"}, {"name": "MapColumns", "description": "Creates an explicit mapping for each drifted column"}, {"name": "FilterOutNoActualBalance"}], "script": "parameters{\n\tDate as string\n}\nsource(allowSchemaDrift: true,\n\tvalidateSchema: false,\n\tignoreNoFilesFound: false,\n\trowUrlColumn: 'FileName',\n\twildcardPaths:[(concat('Custodian Data/GTC CIT/IN/InvestmentBalance*.csv'))]) ~> GTCCSVSource\nMapColumns select(skipDuplicateMapInputs: true,\n\tskipDuplicateMapOutputs: true) ~> SelectColumns\nGTCCSVSource derive({Balance as of Date} = replace(toString(toDate(toString(byName('Balance as of Date')),'MM/dd/yyyy')),'-',''),\n\t\t{Account Number} = toString(byName('Account Number')),\n\t\t{Account Name} = toString(byName('Account Name')),\n\t\t{Fund Name} = toString(byName('Fund Name')),\n\t\t{Number of Units} = toString(byName('Number of Units')),\n\t\t{Unit Price} = toString(byName('Unit Price')),\n\t\t{Actual Balance} = toString(byName('Actual Balance')),\n\t\tFileName = replace(toString(byName('FileName')), '/Custodian%20Data/GTC%20CIT/', '')) ~> MapColumns\nSelectColumns filter(toString(byName('Actual Balance')) != '') ~> FilterOutNoActualBalance\nFilterOutNoActualBalance sink(allowSchemaDrift: true,\n\tvalidateSchema: false,\n\tpartitionFileNames:[('InvestmentBalanceTemp.csv'\r\n\r\n)],\n\tumask: 0022,\n\tpreCommands: [],\n\tpostCommands: [],\n\tskipDuplicateMapInputs: true,\n\tskipDuplicateMapOutputs: true,\n\tpartitionBy('hash', 1)) ~> OutputToCSV"}}}