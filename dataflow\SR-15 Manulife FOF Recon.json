{"name": "SR-15 Manulife FOF Recon", "properties": {"folder": {"name": "Inbound"}, "type": "MappingDataFlow", "typeProperties": {"sources": [{"dataset": {"referenceName": "ADL_Manulife_FOF_RECON_Excel", "type": "DatasetReference"}, "name": "SourceExcelFile"}], "sinks": [{"dataset": {"referenceName": "ADL_Manulife_FOF_RECON_CSV", "type": "DatasetReference"}, "name": "OutputToCSV"}], "transformations": [{"name": "SelectColumns"}, {"name": "AddSelectDerivedColumn"}, {"name": "FilterZeroSETTLEDUNITS"}], "script": "parameters{\n\tDate as string\n}\nsource(output(\n\t\tACCOUNT as string,\n\t\t{ACCOUNT NAME} as string,\n\t\tBAYCOM as string,\n\t\t{UNDERLYING FUND NAME} as string,\n\t\tFUND as string,\n\t\tCLASS as string,\n\t\t{SETTLED UNITS} as double,\n\t\t{UNSETTLED UNITS} as double,\n\t\t{TOTAL UNITS HELD} as double,\n\t\tN<PERSON> as double,\n\t\tDEFF as string,\n\t\t{Settled Market Value} as double,\n\t\t{UnSettled MarketValue} as double,\n\t\t{Total Market Value} as double\n\t),\n\tallowSchemaDrift: true,\n\tvalidateSchema: false,\n\tignoreNoFilesFound: false,\n\twildcardPaths:[(concat('Custodian Data/Manulife FOF Reconciliation/IN/*.xls'))]) ~> SourceExcelFile\nAddSelectDerivedColumn select(mapColumn(\n\t\tACCOUNT,\n\t\t{ACCOUNT NAME},\n\t\tBAYCOM,\n\t\t{UNDERLYING FUND NAME},\n\t\tFUND,\n\t\tCLASS,\n\t\t{SETTLED UNITS},\n\t\t{UNSETTLED UNITS},\n\t\t{TOTAL UNITS HELD},\n\t\tNAV,\n\t\tDEFF,\n\t\t{Settled Market Value},\n\t\t{UnSettled MarketValue},\n\t\t{Total Market Value},\n\t\t{Sec ID}\n\t),\n\tskipDuplicateMapInputs: true,\n\tskipDuplicateMapOutputs: true) ~> SelectColumns\nFilterZeroSETTLEDUNITS derive({Sec ID} = concat('EPL', FUND, CLASS),\n\t\tDEFF = replace(toString(byName('DEFF')),'-','')) ~> AddSelectDerivedColumn\nSourceExcelFile filter({SETTLED UNITS} != 0) ~> FilterZeroSETTLEDUNITS\nSelectColumns sink(allowSchemaDrift: true,\n\tvalidateSchema: false,\n\tpartitionFileNames:[(concat('Custodian Data/Manulife FOF Reconciliation/TEMP/ManulifeFOFRecon.csv'))],\n\tumask: 0022,\n\tpreCommands: [],\n\tpostCommands: [],\n\tskipDuplicateMapInputs: true,\n\tskipDuplicateMapOutputs: true,\n\tpartitionBy('hash', 1)) ~> OutputToCSV"}}}