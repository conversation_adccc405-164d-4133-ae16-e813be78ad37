{"name": "DECOM- IN-370 CoppClark Holiday Calendar - PARENT", "properties": {"description": "The pipeline moves the latest ExchangeSettlement_yyyymmdd.csv from <PERSON><PERSON>'s SFTP into ADL's IN, then OUT, then into SCD. The pipeline ends by triggering ADL's TEMP folder cleanup.", "activities": [{"name": "Copy latest file to IN", "type": "Copy", "dependsOn": [], "policy": {"timeout": "7.00:00:00", "retry": 0, "retryIntervalInSeconds": 30, "secureOutput": false, "secureInput": false}, "userProperties": [], "typeProperties": {"source": {"type": "DelimitedTextSource", "storeSettings": {"type": "SftpReadSettings", "recursive": false, "wildcardFileName": {"value": "@variables('latest_file_name')", "type": "Expression"}, "enablePartitionDiscovery": false}, "formatSettings": {"type": "DelimitedTextReadSettings"}}, "sink": {"type": "DelimitedTextSink", "storeSettings": {"type": "AzureBlobFSWriteSettings"}, "formatSettings": {"type": "DelimitedTextWriteSettings", "quoteAllText": true, "fileExtension": ".txt"}}, "enableStaging": false, "translator": {"type": "TabularTranslator", "typeConversion": true, "typeConversionSettings": {"allowDataTruncation": true, "treatBooleanAsNumber": false}}}, "inputs": [{"referenceName": "SFTP_CoppClark_Holiday_Calender", "type": "DatasetReference"}], "outputs": [{"referenceName": "AzureDataLakeStorage_CoppClark_File", "type": "DatasetReference", "parameters": {"dir_name": {"value": "IN/@{variables('run_date')}", "type": "Expression"}, "file_name": "@variables('latest_file_name')"}}]}, {"name": "Copy from IN to OUT", "type": "Copy", "dependsOn": [{"activity": "Copy latest file to IN", "dependencyConditions": ["Succeeded"]}], "policy": {"timeout": "7.00:00:00", "retry": 0, "retryIntervalInSeconds": 30, "secureOutput": false, "secureInput": false}, "userProperties": [], "typeProperties": {"source": {"type": "DelimitedTextSource", "storeSettings": {"type": "AzureBlobFSReadSettings", "recursive": true, "enablePartitionDiscovery": false}, "formatSettings": {"type": "DelimitedTextReadSettings"}}, "sink": {"type": "DelimitedTextSink", "storeSettings": {"type": "AzureBlobFSWriteSettings"}, "formatSettings": {"type": "DelimitedTextWriteSettings", "quoteAllText": true, "fileExtension": ".txt"}}, "enableStaging": false, "translator": {"type": "TabularTranslator", "typeConversion": true, "typeConversionSettings": {"allowDataTruncation": true, "treatBooleanAsNumber": false}}}, "inputs": [{"referenceName": "AzureDataLakeStorage_CoppClark_File", "type": "DatasetReference", "parameters": {"dir_name": "IN/@{variables('run_date')}", "file_name": "@variables('latest_file_name')"}}], "outputs": [{"referenceName": "AzureDataLakeStorage_CoppClark_File", "type": "DatasetReference", "parameters": {"dir_name": "OUT/@{variables('run_date')}", "file_name": "@variables('latest_file_name')"}}]}, {"name": "Cleanup", "type": "ExecutePipeline", "dependsOn": [{"activity": "Copy from OUT to SCD", "dependencyConditions": ["Succeeded"]}], "policy": {"secureInput": false}, "userProperties": [], "typeProperties": {"waitOnCompletion": true}}, {"name": "Copy from OUT to SCD", "type": "Copy", "dependsOn": [{"activity": "Copy from IN to OUT", "dependencyConditions": ["Succeeded"]}], "policy": {"timeout": "7.00:00:00", "retry": 0, "retryIntervalInSeconds": 30, "secureOutput": false, "secureInput": false}, "userProperties": [], "typeProperties": {"source": {"type": "DelimitedTextSource", "storeSettings": {"type": "AzureBlobFSReadSettings", "recursive": true, "enablePartitionDiscovery": false}, "formatSettings": {"type": "DelimitedTextReadSettings"}}, "sink": {"type": "DelimitedTextSink", "storeSettings": {"type": "SftpWriteSettings", "operationTimeout": "01:00:00", "useTempFileRename": true}, "formatSettings": {"type": "DelimitedTextWriteSettings", "quoteAllText": true, "fileExtension": ".txt"}}, "enableStaging": false}, "inputs": [{"referenceName": "AzureDataLakeStorage_CoppClark_File", "type": "DatasetReference", "parameters": {"dir_name": "OUT/@{variables('run_date')}", "file_name": "@variables('latest_file_name')"}}], "outputs": [{"referenceName": "SFTP_SCD_Parameterized_Directory_CSV", "type": "DatasetReference", "parameters": {"FilePath": {"value": "@concat(pipeline().globalParameters.SCD_ENV, \n'/ActiveData/Interfaces/In/SFTP/CoppClarke/Working' )\n\n\n", "type": "Expression"}, "FileName": "@variables('latest_file_name')"}}]}, {"name": "ARCHIVE IN Folder", "type": "Copy", "dependsOn": [{"activity": "Copy from IN to OUT", "dependencyConditions": ["Succeeded"]}], "policy": {"timeout": "7.00:00:00", "retry": 0, "retryIntervalInSeconds": 30, "secureOutput": false, "secureInput": false}, "userProperties": [], "typeProperties": {"source": {"type": "DelimitedTextSource", "storeSettings": {"type": "AzureBlobFSReadSettings", "recursive": true, "enablePartitionDiscovery": false}, "formatSettings": {"type": "DelimitedTextReadSettings"}}, "sink": {"type": "DelimitedTextSink", "storeSettings": {"type": "AzureBlobFSWriteSettings"}, "formatSettings": {"type": "DelimitedTextWriteSettings", "quoteAllText": true, "fileExtension": ".txt"}}, "enableStaging": false, "translator": {"type": "TabularTranslator", "typeConversion": true, "typeConversionSettings": {"allowDataTruncation": true, "treatBooleanAsNumber": false}}}, "inputs": [{"referenceName": "AzureDataLakeStorage_CoppClark_File", "type": "DatasetReference", "parameters": {"dir_name": "IN/@{variables('run_date')}", "file_name": "@variables('latest_file_name')"}}], "outputs": [{"referenceName": "AzureDataLakeStorage_CoppClark_File", "type": "DatasetReference", "parameters": {"dir_name": "ARCHIVE/IN/@{variables('run_date')}", "file_name": "@variables('latest_file_name')"}}]}, {"name": "ARCHIVE OUT Folder", "type": "Copy", "dependsOn": [{"activity": "Copy from IN to OUT", "dependencyConditions": ["Succeeded"]}], "policy": {"timeout": "7.00:00:00", "retry": 0, "retryIntervalInSeconds": 30, "secureOutput": false, "secureInput": false}, "userProperties": [], "typeProperties": {"source": {"type": "DelimitedTextSource", "storeSettings": {"type": "AzureBlobFSReadSettings", "recursive": true, "enablePartitionDiscovery": false}, "formatSettings": {"type": "DelimitedTextReadSettings"}}, "sink": {"type": "DelimitedTextSink", "storeSettings": {"type": "AzureBlobFSWriteSettings"}, "formatSettings": {"type": "DelimitedTextWriteSettings", "quoteAllText": true, "fileExtension": ".txt"}}, "enableStaging": false, "translator": {"type": "TabularTranslator", "typeConversion": true, "typeConversionSettings": {"allowDataTruncation": true, "treatBooleanAsNumber": false}}}, "inputs": [{"referenceName": "AzureDataLakeStorage_CoppClark_File", "type": "DatasetReference", "parameters": {"dir_name": "OUT/@{variables('run_date')}", "file_name": "@variables('latest_file_name')"}}], "outputs": [{"referenceName": "AzureDataLakeStorage_CoppClark_File", "type": "DatasetReference", "parameters": {"dir_name": "ARCHIVE/OUT/@{variables('run_date')}", "file_name": "@variables('latest_file_name')"}}]}], "parameters": {"latest_file_name": {"type": "string"}}, "variables": {"latest_file_name": {"type": "String", "defaultValue": "@pipeline().parameters.latest_file_name"}, "run_date": {"type": "String", "defaultValue": "@formatDatetime(utcnow(),'yyyy-MM-dd')"}}, "folder": {"name": "Internal/Copp Clark Holiday Calendar"}, "annotations": [], "lastPublishTime": "2023-12-01T04:12:15Z"}, "type": "Microsoft.DataFactory/factories/pipelines"}