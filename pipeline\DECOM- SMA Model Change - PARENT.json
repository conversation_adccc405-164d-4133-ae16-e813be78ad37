{"name": "DECOM- SMA Model Change - PARENT", "properties": {"activities": [{"name": "Model Change files to Excel", "type": "WebActivity", "dependsOn": [{"activity": "Clean up SMA in MI", "dependencyConditions": ["Succeeded"]}], "policy": {"timeout": "7.00:00:00", "retry": 0, "retryIntervalInSeconds": 30, "secureOutput": false, "secureInput": false}, "userProperties": [], "typeProperties": {"method": "POST", "httpRequestTimeout": "00:09:00", "url": {"value": "https://az-dev-uw-fnc-smamodelchange.azurewebsites.net/api/SMAModelChange?code=NttwXmyOLzrERX9NO-jkff7k2_s3_zD4xwbcCWFf76u4AzFuKktTug==", "type": "Expression"}, "body": {"value": "@concat('{\"inputFileName\":\"',\n\nvariables('FileName'),\n\n'\",\n\"archiveOutContainer\" : \"SMA/OUT\",\n\"templateContainer\":\"SMA\"\n,\"inputContainer\":\"SMA/IN\",\n\"rootContainer\":\"scdintegration\"}')", "type": "Expression"}}}, {"name": "Get List of Output Files", "type": "GetMetadata", "dependsOn": [{"activity": "Model Change files to Excel", "dependencyConditions": ["Succeeded"]}], "policy": {"timeout": "7.00:00:00", "retry": 0, "retryIntervalInSeconds": 30, "secureOutput": false, "secureInput": false}, "userProperties": [], "typeProperties": {"dataset": {"referenceName": "ADL_Binary_Dir", "type": "DatasetReference", "parameters": {"Path": {"value": "@concat('SMA/OUT/')", "type": "Expression"}}}, "fieldList": ["childItems"], "storeSettings": {"type": "AzureBlobFSReadSettings", "recursive": true, "enablePartitionDiscovery": false}, "formatSettings": {"type": "BinaryReadSettings"}}}, {"name": "Loop Thru Output Files", "type": "ForEach", "dependsOn": [{"activity": "Filter Non TD Files", "dependencyConditions": ["Succeeded"]}], "userProperties": [], "typeProperties": {"items": {"value": "@activity('Filter Non TD Files').output.Value", "type": "Expression"}, "isSequential": true, "activities": [{"name": "Set Output Path with FileName", "type": "SetVariable", "dependsOn": [], "policy": {"secureOutput": false, "secureInput": false}, "userProperties": [], "typeProperties": {"variableName": "OutputFileName", "value": {"value": "@concat('scdintegration/SMA/OUT/',item().name)\n\n", "type": "Expression"}}}, {"name": "Set Alliance Partner Name", "type": "SetVariable", "dependsOn": [{"activity": "Set Output Path with FileName", "dependencyConditions": ["Succeeded"]}], "policy": {"secureOutput": false, "secureInput": false}, "userProperties": [], "typeProperties": {"variableName": "AlliancePartner", "value": {"value": "@substring(item().name,0, add(indexOf(item().name,'.xlsx'),-16))", "type": "Expression"}}}, {"name": "Move File from OUT to Archive OUT", "type": "Copy", "dependsOn": [{"activity": "Send Only on Model Change", "dependencyConditions": ["Succeeded"]}], "policy": {"timeout": "7.00:00:00", "retry": 0, "retryIntervalInSeconds": 30, "secureOutput": false, "secureInput": false}, "userProperties": [], "typeProperties": {"source": {"type": "BinarySource", "storeSettings": {"type": "AzureBlobFSReadSettings", "recursive": true, "wildcardFileName": {"value": "@concat(item().name)", "type": "Expression"}, "deleteFilesAfterCompletion": true}, "formatSettings": {"type": "BinaryReadSettings"}}, "sink": {"type": "BinarySink", "storeSettings": {"type": "AzureBlobFSWriteSettings"}}, "enableStaging": false}, "inputs": [{"referenceName": "ADL_Binary_Dir", "type": "DatasetReference", "parameters": {"Path": "SMA/OUT"}}], "outputs": [{"referenceName": "ADL_Binary_Dir", "type": "DatasetReference", "parameters": {"Path": {"value": "@concat('SMA/ARCHIVE/OUT/',formatDateTime(\nutcnow()\n,'yyyyMMdd'))", "type": "Expression"}}}]}, {"name": "GetEmailList", "type": "Lookup", "dependsOn": [{"activity": "Set Alliance Partner Name", "dependencyConditions": ["Succeeded"]}], "policy": {"timeout": "0.12:00:00", "retry": 0, "retryIntervalInSeconds": 30, "secureOutput": false, "secureInput": false}, "userProperties": [], "typeProperties": {"source": {"type": "SqlMISource", "sqlReaderQuery": {"value": "SELECT  e.[AlliancePartnerShortName]\n      ,[AlliancePartnerFullName]\n      ,[Email]\n      ,[HTMLBody]\n  FROM [Integration].[dbo].[SMAEmailDetails] e\nINNER JOIN \n\t(\n\tSELECT DISTINCT \n\t\tLEFT(PortfolioName, CHARINDEX(' -', PortfolioName) - 1) AS AlliancePartnerShortName \n\tFROM\n\t\tdbo.SMAModelChanges\n\tWHERE 1=1\n\t\tAND ActiveModelChange = '1'\n\t\tAND NOT (SecurityName LIKE '%bank account%')\n\t\t) s\n\n\tON s.AlliancePartnerShortName = e.AlliancePartnerShortName\n  WHERE 1=1\n  AND  e.AlliancePartnerShortName = '@{variables('AlliancePartner')}'\n  AND e.isPaused = 0", "type": "Expression"}, "partitionOption": "None"}, "dataset": {"referenceName": "SQLMI_PRD", "type": "DatasetReference", "parameters": {"DBName": "Integration", "Schema": "dbo", "Table": "SMAEmailDetails"}}}}, {"name": "Send Only on Model Change", "type": "IfCondition", "dependsOn": [{"activity": "GetEmailList", "dependencyConditions": ["Succeeded"]}], "userProperties": [], "typeProperties": {"expression": {"value": "@bool(contains(activity('GetEmailList').output, 'firstRow'))\n", "type": "Expression"}, "ifTrueActivities": [{"name": "Send Email with Attachement", "type": "WebActivity", "dependsOn": [], "policy": {"timeout": "7.00:00:00", "retry": 0, "retryIntervalInSeconds": 30, "secureOutput": false, "secureInput": false}, "userProperties": [], "typeProperties": {"method": "POST", "url": "https://prod-181.westus.logic.azure.com:443/workflows/37ec24fd02f54823af11e60dba4a1feb/triggers/manual/paths/invoke?api-version=2016-10-01&sp=%2Ftriggers%2Fmanual%2Frun&sv=1.0&sig=0234K-IZhjGlr4Ho2PkysG9EHxmR3-QUe9CCr45BrcY", "body": {"value": "{\"ADL_File_With_Attachments\": \"@{variables('OutputFileName')}\",\n\"Subject\": \"@{activity('GetEmailList').output.firstRow.AlliancePartnerFullName} - Model Change Recommendation\",\n\"Email_Body\":\"<body><p>Hi,\n    <br>\n    Please see attached model for updated target weights.\n    <br>\n    @{activity('GetEmailList').output.firstRow.HTMLBody}</p>\n    <br>\n    <hr>\n    Please advise once the orders have been completed.\n    <p>Thank you,</p>\n    <p><strong><PERSON></strong><br>\n    Analyst, Investment Operations<br>\n    t: +1 416 640 8024<br>\n    e: <a href=\\\"mailto:<EMAIL>\\\"><EMAIL></a><br>\n    w: <a href=\\\"https://mawer.com\\\">mawer.com</a></p>\n    <p><strong><PERSON><PERSON></strong><br>\n    Trade Management Analyst, Investment Operations<br>\n    t: +1 403 267 1972<br>\n    e: <a href=\\\"mailto:h<PERSON><PERSON>@mawer.com\\\">h<PERSON><EMAIL></a><br>\n    w: <a href=\\\"https://mawer.com\\\">mawer.com</a></p>\n    <p><strong>Katerina Pervykh</strong><br>\n    Trade Support Analyst<br>\n    t: +1 403 775 5790<br>\n    e: <a href=\\\"mailto:<EMAIL>\\\"><EMAIL></a><br>\n    w: <a href=\\\"https://mawer.com\\\">mawer.com</a></p>\n    <br>\n  </body>\",\n\"Attachment_Name\":\"@{concat(activity('GetEmailList').output.firstRow.AlliancePartnerFullName,'-',formatDateTime(utcnow(),'MMddyyyy'),'.xlsx')}\",\n\"Email_Recipients\":\"@{activity('GetEmailList').output.firstRow.Email}\"}", "type": "Expression"}}}]}}]}}, {"name": "From SCD SFTP to ADL IN", "type": "Copy", "dependsOn": [{"activity": "Set FileName Variable", "dependencyConditions": ["Succeeded"]}], "policy": {"timeout": "7.00:00:00", "retry": 0, "retryIntervalInSeconds": 30, "secureOutput": false, "secureInput": false}, "userProperties": [], "typeProperties": {"source": {"type": "DelimitedTextSource", "storeSettings": {"type": "SftpReadSettings", "recursive": true, "enablePartitionDiscovery": false, "disableChunking": false}, "formatSettings": {"type": "DelimitedTextReadSettings"}}, "sink": {"type": "DelimitedTextSink", "storeSettings": {"type": "AzureBlobFSWriteSettings"}, "formatSettings": {"type": "DelimitedTextWriteSettings", "quoteAllText": true, "fileExtension": ".txt"}}, "enableStaging": false, "translator": {"type": "TabularTranslator", "typeConversion": true, "typeConversionSettings": {"allowDataTruncation": true, "treatBooleanAsNumber": false}}}, "inputs": [{"referenceName": "SFTP_SCD_PRD_Parameterized", "type": "DatasetReference", "parameters": {"FilePath": {"value": "@concat('mawerp1','/ActiveData/Interfaces/Out/SFTP/Front Office/Outgoing')", "type": "Expression"}, "FileName": {"value": "@variables('FileName')", "type": "Expression"}}}], "outputs": [{"referenceName": "AzureDataLake_SMA_DIR_FILE", "type": "DatasetReference", "parameters": {"Path": "SMA/IN", "FileName": {"value": "@variables('FileName')", "type": "Expression"}}}]}, {"name": "Find File in SCD SFTP", "type": "GetMetadata", "dependsOn": [], "policy": {"timeout": "7.00:00:00", "retry": 0, "retryIntervalInSeconds": 30, "secureOutput": false, "secureInput": false}, "userProperties": [], "typeProperties": {"dataset": {"referenceName": "SFTP_SCD_PRD_Parameterized_2", "type": "DatasetReference", "parameters": {"FilePath": {"value": "@concat('mawerp1','/ActiveData/Interfaces/Out/SFTP/Front Office/Outgoing')", "type": "Expression"}}}, "fieldList": ["childItems"], "storeSettings": {"type": "SftpReadSettings", "recursive": true, "modifiedDatetimeStart": {"value": "@getPastTime(1,'Hour' )", "type": "Expression"}, "modifiedDatetimeEnd": {"value": "@utcnow()", "type": "Expression"}, "enablePartitionDiscovery": false, "disableChunking": false}, "formatSettings": {"type": "DelimitedTextReadSettings"}}}, {"name": "Filter Current File", "type": "Filter", "dependsOn": [{"activity": "Find File in SCD SFTP", "dependencyConditions": ["Succeeded"]}], "userProperties": [], "typeProperties": {"items": {"value": "@activity('Find File in SCD SFTP').output.childItems", "type": "Expression"}, "condition": {"value": "@and(contains(item().name, 'SMA_Model_Changes'),contains(item().name, formatDateTime(\nconvertFromUtc(utcnow(), 'Mountain Standard Time')\n,'ddMMyy')))", "type": "Expression"}}}, {"name": "Set FileName Variable", "type": "SetVariable", "dependsOn": [{"activity": "Set Index to Get Lastest File", "dependencyConditions": ["Succeeded"]}], "policy": {"secureOutput": false, "secureInput": false}, "userProperties": [], "typeProperties": {"variableName": "FileName", "value": {"value": "@activity('Filter Current File').output.Value[add(int(variables('FileCountIndex')),-1)].name", "type": "Expression"}}}, {"name": "Set Index to Get Lastest File", "type": "SetVariable", "dependsOn": [{"activity": "Filter Current File", "dependencyConditions": ["Succeeded"]}], "policy": {"secureOutput": false, "secureInput": false}, "userProperties": [], "typeProperties": {"variableName": "FileCountIndex", "value": {"value": "@string(activity('Filter Current File').output.FilteredItemsCount)", "type": "Expression"}}}, {"name": "Move File from IN to Archive IN", "type": "Copy", "dependsOn": [{"activity": "Loop Thru Output Files", "dependencyConditions": ["Succeeded"]}], "policy": {"timeout": "7.00:00:00", "retry": 0, "retryIntervalInSeconds": 30, "secureOutput": false, "secureInput": false}, "userProperties": [], "typeProperties": {"source": {"type": "BinarySource", "storeSettings": {"type": "AzureBlobFSReadSettings", "recursive": true, "wildcardFileName": {"value": "@variables('FileName')", "type": "Expression"}, "deleteFilesAfterCompletion": true}, "formatSettings": {"type": "BinaryReadSettings"}}, "sink": {"type": "BinarySink", "storeSettings": {"type": "AzureBlobFSWriteSettings"}}, "enableStaging": false}, "inputs": [{"referenceName": "ADL_Binary_Dir", "type": "DatasetReference", "parameters": {"Path": "SMA/IN"}}], "outputs": [{"referenceName": "ADL_Binary_Dir", "type": "DatasetReference", "parameters": {"Path": {"value": "@concat('SMA/ARCHIVE/IN/',formatDateTime(\nutcnow()\n,'yyyyMMdd'))", "type": "Expression"}}}]}, {"name": "Filter Non TD Files", "type": "Filter", "dependsOn": [{"activity": "Get List of Output Files", "dependencyConditions": ["Succeeded"]}], "userProperties": [], "typeProperties": {"items": {"value": "@activity('Get List of Output Files').output.childItems", "type": "Expression"}, "condition": {"value": "@not(contains(item().name,'TD'))", "type": "Expression"}}}, {"name": "Filter <PERSON>", "type": "Filter", "dependsOn": [{"activity": "Get List of Output Files", "dependencyConditions": ["Succeeded"]}], "userProperties": [], "typeProperties": {"items": {"value": "@activity('Get List of Output Files').output.childItems", "type": "Expression"}, "condition": {"value": "@contains(item().name,'TD')", "type": "Expression"}}}, {"name": "Create TD Zip File", "type": "Copy", "dependsOn": [{"activity": "Filter <PERSON>", "dependencyConditions": ["Succeeded"]}], "policy": {"timeout": "0.12:00:00", "retry": 0, "retryIntervalInSeconds": 30, "secureOutput": false, "secureInput": false}, "userProperties": [], "typeProperties": {"source": {"type": "BinarySource", "storeSettings": {"type": "AzureBlobFSReadSettings", "recursive": true, "wildcardFolderPath": "SMA/OUT", "wildcardFileName": "TD*", "deleteFilesAfterCompletion": false}, "formatSettings": {"type": "BinaryReadSettings"}}, "sink": {"type": "BinarySink", "storeSettings": {"type": "AzureBlobFSWriteSettings"}}, "enableStaging": false}, "inputs": [{"referenceName": "ADL_Binary_Dir", "type": "DatasetReference", "parameters": {"Path": "SMA/OUT"}}], "outputs": [{"referenceName": "ADL_Binary_File_ZIP", "type": "DatasetReference", "parameters": {"FileName": "TD Model Change.zip", "Path": "SMA/OUT", "Container": "scdintegration"}}]}, {"name": "Stage Raw File to MI", "type": "Copy", "dependsOn": [{"activity": "From SCD SFTP to ADL IN", "dependencyConditions": ["Succeeded"]}], "policy": {"timeout": "0.12:00:00", "retry": 0, "retryIntervalInSeconds": 30, "secureOutput": false, "secureInput": false}, "userProperties": [], "typeProperties": {"source": {"type": "DelimitedTextSource", "additionalColumns": [{"name": "LoadedTs", "value": {"value": "@utcNow()", "type": "Expression"}}, {"name": "LoadedBy", "value": {"value": "@pipeline().Pipeline", "type": "Expression"}}], "storeSettings": {"type": "AzureBlobFSReadSettings", "recursive": true, "enablePartitionDiscovery": false}, "formatSettings": {"type": "DelimitedTextReadSettings"}}, "sink": {"type": "SqlMISink", "preCopyScript": "TRUNCATE TABLE Integration.dbo.SMAModelChanges", "writeBehavior": "insert", "sqlWriterUseTableLock": false}, "enableStaging": false, "translator": {"type": "TabularTranslator", "mappings": [{"source": {"type": "String", "ordinal": 1}, "sink": {"name": "Portfolio", "type": "String", "physicalType": "n<PERSON><PERSON><PERSON>"}}, {"source": {"type": "String", "ordinal": 2}, "sink": {"name": "PortfolioName", "type": "String", "physicalType": "n<PERSON><PERSON><PERSON>"}}, {"source": {"type": "String", "ordinal": 3}, "sink": {"name": "SecurityName", "type": "String", "physicalType": "n<PERSON><PERSON><PERSON>"}}, {"source": {"type": "String", "ordinal": 4}, "sink": {"name": "CUSIP", "type": "String", "physicalType": "n<PERSON><PERSON><PERSON>"}}, {"source": {"type": "String", "ordinal": 5}, "sink": {"name": "SecurityID", "type": "String", "physicalType": "n<PERSON><PERSON><PERSON>"}}, {"source": {"type": "String", "ordinal": 6}, "sink": {"name": "Exchange", "type": "String", "physicalType": "n<PERSON><PERSON><PERSON>"}}, {"source": {"type": "String", "ordinal": 7}, "sink": {"name": "PriorDayPrice", "type": "Double", "physicalType": "float"}}, {"source": {"type": "String", "ordinal": 8}, "sink": {"name": "SecurityCurrency", "type": "String", "physicalType": "n<PERSON><PERSON><PERSON>"}}, {"source": {"type": "String", "ordinal": 9}, "sink": {"name": "OldWeight", "type": "Double", "physicalType": "float"}}, {"source": {"type": "String", "ordinal": 10}, "sink": {"name": "NewWeight", "type": "Double", "physicalType": "float"}}, {"source": {"type": "String", "ordinal": 11}, "sink": {"name": "WeightChange", "type": "Double", "physicalType": "float"}}, {"source": {"type": "String", "ordinal": 12}, "sink": {"name": "Action", "type": "String", "physicalType": "n<PERSON><PERSON><PERSON>"}}, {"source": {"type": "String", "ordinal": 13}, "sink": {"name": "ActiveModelChange", "type": "String", "physicalType": "n<PERSON><PERSON><PERSON>"}}, {"source": {"name": "LoadedTs", "type": "String"}, "sink": {"name": "LoadedTs", "type": "DateTime", "physicalType": "datetime"}}, {"source": {"name": "LoadedBy", "type": "String"}, "sink": {"name": "LoadedBy", "type": "String", "physicalType": "n<PERSON><PERSON><PERSON>"}}], "typeConversion": true, "typeConversionSettings": {"allowDataTruncation": true, "treatBooleanAsNumber": false}}}, "inputs": [{"referenceName": "AzureDataLake_SMA_DIR_FILE_2", "type": "DatasetReference", "parameters": {"Path": "SMA/IN", "FileName": {"value": "@variables('FileName')", "type": "Expression"}}}], "outputs": [{"referenceName": "SQLMI_RESEARCH_PARAM", "type": "DatasetReference", "parameters": {"DBName": "Integration", "SchemaName": "dbo", "TableName": "SMAModelChanges"}}]}, {"name": "Clean up SMA in MI", "type": "<PERSON><PERSON><PERSON>", "dependsOn": [{"activity": "Stage Raw File to MI", "dependencyConditions": ["Succeeded"]}], "policy": {"timeout": "0.12:00:00", "retry": 0, "retryIntervalInSeconds": 30, "secureOutput": false, "secureInput": false}, "userProperties": [], "linkedServiceName": {"referenceName": "SQLMI_RESEARCH_PRD", "type": "LinkedServiceReference", "parameters": {"DBName": "Integration"}}, "typeProperties": {"scripts": [{"type": "Query", "text": "drop table if exists #t\nselect *\ninto #t\nfrom dbo.SMAModelChanges\n\ntruncate table dbo.SMAModelChanges\ninsert into dbo.SMAModelChanges\nselect  replace(Portfolio,'\"','') as Portfolio, \ncase when PortfolioName like '%Aviso%' then 'Aviso - '+ PortfolioName else PortfolioName end as PortfolioName, SecurityName, CUSIP, SecurityID, Exchange, PriorDayPrice, SecurityCurrency\n,OldWeight, NewWeight, WeightChange, Action, replace(ActiveModelChange,'\"','') as ActiveModelChange,LoadedTs, LoadedBy\nfrom #t"}], "scriptBlockExecutionTimeout": "02:00:00"}}, {"name": "GetEmailList_TD", "type": "Lookup", "dependsOn": [{"activity": "Create TD Zip File", "dependencyConditions": ["Succeeded"]}], "policy": {"timeout": "0.12:00:00", "retry": 0, "retryIntervalInSeconds": 30, "secureOutput": false, "secureInput": false}, "userProperties": [], "typeProperties": {"source": {"type": "SqlMISource", "sqlReaderQuery": {"value": "SELECT  e.[AlliancePartnerShortName]\n      ,[AlliancePartnerFullName]\n      ,[Email]\n      ,[HTMLBody]\n  FROM [Integration].[dbo].[SMAEmailDetails] e\nINNER JOIN \n\t(\n\tSELECT DISTINCT \n\t\tLEFT(PortfolioName, CHARINDEX(' -', PortfolioName) - 1) AS AlliancePartnerShortName \n\tFROM\n\t\tdbo.SMAModelChanges\n\tWHERE 1=1\n\t\tAND ActiveModelChange = '1'\n\t\tAND NOT (SecurityName LIKE '%bank account%')\n\t\t) s\n\n\tON s.AlliancePartnerShortName = e.AlliancePartnerShortName\n  WHERE e.AlliancePartnerShortName = 'TD'", "type": "Expression"}, "partitionOption": "None"}, "dataset": {"referenceName": "SQLMI_PRD", "type": "DatasetReference", "parameters": {"DBName": "Integration", "Schema": "dbo", "Table": "SMAEmailDetails"}}}}, {"name": "TD - Send Only on Model Change", "type": "IfCondition", "dependsOn": [{"activity": "GetEmailList_TD", "dependencyConditions": ["Succeeded"]}], "userProperties": [], "typeProperties": {"expression": {"value": "@bool(contains(activity('GetEmailList_TD').output, 'firstRow'))\n", "type": "Expression"}, "ifTrueActivities": [{"name": "TD - Send Email with Attachement_copy1", "type": "WebActivity", "dependsOn": [], "policy": {"timeout": "7.00:00:00", "retry": 0, "retryIntervalInSeconds": 30, "secureOutput": false, "secureInput": false}, "userProperties": [], "typeProperties": {"method": "POST", "url": "https://prod-181.westus.logic.azure.com:443/workflows/37ec24fd02f54823af11e60dba4a1feb/triggers/manual/paths/invoke?api-version=2016-10-01&sp=%2Ftriggers%2Fmanual%2Frun&sv=1.0&sig=0234K-IZhjGlr4Ho2PkysG9EHxmR3-QUe9CCr45BrcY", "body": {"value": "{\n    \"ADL_File_With_Attachments\":\"scdintegration/SMA/OUT/TD Model Change.zip\",\n    \"Subject\":\"TD - Model Change Recommendation\",\n    \"Email_Body\":\"<body><p>Hi,\n    <br>\n    Please see attached model for updated target weights.\n    <br>\n    Please note that all blue highlighted securities are buys and all pink highlighted securities are sells.</p>\n    <br>\n    <hr>\n    Please advise once the orders have been completed.\n    <p>Thank you,</p>\n    <p><strong><PERSON></strong><br>\n    Analyst, Investment Operations<br>\n    t: +1 416 640 8024<br>\n    e: <a href=\\\"mailto:<EMAIL>\\\"><EMAIL></a><br>\n    w: <a href=\\\"https://mawer.com\\\">mawer.com</a></p>\n    <p><strong><PERSON><PERSON></strong><br>\n    Trade Management Analyst, Investment Operations<br>\n    t: +1 403 267 1972<br>\n    e: <a href=\\\"mailto:h<PERSON><PERSON>@mawer.com\\\">h<PERSON><PERSON>@mawer.com</a><br>\n    w: <a href=\\\"https://mawer.com\\\">mawer.com</a></p>\n    <p><strong><PERSON><PERSON></strong><br>\n    Trade Support Analyst<br>\n    t: +1 403 775 5790<br>\n    e: <a href=\\\"mailto:<EMAIL>\\\"><EMAIL></a><br>\n    w: <a href=\\\"https://mawer.com\\\">mawer.com</a></p>\n    <br>\n  </body>\",\n    \"Attachment_Name\":\"TDReports.zip\",\n    \"Email_Recipients\":\"@{activity('GetEmailList_TD').output.firstRow.Email}\"\n}", "type": "Expression"}}}, {"name": "Move File from OUT to Archive OUT_copy1", "type": "Copy", "dependsOn": [{"activity": "TD - Send Email with Attachement_copy1", "dependencyConditions": ["Succeeded"]}], "policy": {"timeout": "7.00:00:00", "retry": 0, "retryIntervalInSeconds": 30, "secureOutput": false, "secureInput": false}, "userProperties": [], "typeProperties": {"source": {"type": "BinarySource", "storeSettings": {"type": "AzureBlobFSReadSettings", "recursive": true, "wildcardFolderPath": "SMA/OUT", "wildcardFileName": "TD*", "deleteFilesAfterCompletion": true}, "formatSettings": {"type": "BinaryReadSettings"}}, "sink": {"type": "BinarySink", "storeSettings": {"type": "AzureBlobFSWriteSettings"}}, "enableStaging": false}, "inputs": [{"referenceName": "ADL_Binary_Dir", "type": "DatasetReference", "parameters": {"Path": "SMA/OUT"}}], "outputs": [{"referenceName": "ADL_Binary_Dir", "type": "DatasetReference", "parameters": {"Path": {"value": "@concat('SMA/ARCHIVE/OUT/',formatDateTime(\nutcnow()\n,'yyyyMMdd'))", "type": "Expression"}}}]}]}}], "variables": {"FileName": {"type": "String"}, "ToEmail": {"type": "String"}, "OutputFileName": {"type": "String"}, "AlliancePartner": {"type": "String"}, "FileCountIndex": {"type": "String"}, "PipelineRunStatus": {"type": "String"}}, "folder": {"name": "Internal/SMA/SMA Automation"}, "annotations": [], "lastPublishTime": "2024-10-04T18:48:44Z"}, "type": "Microsoft.DataFactory/factories/pipelines"}