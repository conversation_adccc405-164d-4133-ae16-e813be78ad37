{"name": "SQLMI_DEV", "properties": {"linkedServiceName": {"referenceName": "AzureSQLManagedInstance", "type": "LinkedServiceReference", "parameters": {"dbName": "Billing"}}, "parameters": {"Schema": {"type": "string"}, "TableName": {"type": "string"}}, "folder": {"name": "General"}, "annotations": [], "type": "AzureSqlMITable", "schema": [], "typeProperties": {"schema": {"value": "@dataset().Schema", "type": "Expression"}, "table": {"value": "@dataset().TableName", "type": "Expression"}}}, "type": "Microsoft.DataFactory/factories/datasets"}