{"name": "IN-166 StateStreet FX - PARENT", "properties": {"activities": [{"name": "Unzip FX File", "type": "Copy", "dependsOn": [{"activity": "Set FX Output File Name", "dependencyConditions": ["Succeeded"]}], "policy": {"timeout": "7.00:00:00", "retry": 0, "retryIntervalInSeconds": 30, "secureOutput": false, "secureInput": false}, "userProperties": [], "typeProperties": {"source": {"type": "BinarySource", "storeSettings": {"type": "AzureBlobFSReadSettings", "recursive": true}, "formatSettings": {"type": "BinaryReadSettings", "compressionProperties": {"type": "ZipDeflateReadSettings"}}}, "sink": {"type": "BinarySink", "storeSettings": {"type": "AzureBlobFSWriteSettings"}}, "enableStaging": false}, "inputs": [{"referenceName": "AzureDataLakeStorage_StateStreet_FX_Zipped", "type": "DatasetReference", "parameters": {"FileName": "attachment.zip"}}], "outputs": [{"referenceName": "ADL_StateStreet_FX_Unzipped", "type": "DatasetReference", "parameters": {"FileName": {"value": "@variables('InputFileName')", "type": "Expression"}}}]}, {"name": "Set FX Output File Name", "type": "SetVariable", "dependsOn": [{"activity": "Set Input File Name", "dependencyConditions": ["Succeeded"]}], "policy": {"secureOutput": false, "secureInput": false}, "userProperties": [], "typeProperties": {"variableName": "CSVOutFileName", "value": {"value": "@concat('StateStreet_FX_',pipeline().parameters.Date,'.csv')", "type": "Expression"}}}, {"name": "Execute ETL", "type": "ExecutePipeline", "dependsOn": [{"activity": "Unzip FX File", "dependencyConditions": ["Succeeded"]}], "policy": {"secureInput": false}, "userProperties": [], "typeProperties": {"pipeline": {"referenceName": "IN-166 StateStreet FX - CHILD", "type": "PipelineReference"}, "waitOnCompletion": true, "parameters": {"inputFXFileName": {"value": "@variables('InputFileName')", "type": "Expression"}, "CSVOutFileName": {"value": "@variables('CSVOutFileName')", "type": "Expression"}, "Date": {"value": "@replace(pipeline().parameters.Date,'-','')", "type": "Expression"}}}}, {"name": "Set Input File Name", "type": "SetVariable", "dependsOn": [{"activity": "File Download and Validation", "dependencyConditions": ["Succeeded"]}], "policy": {"secureOutput": false, "secureInput": false}, "userProperties": [], "typeProperties": {"variableName": "InputFileName", "value": {"value": "@concat('StateStreet_FX_',replace(pipeline().parameters.Date,'-',''),'000000.csv')", "type": "Expression"}}}, {"name": "File Download and Validation", "type": "ExecutePipeline", "dependsOn": [], "policy": {"secureInput": false}, "userProperties": [], "typeProperties": {"pipeline": {"referenceName": "GNRL03_DOWNLOAD_EMAIL_ATTACH_VALIDATION", "type": "PipelineReference"}, "waitOnCompletion": true, "parameters": {"FilePath": "FX/Statestreet/IN", "PipelineName": {"value": "@pipeline().Pipeline", "type": "Expression"}, "WebBODY": {"value": "@concat('{\"FileLocation\":\"FX/Statestreet/IN/\",\"SubjectFilterString\":\"FW: FX Activity Automation Ext Mailbox Test: Completed Successfully\",\"ReceivedDate\":\"', pipeline().parameters.Date, '\"}')", "type": "Expression"}, "WaitForNextTryInSec": 900}}}], "parameters": {"Date": {"type": "string"}}, "variables": {"CSVOutFileName": {"type": "String"}, "InputFileName": {"type": "String"}}, "folder": {"name": "State Street/SSCA FX"}, "annotations": [], "lastPublishTime": "2023-12-11T22:28:54Z"}, "type": "Microsoft.DataFactory/factories/pipelines"}