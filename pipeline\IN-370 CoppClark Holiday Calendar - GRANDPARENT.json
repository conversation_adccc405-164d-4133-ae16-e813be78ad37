{"name": "IN-370 CoppClark Holiday Calendar - GRANDPARENT", "properties": {"description": "The pipeline reads <PERSON><PERSON>'s Holiday Calendar SFTP and filters for the latest ExchangeSettlement_yyyymmdd.csv file. If that file is newer than the current one, the next pipeline is triggered, which copies it.", "activities": [{"name": "Filter for ExchangeSettlement", "type": "Filter", "dependsOn": [{"activity": "<PERSON><PERSON> metadata", "dependencyConditions": ["Succeeded"]}], "userProperties": [], "typeProperties": {"items": {"value": "@activity('Read Copp Clark metadata').output.childitems", "type": "Expression"}, "condition": {"value": "@startswith(item().name, 'ExchangeSettlement')", "type": "Expression"}}}, {"name": "Find latest file name", "type": "ForEach", "dependsOn": [{"activity": "Filter for ExchangeSettlement", "dependencyConditions": ["Succeeded"]}], "userProperties": [], "typeProperties": {"items": {"value": "@activity('Filter for ExchangeSettlement').output.Value", "type": "Expression"}, "isSequential": true, "activities": [{"name": "If file name greater than current latest", "type": "IfCondition", "dependsOn": [], "userProperties": [], "typeProperties": {"expression": {"value": "@greater(item().name, variables('latest_file_name'))", "type": "Expression"}, "ifTrueActivities": [{"name": "Update latest file name", "type": "SetVariable", "dependsOn": [], "policy": {"secureOutput": false, "secureInput": false}, "userProperties": [], "typeProperties": {"variableName": "latest_file_name", "value": {"value": "@item().name", "type": "Expression"}}}]}}]}}, {"name": "<PERSON><PERSON> metadata", "type": "GetMetadata", "dependsOn": [], "policy": {"timeout": "7.00:00:00", "retry": 0, "retryIntervalInSeconds": 30, "secureOutput": false, "secureInput": false}, "userProperties": [], "typeProperties": {"dataset": {"referenceName": "SFTP_CoppClark_Holiday_Calender", "type": "DatasetReference"}, "fieldList": ["childItems"], "storeSettings": {"type": "SftpReadSettings", "recursive": true}, "formatSettings": {"type": "DelimitedTextReadSettings"}}}, {"name": "Get previous latest file name", "type": "GetMetadata", "dependsOn": [{"activity": "Find latest file name", "dependencyConditions": ["Succeeded"]}], "policy": {"timeout": "7.00:00:00", "retry": 0, "retryIntervalInSeconds": 30, "secureOutput": false, "secureInput": false}, "userProperties": [], "typeProperties": {"dataset": {"referenceName": "AzureDataLakeStorage_CoppClark_Dir", "type": "DatasetReference", "parameters": {"dir_name": "UTIL"}}, "fieldList": ["childItems"], "storeSettings": {"type": "AzureBlobFSReadSettings", "recursive": true, "enablePartitionDiscovery": false}, "formatSettings": {"type": "DelimitedTextReadSettings"}}}, {"name": "If new file available", "type": "IfCondition", "dependsOn": [{"activity": "Set previous latest file name", "dependencyConditions": ["Succeeded"]}], "userProperties": [], "typeProperties": {"expression": {"value": "@greater(\n    variables('latest_file_name'),\n    variables('prev_latest_file_name')\n)", "type": "Expression"}, "ifTrueActivities": [{"name": "Copy latest file", "type": "ExecutePipeline", "dependsOn": [], "policy": {"secureInput": false}, "userProperties": [], "typeProperties": {"pipeline": {"referenceName": "DECOM- IN-370 CoppClark Holiday Calendar - PARENT", "type": "PipelineReference"}, "waitOnCompletion": true, "parameters": {"latest_file_name": {"value": "@variables('latest_file_name')", "type": "Expression"}}}}, {"name": "Update previous latest file name", "type": "Copy", "dependsOn": [{"activity": "Copy latest file", "dependencyConditions": ["Succeeded"]}], "policy": {"timeout": "7.00:00:00", "retry": 0, "retryIntervalInSeconds": 30, "secureOutput": false, "secureInput": false}, "userProperties": [], "typeProperties": {"source": {"type": "DelimitedTextSource", "storeSettings": {"type": "AzureBlobFSReadSettings", "recursive": true, "enablePartitionDiscovery": false}, "formatSettings": {"type": "DelimitedTextReadSettings"}}, "sink": {"type": "DelimitedTextSink", "storeSettings": {"type": "AzureBlobFSWriteSettings"}, "formatSettings": {"type": "DelimitedTextWriteSettings", "quoteAllText": true, "fileExtension": ".txt"}}, "enableStaging": false, "translator": {"type": "TabularTranslator", "typeConversion": true, "typeConversionSettings": {"allowDataTruncation": true, "treatBooleanAsNumber": false}}}, "inputs": [{"referenceName": "AzureDataLakeStorage_CoppClark_File", "type": "DatasetReference", "parameters": {"dir_name": "UTIL", "file_name": "@variables('prev_latest_file_name')"}}], "outputs": [{"referenceName": "AzureDataLakeStorage_CoppClark_File", "type": "DatasetReference", "parameters": {"dir_name": "UTIL", "file_name": "@variables('latest_file_name')"}}]}, {"name": "Delete previous latest file name", "type": "Delete", "dependsOn": [{"activity": "Update previous latest file name", "dependencyConditions": ["Succeeded"]}], "policy": {"timeout": "7.00:00:00", "retry": 0, "retryIntervalInSeconds": 30, "secureOutput": false, "secureInput": false}, "userProperties": [], "typeProperties": {"dataset": {"referenceName": "AzureDataLakeStorage_CoppClark_File", "type": "DatasetReference", "parameters": {"dir_name": "UTIL", "file_name": "@variables('prev_latest_file_name')"}}, "enableLogging": false, "storeSettings": {"type": "AzureBlobFSReadSettings", "recursive": true, "enablePartitionDiscovery": false}}}]}}, {"name": "Set previous latest file name", "type": "SetVariable", "dependsOn": [{"activity": "Get previous latest file name", "dependencyConditions": ["Succeeded"]}], "policy": {"secureOutput": false, "secureInput": false}, "userProperties": [], "typeProperties": {"variableName": "prev_latest_file_name", "value": {"value": "@first(\n    activity('Get previous latest file name').output.childitems\n).name", "type": "Expression"}}}], "variables": {"latest_file_name": {"type": "String"}, "prev_latest_file_name": {"type": "String"}}, "folder": {"name": "Internal/Copp Clark Holiday Calendar"}, "annotations": [], "lastPublishTime": "2023-12-01T04:12:42Z"}, "type": "Microsoft.DataFactory/factories/pipelines"}