{"name": "WatsonResearchAPI_IHSMarkit_sharepoint", "properties": {"linkedServiceName": {"referenceName": "WatsonResearchAPI", "type": "LinkedServiceReference"}, "folder": {"name": "Inbound/DDI/IN-329 DDI Migration/IN-384 Mawer IHSMarkit - TransactionCostAnalysis - Workflow"}, "annotations": [], "type": "RestResource", "typeProperties": {"relativeUrl": "_vti_bin/listdata.svc/PortfolioAssetClass"}, "schema": []}, "type": "Microsoft.DataFactory/factories/datasets"}