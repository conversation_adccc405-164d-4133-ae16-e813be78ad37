# Azure Data Factory Pipeline Exclusion Script
# This script filters out pipelines marked for DEV-only deployment

param(
    [Parameter(Mandatory=$true)]
    [string]$SourcePath,
    
    [Parameter(Mandatory=$true)]
    [string]$TargetPath,
    
    [Parameter(Mandatory=$false)]
    [string[]]$ExclusionAnnotations = @("DEV_ONLY", "EXCLUDE_FROM_PROD"),
    
    [Parameter(Mandatory=$false)]
    [switch]$WhatIf
)

Write-Host "Starting ADF Pipeline Exclusion Process..." -ForegroundColor Green
Write-Host "Source Path: $SourcePath" -ForegroundColor Yellow
Write-Host "Target Path: $TargetPath" -ForegroundColor Yellow
Write-Host "Exclusion Annotations: $($ExclusionAnnotations -join ', ')" -ForegroundColor Yellow

# Ensure target directory exists
if (-not (Test-Path $TargetPath)) {
    New-Item -ItemType Directory -Path $TargetPath -Force | Out-Null
    Write-Host "Created target directory: $TargetPath" -ForegroundColor Green
}

# Get all pipeline files
$pipelineFiles = Get-ChildItem -Path "$SourcePath/pipeline" -Filter "*.json" -ErrorAction SilentlyContinue

if (-not $pipelineFiles) {
    Write-Warning "No pipeline files found in $SourcePath/pipeline"
    return
}

$excludedPipelines = @()
$includedPipelines = @()

foreach ($file in $pipelineFiles) {
    try {
        $content = Get-Content $file.FullName -Raw | ConvertFrom-Json
        $shouldExclude = $false
        
        # Check if pipeline has exclusion annotations
        if ($content.properties.annotations) {
            foreach ($annotation in $content.properties.annotations) {
                if ($annotation -in $ExclusionAnnotations) {
                    $shouldExclude = $true
                    break
                }
            }
        }
        
        if ($shouldExclude) {
            $excludedPipelines += $file.Name
            Write-Host "EXCLUDED: $($file.Name)" -ForegroundColor Red
        } else {
            $includedPipelines += $file.Name
            
            if (-not $WhatIf) {
                # Copy pipeline to target
                $targetFile = Join-Path $TargetPath "pipeline" $file.Name
                $targetDir = Split-Path $targetFile -Parent
                
                if (-not (Test-Path $targetDir)) {
                    New-Item -ItemType Directory -Path $targetDir -Force | Out-Null
                }
                
                Copy-Item $file.FullName $targetFile -Force
            }
            
            Write-Host "INCLUDED: $($file.Name)" -ForegroundColor Green
        }
    }
    catch {
        Write-Error "Error processing file $($file.Name): $($_.Exception.Message)"
    }
}

# Handle triggers separately to check for pipeline dependencies
$triggerFiles = Get-ChildItem -Path "$SourcePath/trigger" -Filter "*.json" -ErrorAction SilentlyContinue
$excludedTriggers = @()
$includedTriggers = @()

if ($triggerFiles) {
    foreach ($file in $triggerFiles) {
        try {
            $content = Get-Content $file.FullName -Raw | ConvertFrom-Json
            $shouldExclude = $false

            # Check if trigger has exclusion annotations
            if ($content.properties.annotations) {
                foreach ($annotation in $content.properties.annotations) {
                    if ($annotation -in $ExclusionAnnotations) {
                        $shouldExclude = $true
                        break
                    }
                }
            }

            # Check if trigger references excluded pipelines
            if (-not $shouldExclude -and $content.properties.pipelines) {
                foreach ($pipeline in $content.properties.pipelines) {
                    $pipelineName = $pipeline.pipelineReference.referenceName
                    if ("$pipelineName.json" -in $excludedPipelines) {
                        $shouldExclude = $true
                        Write-Host "TRIGGER EXCLUDED due to pipeline dependency: $($file.Name) -> $pipelineName" -ForegroundColor Yellow
                        break
                    }
                }
            }

            if ($shouldExclude) {
                $excludedTriggers += $file.Name
                Write-Host "EXCLUDED TRIGGER: $($file.Name)" -ForegroundColor Red
            } else {
                $includedTriggers += $file.Name

                if (-not $WhatIf) {
                    $targetFile = Join-Path $TargetPath "trigger" $file.Name
                    $targetDir = Split-Path $targetFile -Parent

                    if (-not (Test-Path $targetDir)) {
                        New-Item -ItemType Directory -Path $targetDir -Force | Out-Null
                    }

                    Copy-Item $file.FullName $targetFile -Force
                }

                Write-Host "INCLUDED TRIGGER: $($file.Name)" -ForegroundColor Green
            }
        }
        catch {
            Write-Error "Error processing trigger file $($file.Name): $($_.Exception.Message)"
        }
    }
}

# Copy other ADF artifacts (datasets, linkedServices, etc.)
$otherArtifacts = @("dataset", "linkedService", "dataflow", "factory", "integrationRuntime")

foreach ($artifactType in $otherArtifacts) {
    $sourcePath = Join-Path $SourcePath $artifactType
    if (Test-Path $sourcePath) {
        $targetArtifactPath = Join-Path $TargetPath $artifactType

        if (-not $WhatIf) {
            if (-not (Test-Path $targetArtifactPath)) {
                New-Item -ItemType Directory -Path $targetArtifactPath -Force | Out-Null
            }

            Copy-Item "$sourcePath/*" $targetArtifactPath -Recurse -Force
        }

        $fileCount = (Get-ChildItem $sourcePath -Recurse -File).Count
        Write-Host "COPIED: $fileCount $artifactType files" -ForegroundColor Cyan
    }
}

# Copy ARM template parameter definition
$armTemplateFile = Join-Path $SourcePath "arm-template-parameters-definition.json"
if (Test-Path $armTemplateFile) {
    if (-not $WhatIf) {
        Copy-Item $armTemplateFile (Join-Path $TargetPath "arm-template-parameters-definition.json") -Force
    }
    Write-Host "COPIED: ARM template parameters definition" -ForegroundColor Cyan
}

# Generate summary report
Write-Host "`n=== EXCLUSION SUMMARY ===" -ForegroundColor Magenta
Write-Host "Total Pipelines Processed: $($pipelineFiles.Count)" -ForegroundColor White
Write-Host "Pipelines Included: $($includedPipelines.Count)" -ForegroundColor Green
Write-Host "Pipelines Excluded: $($excludedPipelines.Count)" -ForegroundColor Red

if ($triggerFiles) {
    Write-Host "Total Triggers Processed: $($triggerFiles.Count)" -ForegroundColor White
    Write-Host "Triggers Included: $($includedTriggers.Count)" -ForegroundColor Green
    Write-Host "Triggers Excluded: $($excludedTriggers.Count)" -ForegroundColor Red
}

if ($excludedPipelines.Count -gt 0) {
    Write-Host "`nExcluded Pipelines:" -ForegroundColor Red
    $excludedPipelines | ForEach-Object { Write-Host "  - $_" -ForegroundColor Red }
}

if ($excludedTriggers.Count -gt 0) {
    Write-Host "`nExcluded Triggers:" -ForegroundColor Red
    $excludedTriggers | ForEach-Object { Write-Host "  - $_" -ForegroundColor Red }
}

# Create exclusion report file
if (-not $WhatIf) {
    $reportPath = Join-Path $TargetPath "exclusion-report.json"
    $report = @{
        timestamp = Get-Date -Format "yyyy-MM-ddTHH:mm:ssZ"
        exclusionAnnotations = $ExclusionAnnotations
        summary = @{
            totalPipelines = $pipelineFiles.Count
            includedPipelines = $includedPipelines.Count
            excludedPipelines = $excludedPipelines.Count
            totalTriggers = if ($triggerFiles) { $triggerFiles.Count } else { 0 }
            includedTriggers = $includedTriggers.Count
            excludedTriggers = $excludedTriggers.Count
        }
        excludedPipelines = $excludedPipelines
        excludedTriggers = $excludedTriggers
        includedPipelines = $includedPipelines
        includedTriggers = $includedTriggers
    }

    $report | ConvertTo-Json -Depth 3 | Out-File $reportPath -Encoding UTF8
    Write-Host "`nExclusion report saved to: $reportPath" -ForegroundColor Cyan
}

if ($WhatIf) {
    Write-Host "`n[WHAT-IF MODE] No files were actually copied." -ForegroundColor Yellow
}

Write-Host "`nPipeline exclusion process completed!" -ForegroundColor Green
