{"name": "DECOM- Clients CSV Template - SF to SCD", "properties": {"description": "Integrate Client data from Salesforce to SCD\nhttps://beboringmakedata.atlassian.net/browse/IN-383\n", "activities": [{"name": "Client Info", "type": "Switch", "dependsOn": [{"activity": "Set File Name", "dependencyConditions": ["Succeeded"]}], "userProperties": [], "typeProperties": {"on": {"value": "@pipeline().parameters.ObjectName", "type": "Expression"}, "cases": [{"value": "Account", "activities": [{"name": "Copy Account data", "type": "Copy", "dependsOn": [], "policy": {"timeout": "7.00:00:00", "retry": 0, "retryIntervalInSeconds": 30, "secureOutput": false, "secureInput": false}, "userProperties": [], "typeProperties": {"source": {"type": "SqlMISource", "sqlReaderQuery": {"value": "@concat(\n' SELECT [PortfolioCode] ,[Portfolio Client Type (Portfolio Free Code 25)] ,[Name] ,[First Name] ,REPLACE(REPLACE(Street, CHAR(13), '' ''), CHAR(10), '' '') as [Street] ,[City] ,[Post Code] ,[Country] ,[Region] ,[E-mail] ,[Phone] ,[National security No.] ,[Blank78] ,[Client ID] ,[Client Group] ,[Title] ,REPLACE(REPLACE([Street (Alternative Address Shipping)], CHAR(13), '' ''), CHAR(10), '' '') as [Street (Alternative Address Shipping)] ,[City (Additional Addresses)] ,[Post code (Additional Addresses)] ,[Country (Additional Addresses)] ,[Region (Telefax (Additional Addresses))] ,[Free comment (Additional Addresses)] from [scd].[vClientsChange] WHERE AccountId in (',\n'',pipeline().parameters.ObjectId,')')", "type": "Expression"}, "partitionOption": "None"}, "sink": {"type": "DelimitedTextSink", "storeSettings": {"type": "AzureBlobFSWriteSettings"}, "formatSettings": {"type": "DelimitedTextWriteSettings", "quoteAllText": true, "fileExtension": ".csv"}}, "enableStaging": false}, "inputs": [{"referenceName": "SalesforceViews", "type": "DatasetReference"}], "outputs": [{"referenceName": "AzureDataLakeStorage_SalesforceToScd_PortfolioFile", "type": "DatasetReference", "parameters": {"FileName": {"value": "@variables('FileName')", "type": "Expression"}, "FolderName": {"value": "@concat('Salesforce To SCD/Daily Loads/',formatDateTime(convertFromUtc(utcnow(),'Mountain Standard Time'),'yyyyMMdd'))", "type": "Expression"}}}]}]}, {"value": "Contact", "activities": [{"name": "Copy Contact data", "type": "Copy", "dependsOn": [], "policy": {"timeout": "7.00:00:00", "retry": 0, "retryIntervalInSeconds": 30, "secureOutput": false, "secureInput": false}, "userProperties": [], "typeProperties": {"source": {"type": "SqlMISource", "sqlReaderQuery": {"value": "@concat(\n' SELECT [PortfolioCode] ,[Portfolio Client Type (Portfolio Free Code 25)] ,[Name] ,[First Name] ,REPLACE(REPLACE(Street, CHAR(13), '' ''), CHAR(10), '' '') as [Street] ,[City] ,[Post Code] ,[Country] ,[Region] ,[E-mail] ,[Phone] ,[National security No.] ,[Blank78] ,[Client ID] ,[Client Group] ,[Title] ,REPLACE(REPLACE([Street (Alternative Address Shipping)], CHAR(13), '' ''), CHAR(10), '' '') as [Street (Alternative Address Shipping)] ,[City (Additional Addresses)] ,[Post code (Additional Addresses)] ,[Country (Additional Addresses)] ,[Region (Telefax (Additional Addresses))] ,[Free comment (Additional Addresses)] from [scd].[vClientsChange] WHERE ContactId in (',\n'',pipeline().parameters.ObjectId,')')", "type": "Expression"}, "partitionOption": "None"}, "sink": {"type": "DelimitedTextSink", "storeSettings": {"type": "AzureBlobFSWriteSettings"}, "formatSettings": {"type": "DelimitedTextWriteSettings", "quoteAllText": true, "fileExtension": ".csv"}}, "enableStaging": false}, "inputs": [{"referenceName": "SalesforceViews", "type": "DatasetReference"}], "outputs": [{"referenceName": "AzureDataLakeStorage_SalesforceToScd_PortfolioFile", "type": "DatasetReference", "parameters": {"FileName": {"value": "@variables('FileName')", "type": "Expression"}, "FolderName": {"value": "@concat('Salesforce To SCD/Daily Loads/',formatDateTime(convertFromUtc(utcnow(),'Mountain Standard Time'),'yyyyMMdd'))", "type": "Expression"}}}]}]}, {"value": "FinancialAccount", "activities": [{"name": "Copy FinancialAccount data", "type": "Copy", "dependsOn": [], "policy": {"timeout": "7.00:00:00", "retry": 0, "retryIntervalInSeconds": 30, "secureOutput": false, "secureInput": false}, "userProperties": [], "typeProperties": {"source": {"type": "SqlMISource", "sqlReaderQuery": {"value": "@concat(\n' SELECT [PortfolioCode] ,[Portfolio Client Type (Portfolio Free Code 25)] ,[Name] ,[First Name] ,REPLACE(REPLACE(Street, CHAR(13), '' ''), CHAR(10), '' '') as [Street] ,[City] ,[Post Code] ,[Country] ,[Region] ,[E-mail] ,[Phone] ,[National security No.] ,[Blank78] ,[Client ID] ,[Client Group] ,[Title] ,REPLACE(REPLACE([Street (Alternative Address Shipping)], CHAR(13), '' ''), CHAR(10), '' '') as [Street (Alternative Address Shipping)] ,[City (Additional Addresses)] ,[Post code (Additional Addresses)] ,[Country (Additional Addresses)] ,[Region (Telefax (Additional Addresses))] ,[Free comment (Additional Addresses)] from [scd].[vClientsChange] WHERE FinancialAccountId in (',\n'',pipeline().parameters.ObjectId,')')", "type": "Expression"}, "partitionOption": "None"}, "sink": {"type": "DelimitedTextSink", "storeSettings": {"type": "AzureBlobFSWriteSettings"}, "formatSettings": {"type": "DelimitedTextWriteSettings", "quoteAllText": true, "fileExtension": ".csv"}}, "enableStaging": false}, "inputs": [{"referenceName": "SalesforceViews", "type": "DatasetReference"}], "outputs": [{"referenceName": "AzureDataLakeStorage_SalesforceToScd_PortfolioFile", "type": "DatasetReference", "parameters": {"FileName": {"value": "@variables('FileName')", "type": "Expression"}, "FolderName": {"value": "@concat('Salesforce To SCD/Daily Loads/',formatDateTime(convertFromUtc(utcnow(),'Mountain Standard Time'),'yyyyMMdd'))", "type": "Expression"}}}]}]}]}}, {"name": "Set File Name", "type": "SetVariable", "dependsOn": [], "policy": {"secureOutput": false, "secureInput": false}, "userProperties": [], "typeProperties": {"variableName": "FileName", "value": {"value": "@concat('ClientInfo_',formatDateTime(convertFromUtc(utcnow(),'Mountain Standard Time'),'MMddyyyy'),'_',formatDateTime(convertFromUtc(utcnow(),'Mountain Standard Time'),'hhmmss'),'.csv')", "type": "Expression"}}}, {"name": "Copy File to SCD SFTP", "type": "Copy", "dependsOn": [{"activity": "Client Info", "dependencyConditions": ["Succeeded"]}], "policy": {"timeout": "7.00:00:00", "retry": 0, "retryIntervalInSeconds": 30, "secureOutput": false, "secureInput": false}, "userProperties": [], "typeProperties": {"source": {"type": "BinarySource", "storeSettings": {"type": "AzureBlobFSReadSettings", "recursive": true}, "formatSettings": {"type": "BinaryReadSettings"}}, "sink": {"type": "BinarySink", "storeSettings": {"type": "SftpWriteSettings", "operationTimeout": "01:00:00", "useTempFileRename": true}}, "enableStaging": false}, "inputs": [{"referenceName": "AzureDataLakeStorage_Portfolio_Binary", "type": "DatasetReference", "parameters": {"SourceDirectory": {"value": "@concat('Salesforce To SCD/Daily Loads/',formatDateTime(convertFromUtc(utcnow(),'Mountain Standard Time'),'yyyyMMdd'))", "type": "Expression"}, "FileName": {"value": "@variables('FileName')", "type": "Expression"}}}], "outputs": [{"referenceName": "SFTP_SCD_DIR_BINARY", "type": "DatasetReference", "parameters": {"Path": {"value": "@concat(pipeline().globalParameters.SCD_ENV,pipeline().globalParameters.SCD_SFTP_Portfolio)", "type": "Expression"}}}]}], "parameters": {"ObjectId": {"type": "string", "defaultValue": "'0015G00001nTbKiQAK'"}, "ObjectName": {"type": "string", "defaultValue": "Account"}}, "variables": {"FileName": {"type": "String"}}, "folder": {"name": "Internal/SFtoSCD/SF-SCD Real Time"}, "annotations": [], "lastPublishTime": "2023-12-01T04:11:49Z"}, "type": "Microsoft.DataFactory/factories/pipelines"}