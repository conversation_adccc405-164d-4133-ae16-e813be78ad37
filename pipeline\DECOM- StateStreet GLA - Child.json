{"name": "DECOM- StateStreet GLA - Child", "properties": {"activities": [{"name": "Set Input FileName", "type": "SetVariable", "dependsOn": [], "policy": {"secureOutput": false, "secureInput": false}, "userProperties": [], "typeProperties": {"variableName": "InputFileName", "value": {"value": "@pipeline().parameters.InputFileName", "type": "Expression"}}}, {"name": "Set RunDate Variable", "type": "SetVariable", "dependsOn": [], "policy": {"secureOutput": false, "secureInput": false}, "userProperties": [], "typeProperties": {"variableName": "RunDate", "value": {"value": "@formatDateTime(\nconvertFromUtc(utcnow(), 'Mountain Standard Time')\n,'yyyy-MM-dd hh:mm:ss')", "type": "Expression"}}}, {"name": "Update Log with File Found", "type": "Lookup", "dependsOn": [{"activity": "Set Input FileName", "dependencyConditions": ["Succeeded"]}, {"activity": "Set RunDate Variable", "dependencyConditions": ["Succeeded"]}], "policy": {"timeout": "7.00:00:00", "retry": 0, "retryIntervalInSeconds": 30, "secureOutput": false, "secureInput": false}, "userProperties": [], "typeProperties": {"source": {"type": "SqlMISource", "sqlReaderQuery": {"value": "@concat('INSERT INTO Billing.StateStreet.GLAFileLogs\n\nSELECT ''',variables('InputFileName'),\n''',''INTRADAY'', 1,''',variables('RunDate'),''',''Azure''', ' select 1 ')", "type": "Expression"}, "partitionOption": "None"}, "dataset": {"referenceName": "SQLMI_MOW_PARAM", "type": "DatasetReference", "parameters": {"DBName": "Billing", "SchemaName": "StateStreet", "TableName": "GLAFileLogs"}}}}, {"name": "Copy File to Data Lake", "type": "Copy", "dependsOn": [{"activity": "Update Log with File Found", "dependencyConditions": ["Succeeded"]}], "policy": {"timeout": "7.00:00:00", "retry": 0, "retryIntervalInSeconds": 30, "secureOutput": false, "secureInput": false}, "userProperties": [], "typeProperties": {"source": {"type": "DelimitedTextSource", "storeSettings": {"type": "AzureFileStorageReadSettings", "recursive": true, "wildcardFileName": {"value": "@variables('InputFileName')", "type": "Expression"}, "enablePartitionDiscovery": false}, "formatSettings": {"type": "DelimitedTextReadSettings", "skipLineCount": 1}}, "sink": {"type": "DelimitedTextSink", "storeSettings": {"type": "AzureBlobFSWriteSettings"}, "formatSettings": {"type": "DelimitedTextWriteSettings", "quoteAllText": true, "fileExtension": ""}}, "enableStaging": false, "translator": {"type": "TabularTranslator", "typeConversion": true, "typeConversionSettings": {"allowDataTruncation": true, "treatBooleanAsNumber": false}}}, "inputs": [{"referenceName": "FileShare_StateStreetBilling", "type": "DatasetReference"}], "outputs": [{"referenceName": "ADL_Billing_Parameterized", "type": "DatasetReference", "parameters": {"FilePath": {"value": "@concat('Billing/StateStreet/ARCHIVE/IN/')", "type": "Expression"}}}]}, {"name": "Copy File to SQL MI", "type": "Copy", "dependsOn": [{"activity": "Update Log with File Found", "dependencyConditions": ["Succeeded"]}], "policy": {"timeout": "7.00:00:00", "retry": 0, "retryIntervalInSeconds": 30, "secureOutput": false, "secureInput": false}, "userProperties": [], "typeProperties": {"source": {"type": "DelimitedTextSource", "additionalColumns": [{"name": "UpdatedDated", "value": {"value": "@utcNow()", "type": "Expression"}}, {"name": "UpdatedBy", "value": {"value": "@concat('ADL')", "type": "Expression"}}, {"name": "FileName", "value": {"value": "@variables('InputFileName')", "type": "Expression"}}], "storeSettings": {"type": "AzureFileStorageReadSettings", "recursive": true, "wildcardFileName": {"value": "@variables('InputFileName')", "type": "Expression"}, "enablePartitionDiscovery": false}, "formatSettings": {"type": "DelimitedTextReadSettings", "skipLineCount": 1}}, "sink": {"type": "SqlMISink", "writeBehavior": "insert", "sqlWriterUseTableLock": false}, "enableStaging": false, "translator": {"type": "TabularTranslator", "mappings": [{"source": {"type": "String", "ordinal": 1}, "sink": {"name": "TYPE", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}, {"source": {"type": "String", "ordinal": 2}, "sink": {"name": "FUND_ID", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}, {"source": {"type": "String", "ordinal": 3}, "sink": {"name": "CRNCY_CD_BASE", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}, {"source": {"type": "String", "ordinal": 4}, "sink": {"name": "CRNCY_CD_FIN", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}, {"source": {"type": "String", "ordinal": 5}, "sink": {"name": "CRNCY_CD_LOC", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}, {"source": {"type": "String", "ordinal": 6}, "sink": {"name": "POST_NUM", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}, {"source": {"type": "String", "ordinal": 7}, "sink": {"name": "POST_SEQ_NUM", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}, {"source": {"type": "String", "ordinal": 8}, "sink": {"name": "REAL_MEMO_IND", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}, {"source": {"type": "String", "ordinal": 9}, "sink": {"name": "ACCOUNT", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}, {"source": {"type": "String", "ordinal": 10}, "sink": {"name": "SUB_ACCOUNT", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}, {"source": {"type": "String", "ordinal": 11}, "sink": {"name": "GL_ACCT_DESC", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}, {"source": {"type": "String", "ordinal": 12}, "sink": {"name": "BASIS_IND", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}, {"source": {"type": "String", "ordinal": 13}, "sink": {"name": "POST_PERIOD_NUM", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}, {"source": {"type": "String", "ordinal": 14}, "sink": {"name": "POST_DT", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}, {"source": {"type": "String", "ordinal": 15}, "sink": {"name": "GL_REF_DT", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}, {"source": {"type": "String", "ordinal": 16}, "sink": {"name": "EFF_GL_DT", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}, {"source": {"type": "String", "ordinal": 17}, "sink": {"name": "DR_CR_IND", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}, {"source": {"type": "String", "ordinal": 18}, "sink": {"name": "POST_AMT", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}, {"source": {"type": "String", "ordinal": 19}, "sink": {"name": "ASSET_ID", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}, {"source": {"type": "String", "ordinal": 20}, "sink": {"name": "ISSUE_LONG_NM", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}, {"source": {"type": "String", "ordinal": 21}, "sink": {"name": "TRD_ID_NUM", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}, {"source": {"type": "String", "ordinal": 22}, "sink": {"name": "LOT_NUM", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}, {"source": {"type": "String", "ordinal": 23}, "sink": {"name": "POST_TYPE_CD", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}, {"source": {"type": "String", "ordinal": 24}, "sink": {"name": "GL_POST_DTL_DESC", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}, {"source": {"type": "String", "ordinal": 25}, "sink": {"name": "BAL_IND", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}, {"source": {"type": "String", "ordinal": 26}, "sink": {"name": "CAPSTK_SHRPAR_QTY", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}, {"source": {"type": "String", "ordinal": 27}, "sink": {"name": "ALT_ASSET_ID_TYPE_CD1", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}, {"source": {"type": "String", "ordinal": 28}, "sink": {"name": "ALT_ASSET_ID1", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}, {"source": {"type": "String", "ordinal": 29}, "sink": {"name": "ALT_ASSET_ID_TYPE_CD2", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}, {"source": {"type": "String", "ordinal": 30}, "sink": {"name": "ALT_ASSET_ID2", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}, {"source": {"type": "String", "ordinal": 31}, "sink": {"name": "FUND_NM", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}, {"source": {"name": "UpdatedDated", "type": "String"}, "sink": {"name": "UpdatedDate", "type": "DateTime", "physicalType": "datetime"}}, {"source": {"name": "UpdatedBy", "type": "String"}, "sink": {"name": "UpdatedBy", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}, {"source": {"name": "FileName", "type": "String"}, "sink": {"name": "FileName", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}], "typeConversion": true, "typeConversionSettings": {"allowDataTruncation": true, "treatBooleanAsNumber": false}}}, "inputs": [{"referenceName": "FileShare_StateStreetBilling", "type": "DatasetReference"}], "outputs": [{"referenceName": "SQLMI_MOW_PARAM", "type": "DatasetReference", "parameters": {"DBName": "Billing", "SchemaName": "StateStreet", "TableName": "AccruedFeesInput"}}]}, {"name": "sp_LoadAccruedFees", "type": "Copy", "dependsOn": [{"activity": "Copy File to SQL MI", "dependencyConditions": ["Succeeded"]}], "policy": {"timeout": "7.00:00:00", "retry": 0, "retryIntervalInSeconds": 30, "secureOutput": false, "secureInput": false}, "userProperties": [], "typeProperties": {"source": {"type": "SqlMISource", "sqlReaderQuery": "EXEC Billing.StateStreet.[sp_LoadAccruedFees]", "partitionOption": "None"}, "sink": {"type": "DelimitedTextSink", "storeSettings": {"type": "AzureBlobFSWriteSettings"}, "formatSettings": {"type": "DelimitedTextWriteSettings", "quoteAllText": true, "fileExtension": ".txt"}}, "enableStaging": false, "translator": {"type": "TabularTranslator", "typeConversion": true, "typeConversionSettings": {"allowDataTruncation": true, "treatBooleanAsNumber": false}}}, "inputs": [{"referenceName": "SQLMI_MOW_PARAM", "type": "DatasetReference", "parameters": {"DBName": "Billing", "SchemaName": "StateStreet", "TableName": "AccruedFees"}}], "outputs": [{"referenceName": "ADL_Billing_Parameterized_v3", "type": "DatasetReference", "parameters": {"FilePath": {"value": "@concat('Billing/StateStreet/ARCHIVE/OUT/',pipeline().parameters.Date)", "type": "Expression"}, "FileName": {"value": "@concat('SS_AccruedRevenue_',pipeline().parameters.Date,'.csv')", "type": "Expression"}}}]}, {"name": "Check If Missing Portfolio Grtr then 0", "type": "IfCondition", "dependsOn": [{"activity": "Check if All Portfolios are Included", "dependencyConditions": ["Succeeded"]}], "userProperties": [], "typeProperties": {"expression": {"value": "@greater(activity('Check if All Portfolios are Included').output.firstRow.MissingPortfolios,0 )\n", "type": "Expression"}, "ifFalseActivities": [{"name": "Send file to SCD", "type": "Copy", "dependsOn": [], "policy": {"timeout": "0.12:00:00", "retry": 0, "retryIntervalInSeconds": 30, "secureOutput": false, "secureInput": false}, "userProperties": [], "typeProperties": {"source": {"type": "DelimitedTextSource", "storeSettings": {"type": "AzureBlobFSReadSettings", "recursive": true, "enablePartitionDiscovery": false}, "formatSettings": {"type": "DelimitedTextReadSettings"}}, "sink": {"type": "DelimitedTextSink", "storeSettings": {"type": "SftpWriteSettings", "operationTimeout": "01:00:00", "useTempFileRename": false}, "formatSettings": {"type": "DelimitedTextWriteSettings", "quoteAllText": true, "fileExtension": ""}}, "enableStaging": false, "translator": {"type": "TabularTranslator", "typeConversion": true, "typeConversionSettings": {"allowDataTruncation": true, "treatBooleanAsNumber": false}}}, "inputs": [{"referenceName": "ADL_Billing_Parameterized_v3", "type": "DatasetReference", "parameters": {"FilePath": {"value": "@concat('Billing/StateStreet/ARCHIVE/OUT/',pipeline().parameters.Date)\n", "type": "Expression"}, "FileName": {"value": "@concat('SS_AccruedRevenue_',pipeline().parameters.Date,'.csv')", "type": "Expression"}}}], "outputs": [{"referenceName": "SFTP_SCD_SS_GLA_Output", "type": "DatasetReference", "parameters": {"FilePath": {"value": "@concat('mawerp1/ActiveData/Interfaces/In/SFTP/Fees/Working')", "type": "Expression"}}}]}], "ifTrueActivities": [{"name": "Clean Up SS ACCRUED FEES OUTPUT", "type": "Lookup", "dependsOn": [], "policy": {"timeout": "7.00:00:00", "retry": 0, "retryIntervalInSeconds": 30, "secureOutput": false, "secureInput": false}, "userProperties": [], "typeProperties": {"source": {"type": "SqlMISource", "sqlReaderQuery": "delete from Billing.StateStreet.AccruedFees\nwhere UpdatedDate = (select max(UpdatedDate) from Billing.StateStreet.AccruedFees)\n\nselect 1 ", "partitionOption": "None"}, "dataset": {"referenceName": "SQLMI_MOW_PARAM", "type": "DatasetReference", "parameters": {"DBName": "Billing", "SchemaName": "StateStreet", "TableName": "AccruedFees"}}}}, {"name": "Clean Up SS ACCRUED FEES", "type": "Lookup", "dependsOn": [], "policy": {"timeout": "7.00:00:00", "retry": 0, "retryIntervalInSeconds": 30, "secureOutput": false, "secureInput": false}, "userProperties": [], "typeProperties": {"source": {"type": "SqlMISource", "sqlReaderQuery": "delete from  Billing.StateStreet.AccruedFeesInput\nwhere UpdatedDate = \n(select max(UpdatedDate) from  Billing.StateStreet.AccruedFeesInput)\n\nselect 1 ", "partitionOption": "None"}, "dataset": {"referenceName": "SQLMI_MOW_PARAM", "type": "DatasetReference", "parameters": {"DBName": "Billing", "SchemaName": "StateStreet", "TableName": "AccruedFeesInput"}}}}, {"name": "Update Log Table", "type": "Lookup", "dependsOn": [], "policy": {"timeout": "7.00:00:00", "retry": 0, "retryIntervalInSeconds": 30, "secureOutput": false, "secureInput": false}, "userProperties": [], "typeProperties": {"source": {"type": "SqlMISource", "sqlReaderQuery": {"value": "@concat('UPDATE Billing.StateStreet.GLAFileLogs\nSET IsProcessed = 0 \nwhere 1=1\nand [FileName] =''',pipeline().parameters.InputFileName,''' \nand [FileType] = ''INTRADAY''', ' select 1')", "type": "Expression"}, "partitionOption": "None"}, "dataset": {"referenceName": "SQLMI_MOW_PARAM", "type": "DatasetReference", "parameters": {"DBName": "Billing", "SchemaName": "StateStreet", "TableName": "GLAFileLogs"}}}}]}}, {"name": "Check if All Portfolios are Included", "type": "Lookup", "dependsOn": [{"activity": "sp_LoadAccruedFees", "dependencyConditions": ["Succeeded"]}], "policy": {"timeout": "7.00:00:00", "retry": 0, "retryIntervalInSeconds": 30, "secureOutput": false, "secureInput": false}, "userProperties": [], "typeProperties": {"source": {"type": "SqlMISource", "sqlReaderQuery": "IF OBJECT_ID(N'tempdb..#result') IS NOT NULL\n    DROP TABLE #result\n\nSELECT \n    a.scdPortfolio, \n    b.portfolio\nINTO #result\nFROM StateStreet.FundPortfolioMap a \nLEFT JOIN (\n    SELECT DISTINCT portfolio \n    FROM Billing.StateStreet.AccruedFees\n    WHERE updatedDate IN (\n        SELECT MAX(updatedDate) \n        FROM Billing.StateStreet.AccruedFees\n    )\n) b ON b.portfolio = a.scdportfolio\n\nSELECT COUNT(scdPortfolio) AS MissingPortfolios  \nFROM #result \nWHERE portfolio IS NULL", "partitionOption": "None"}, "dataset": {"referenceName": "SQLMI_MOW_PARAM", "type": "DatasetReference", "parameters": {"DBName": "Billing", "SchemaName": "StateStreet", "TableName": "AccruedFees"}}}}], "parameters": {"InputFileName": {"type": "string", "defaultValue": "INTRADAYGLA10520220704_1445.txt"}, "Date": {"type": "string", "defaultValue": "20220704"}}, "variables": {"Date": {"type": "String"}, "InputFileName": {"type": "String"}, "RunDate": {"type": "String"}}, "folder": {"name": "ZzzInternal\\Billing and QFM"}, "annotations": [], "lastPublishTime": "2024-01-25T21:38:20Z"}, "type": "Microsoft.DataFactory/factories/pipelines"}