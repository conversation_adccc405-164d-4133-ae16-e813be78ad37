{"name": "DECOM- IN-458 Mawer-BBHData-ExtractUpload-Workflow", "properties": {"activities": [{"name": "Generate PSV File", "type": "Copy", "dependsOn": [], "policy": {"timeout": "7.00:00:00", "retry": 0, "retryIntervalInSeconds": 30, "secureOutput": false, "secureInput": false}, "userProperties": [], "typeProperties": {"source": {"type": "SqlServerSource", "sqlReaderQuery": "with a\nas (\n\nselect \nreplace(convert(varchar, v.ReportDate, 111),'/','') AS  'AsOfDate'\n      ,s.CUSIPCd AS 'CUSIP'\n       , s.ISINCd AS 'ISIN'\n       , s.SEDOLCd AS 'SEDOL'\n       , case when (left(f.<PERSON><PERSON><PERSON>ian<PERSON>m,4) = '' and f.ManagementTypeCd = 'pooled' and f.HoldingTypeCd = 'Funds') then 'CITI' \n               else rtrim(replace(left(f.CustodianNm,4),'-', '')) end  'ProviderCode'\n     , convert(nvarchar(32),v.PortfolioCode) AS  'AccountNumber'\n     , c.CrdCountryCd AS 'CountryID'\n       , convert(varchar(20), (convert(decimal(15,2), v.Quantity))) 'PositionValue'\n                   , getdate() as Loaded\n\t\t\t\t   ,substring(s.ExchangeCd, 0, 5) as Sec_ext_id\n\t\t\t\t   \n\n\n\nfrom [DATAMART].[dbo].[SecurityDIM] s \nINNER JOIN [INTEGRATION].[dbo].[Country] c ON s.CountryIncorporationCd = c.CrdCountryCd\nINNER JOIN [DATAMART].[dw].[DW_ValuationMaster] v ON v.instrumentid = s.LzSecurityCd\nINNER JOIN ( SELECT MAX(ReportDate) AS MaxDate  FROM [DATAMART].[dw].[DW_ValuationMaster]) vm on v.ReportDate = vm.MaxDate\nINNER JOIN [DATAMART].[dbo].[PortfolioDIM] f ON f.ApxPortfolioCd=v.PortfolioCode \nwhere s. LzSecurityCd ='LZI79' and  s.CurrentInd = 'Y'\nand v.PortfolioCode like '[0-9]%'\n  and f.PortfolioTypeCd <> 'Model'\n  and s.SecurityNm not like '%Dividend%'\n  and ((s.CUSIPCd IS NOT NULL) OR (s.SEDOLCd IS NOT NULL) OR (s.ISINCd IS NOT NULL)) \n  \n\n),\n\n\nb\nas (\nselect \nAsOfDate\n,CUSIP\n,ISIN\n,SEDOL\n,ProviderCode\n,AccountNumber\n,CountryID\n,sum(cast(PositionValue as decimal)) as PositionValue\nfrom a\nwhere sec_ext_id <> 'mfca'\ngroup by \nAsOfDate\n,CUSIP\n,ISIN\n,SEDOL\n,ProviderCode\n,AccountNumber\n,CountryID\n)\n\n\nselect * from [b]\norder by [b].[AccountNumber]", "queryTimeout": "02:00:00", "partitionOption": "None"}, "sink": {"type": "DelimitedTextSink", "storeSettings": {"type": "AzureBlobFSWriteSettings"}, "formatSettings": {"type": "DelimitedTextWriteSettings", "quoteAllText": true, "fileExtension": ".txt"}}, "enableStaging": false}, "inputs": [{"referenceName": "SQL_Mawer_BBH", "type": "DatasetReference"}], "outputs": [{"referenceName": "AzureDataLake_Mawer_BBH_PSV", "type": "DatasetReference"}]}], "folder": {"name": "Internal/DDI/IN-329 DDI Migration/IN-458 Mawer-BBHData-ExtractUpload-Workflow"}, "annotations": [], "lastPublishTime": "2023-12-01T04:12:15Z"}, "type": "Microsoft.DataFactory/factories/pipelines"}