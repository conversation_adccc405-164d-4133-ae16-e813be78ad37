{"name": "CleanUpPerformance", "properties": {"folder": {"name": "Inbound/ClientPortal"}, "type": "MappingDataFlow", "typeProperties": {"sources": [{"dataset": {"referenceName": "ADL_CSV_File_Param", "type": "DatasetReference"}, "name": "ADL"}, {"dataset": {"referenceName": "CosmosClientPortalAR_Prod", "type": "DatasetReference"}, "name": "ClientPortal"}], "sinks": [{"dataset": {"referenceName": "CosmosClientPortalAR_Prod", "type": "DatasetReference"}, "name": "ClientPortalSink"}], "transformations": [{"name": "JOIN"}, {"name": "CheckIfTrue"}], "scriptLines": ["source(output(", "          PerformanceDate as string,", "          Portfolio as integer,", "          PortfolioTypeCode as string,", "          ReportName as string,", "          id as string", "     ),", "     allowSchemaDrift: true,", "     validateSchema: false,", "     ignoreNoFilesFound: false,", "     wildcardPaths:['ClientPortal/Performance/Deleted/20240220.csv']) ~> ADL", "source(output(", "          Portfolio as string,", "          ReportName as string,", "          id as string,", "          PerformanceDate as date,", "          PortfolioTypeCode as string", "     ),", "     allowSchemaDrift: true,", "     validateSchema: false,", "     query: 'SELECT p.id, p<PERSON>, p.PortfolioTypeCode, perf.ReportName, p.PerformanceDate\\nFROM PortfolioPerformance p JOIN perf IN p.Performance',", "     format: 'documentQuery',", "     systemColumns: false,", "     container: 'PortfolioPerformance',", "     storeType: 'oltp') ~> ClientPortal", "ADL, ClientPortal join(ADL@id == ClientPortal@id", "     && ADL@ReportName == ClientPortal@ReportName,", "     joinType:'inner',", "     matchType:'exact',", "     ignoreSpaces: false,", "     broadcast: 'auto')~> JOIN", "JOIN alterRow(deleteIf(true())) ~> CheckIfTrue", "CheckIfTrue sink(allowSchemaDrift: true,", "     validateSchema: false,", "     deletable:true,", "     insertable:false,", "     updateable:false,", "     upsertable:false,", "     format: 'document',", "     partitionKey: ['/PK'],", "     skipDuplicateMapInputs: true,", "     skipDuplicateMapOutputs: true,", "     container: 'PortfolioPerformance',", "     store: 'cosmosDB') ~> ClientPortalSink"]}}}