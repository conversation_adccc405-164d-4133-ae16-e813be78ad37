{"name": "IN-142 USBank FX", "properties": {"activities": [{"name": "If Send File to SCD", "type": "IfCondition", "dependsOn": [{"activity": "Create Output File", "dependencyConditions": ["Succeeded"]}], "userProperties": [], "typeProperties": {"expression": {"value": "@pipeline().parameters.pSendToSCD", "type": "Expression"}, "ifTrueActivities": [{"name": "Copy Output file to SCD SFTP", "type": "Copy", "dependsOn": [], "policy": {"timeout": "7.00:00:00", "retry": 0, "retryIntervalInSeconds": 30, "secureOutput": false, "secureInput": false}, "userProperties": [], "typeProperties": {"source": {"type": "BinarySource", "storeSettings": {"type": "AzureBlobFSReadSettings", "recursive": false, "deleteFilesAfterCompletion": false}, "formatSettings": {"type": "BinaryReadSettings"}}, "sink": {"type": "BinarySink", "storeSettings": {"type": "SftpWriteSettings", "operationTimeout": "01:00:00", "useTempFileRename": true}}, "enableStaging": false}, "inputs": [{"referenceName": "ADL_Binary_Dir", "type": "DatasetReference", "parameters": {"Path": {"value": "@concat(pipeline().parameters.pADLPath,'/OUT')", "type": "Expression"}}}], "outputs": [{"referenceName": "SCD_SFTP_Binary_Dir", "type": "DatasetReference", "parameters": {"Path": {"value": "@pipeline().parameters.pSCDsftpPath", "type": "Expression"}}}]}]}}, {"name": "Create Output File", "type": "Copy", "dependsOn": [], "policy": {"timeout": "7.00:00:00", "retry": 0, "retryIntervalInSeconds": 30, "secureOutput": false, "secureInput": false}, "userProperties": [], "typeProperties": {"source": {"type": "SqlMISource", "sqlReaderStoredProcedureName": "[Integration].[FX].[sp_USBFXTransactions]", "storedProcedureParameters": {"DataDate": {"type": "String", "value": {"value": "@pipeline().parameters.pDate", "type": "Expression"}}}, "partitionOption": "None"}, "sink": {"type": "DelimitedTextSink", "storeSettings": {"type": "AzureBlobFSWriteSettings"}, "formatSettings": {"type": "DelimitedTextWriteSettings", "quoteAllText": true, "fileExtension": ".txt"}}, "enableStaging": false, "translator": {"type": "TabularTranslator", "typeConversion": true, "typeConversionSettings": {"allowDataTruncation": true, "treatBooleanAsNumber": false}}}, "inputs": [{"referenceName": "MOW_PARAM", "type": "DatasetReference", "parameters": {"DBName": "Integration", "SchemaName": "FX", "TableName": "sp_USBFXTransactions"}}], "outputs": [{"referenceName": "ADL_Delimited_File_NoQuote", "type": "DatasetReference", "parameters": {"FilePath": {"value": "@concat(pipeline().parameters.pADLPath,'/OUT')", "type": "Expression"}, "FileName": {"value": "@concat(pipeline().parameters.pDate,\r\nformatDateTime(utcnow(), 'HHmmss'),'_',\r\npipeline().parameters.pDate, \r\n'_USBank_FXTrades.csv' )", "type": "Expression"}, "ColDelimiter": ",", "FirstRowHeader": true}}]}, {"name": "Archive OUT", "type": "Copy", "dependsOn": [{"activity": "If Send File to SCD", "dependencyConditions": ["Succeeded"]}], "policy": {"timeout": "7.00:00:00", "retry": 0, "retryIntervalInSeconds": 30, "secureOutput": false, "secureInput": false}, "userProperties": [], "typeProperties": {"source": {"type": "BinarySource", "storeSettings": {"type": "AzureBlobFSReadSettings", "recursive": true, "deleteFilesAfterCompletion": true}, "formatSettings": {"type": "BinaryReadSettings"}}, "sink": {"type": "BinarySink", "storeSettings": {"type": "AzureBlobFSWriteSettings"}}, "enableStaging": false}, "inputs": [{"referenceName": "ADL_Binary_Dir", "type": "DatasetReference", "parameters": {"Path": {"value": "@concat(pipeline().parameters.pADLPath,'/OUT')", "type": "Expression"}}}], "outputs": [{"referenceName": "ADL_Binary_Dir", "type": "DatasetReference", "parameters": {"Path": {"value": "@concat(pipeline().parameters.pADLPath,'/ARCHIVE/',pipeline().parameters.pDate,'/OUT')", "type": "Expression"}}}]}], "parameters": {"pDate": {"type": "string", "defaultValue": "********"}, "pADLPath": {"type": "string", "defaultValue": "Custodian Data/USBank/USBank FX"}, "pADLContainer": {"type": "string", "defaultValue": "scdintegration"}, "pSendToSCD": {"type": "bool", "defaultValue": false}, "pSCDsftpPath": {"type": "string", "defaultValue": "X"}, "pDataType": {"type": "string", "defaultValue": "X"}}, "folder": {"name": "Custodians and Vendors/USBank/USBank FX"}, "annotations": [], "lastPublishTime": "2024-08-14T21:31:32Z"}, "type": "Microsoft.DataFactory/factories/pipelines"}