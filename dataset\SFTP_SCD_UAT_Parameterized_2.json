{"name": "SFTP_SCD_UAT_Parameterized_2", "properties": {"linkedServiceName": {"referenceName": "SCD_SFTP", "type": "LinkedServiceReference"}, "parameters": {"FilePath": {"type": "string"}}, "folder": {"name": "SCD"}, "annotations": [], "type": "DelimitedText", "typeProperties": {"location": {"type": "SftpLocation", "folderPath": {"value": "@dataset().FilePath", "type": "Expression"}}, "columnDelimiter": ",", "escapeChar": "\\", "firstRowAsHeader": true, "quoteChar": "\""}, "schema": []}, "type": "Microsoft.DataFactory/factories/datasets"}