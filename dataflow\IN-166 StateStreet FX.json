{"name": "IN-166 StateStreet FX", "properties": {"folder": {"name": "Inbound"}, "type": "MappingDataFlow", "typeProperties": {"sources": [{"dataset": {"referenceName": "AzureDataLakeStorage_FX_StateStreet_IN_01", "type": "DatasetReference"}, "name": "CSVSourceFile"}, {"dataset": {"referenceName": "AzureDataLakeStorage_FX_StateStreet_TEMP_01", "type": "DatasetReference"}, "name": "StaticFundPortfolioMappingFile"}, {"dataset": {"referenceName": "AzureDataLakeStorage_FX_StateStreet_TEMP_01", "type": "DatasetReference"}, "name": "Transactions"}, {"dataset": {"referenceName": "AzureDataLakeStorage_FX_04", "type": "DatasetReference"}, "name": "DMPortfolio"}], "sinks": [{"dataset": {"referenceName": "AzureDataLakeStorage_FX_Statestreet_OUT_01", "type": "DatasetReference"}, "name": "GenerateCSVOutputFile"}], "transformations": [{"name": "JoinAtPortfolioCode"}, {"name": "SelectRelevantFields"}, {"name": "AddDerivedColumns"}, {"name": "MapIMWColumns", "description": "Creates an explicit mapping for each drifted column"}, {"name": "JoinWithCustodianMappingSell"}, {"name": "JoinWithCustodianMappingBuy"}, {"name": "SelectStaticFundPortfolioMappingFile"}, {"name": "SelectTxnCols"}, {"name": "SelectDMPortfolioCols"}, {"name": "JoinDMPort<PERSON>lio"}, {"name": "filterSomeFunds"}], "script": "parameters{\n\tinputFileName as string,\n\tinputFileName2 as string,\n\toutputFileName as string,\n\tStaticFileDir as string ('Static/')\n}\nsource(output(\n\t\tFund as string,\n\t\t{Client ID} as string,\n\t\t{Client reference#} as string,\n\t\t{Trade Date} as string,\n\t\t{Value Date} as string,\n\t\t{Buy Currency} as string,\n\t\t{Buy Net Amount} as string,\n\t\t{Se<PERSON> Currency} as string,\n\t\t{Sell Net Amount} as string,\n\t\t{FX Rate} as string,\n\t\t{FX Deal Entry Date} as string\n\t),\n\tallowSchemaDrift: true,\n\tvalidateSchema: false,\n\tignoreNoFilesFound: false,\n\tmoveFiles: [(concat(\"FX/Statestreet/IN/\", $inputFileName)),(concat(\"FX/Statestreet/ARCHIVE/IN/\", toString(currentUTC(),\"yyyyMMdd\"),\"/\",$inputFileName))],\n\twildcardPaths:[(concat(\"FX/Statestreet/IN/\", $inputFileName))]) ~> CSVSourceFile\nsource(output(\n\t\tFUND as string,\n\t\tCODE as string\n\t),\n\tallowSchemaDrift: true,\n\tvalidateSchema: false,\n\tignoreNoFilesFound: false,\n\twildcardPaths:[(concat(\"FX/Statestreet/TEMP/\", $inputFileName2))]) ~> StaticFundPortfolioMappingFile\nsource(output(\n\t\tDWH_EXTRACT_PROCESS_IK as short,\n\t\tDWH_LOAD_TS as string,\n\t\tLAST_CHANGE_TS as string,\n\t\tTRANSACTIONS_KEY as integer,\n\t\tTRANSACTION_IK as short,\n\t\tBUS_TRANS_KEY as integer,\n\t\tBUSINESS_TRANSACTION_CODE as string,\n\t\tBUSINESS_TRANSACTION_CODE_NO as string,\n\t\tELEMENTARY_TRANS_CODE_NO as short,\n\t\tELEMENTARY_TRANS_CODE as string,\n\t\tCOUNTERPARTY_KEY as short,\n\t\tCOUNTERPARTY as string,\n\t\tCOUNTERPARTY_NAME as string,\n\t\tCOUNTRY_OF_SECURITY as string,\n\t\tCOUNTRY_OF_ISSUER as string,\n\t\tCUSTODY as string,\n\t\tINSTRUMENT_TYPE as string,\n\t\tPORTFOLIO_KEY as integer,\n\t\tPORTFOLIO as string,\n\t\tPORTFOLIO_NAME as string,\n\t\tPORTFOLIO_CURRENCY as string,\n\t\tPORTFOLIO_CURRENCY_NAME as string,\n\t\tMODEL_PORTFOLIO as string,\n\t\tMODEL_STRATEGY_CODE as string,\n\t\tMODEL_STRATEGY_NAME as string,\n\t\tREPORTING_ASSET_CLASS as string,\n\t\tSECTOR_NAME as string,\n\t\tINDUSTRY_GROUP_NAME as string,\n\t\tQUOTATION_CURRENCY as string,\n\t\tQUOTATION_CURRENCY_NAME as string,\n\t\tQUOTATION as string,\n\t\tSECURITY_KEY as integer,\n\t\tSECURITY_ID as string,\n\t\tSECURITY_NAME as string,\n\t\tCUSIP as string,\n\t\tISIN as string,\n\t\tSEDOL as string,\n\t\tSECURITY_TYPE as string,\n\t\tSECURITY_TYPE_NAME as string,\n\t\tTRANSACTION_CANCELLATION_FLAG as string,\n\t\tTRANSACTION_STATUS_ACTUAL as string,\n\t\tACCRUED_INTEREST_PC as double,\n\t\tACCRUED_INTEREST_QC as double,\n\t\tACCRUED_INTEREST_SC as double,\n\t\tACCRUED_INTEREST_CAD as double,\n\t\tFX_RATE_QC_PC as double,\n\t\tFX_RATE_QC_SC as double,\n\t\tFX_FACTOR_PC_CAD as double,\n\t\tLEG_NO as short,\n\t\tPAYMENT_AMOUNT_PC as double,\n\t\tPAYMENT_AMOUNT_QC as double,\n\t\tPAYMENT_AMOUNT_SC as double,\n\t\tPAYMENT_AMOUNT_CAD as double,\n\t\tFORWARD_CURRENT_VALUE_PC as double,\n\t\tFORWARD_CURRENT_VALUE_QC as double,\n\t\tFORWARD_CURRENT_VALUE_CAD as double,\n\t\tACCRUED_INTEREST_DAYS as short,\n\t\tNOMINAL as double,\n\t\tCURRENT_VALUE_PC as double,\n\t\tCURRENT_VALUE_QC as double,\n\t\tCURRENT_VALUE_SC as double,\n\t\tCURRENT_VALUE_CAD as double,\n\t\tDIVIDEND_PER_SHARE as double,\n\t\tPAYMENT_DATE_FULL as string,\n\t\tPRICE as double,\n\t\tSETTLEMENT_DATE_FULL as string,\n\t\tSETTLEMENT_CURRENCY as string,\n\t\tSETTLEMENT_CURRENCY_NAME as string,\n\t\tSIGN as short,\n\t\tTRADE_DATE_FULL as string,\n\t\tTRANSACTION_CANCELLATION_NO as boolean,\n\t\tCOMPLIANCE_PURPOSE as string,\n\t\tCOMPLIANCE_INFORMATION as string,\n\t\tSWIFT_MT_TYPE as string,\n\t\tCTM_FORWARD_PAYMENT_AMOUNT as boolean,\n\t\tCTM_TOTAL_REPO_INTEREST as boolean,\n\t\tCTM_TRANS_NO_ON_ALLOCATION as string,\n\t\tCTM_STATUS_BROKER as string,\n\t\tSWIFT_BYPASS as string,\n\t\tTRADE_CONFIRMATION_BYPASS as boolean,\n\t\tCTM_TYPE_OF_REPO_CONTRACT as string,\n\t\tTRANSACTION_NETTING as string,\n\t\tREALIZED_P_L as double,\n\t\tWHT_AMOUNT_SETTLEMENT_CURRENCY as string,\n\t\tWHT_AMOUNT_PORTFOLIO_CURRENCY as string,\n\t\tWHT_AMOUNT_QUOTATION_CURRENCY as string,\n\t\tORIGINATING_SYSTEM_NO as boolean,\n\t\tCOUNTERPARTY_IK as string,\n\t\tPORTFOLIO_IK as string,\n\t\tSECURITY_IK as string,\n\t\tNOMINAL_BASIS as string,\n\t\tTRANS_NO_IN_ORIGINATING_SYSTEM as string\n\t),\n\tallowSchemaDrift: true,\n\tvalidateSchema: false,\n\tignoreNoFilesFound: false,\n\twildcardPaths:[(concat($StaticFileDir, iif(endsWith($StaticFileDir, '/'), '', '/'), 'IMW_ModelPortfolio.csv'))]) ~> Transactions\nsource(output(\n\t\tPORTFOLIO as string,\n\t\tMODEL_PORTFOLIO as string\n\t),\n\tallowSchemaDrift: true,\n\tvalidateSchema: false,\n\tignoreNoFilesFound: false,\n\twildcardPaths:[(concat($StaticFileDir, iif(endsWith($StaticFileDir, '/'), '', '/'), 'DM_MODEL_PORTFOLIO.csv'))]) ~> DMPortfolio\nfilterSomeFunds, SelectStaticFundPortfolioMappingFile join(Fund == StaticFUND,\n\tjoinType:'inner',\n\tmatchType:'exact',\n\tignoreSpaces: false,\n\tbroadcast: 'auto')~> JoinAtPortfolioCode\nJoinWithCustodianMappingBuy select(mapColumn(\n\t\t{Client ID},\n\t\t{Client reference#},\n\t\t{Trade Date},\n\t\t{Value Date},\n\t\t{Buy Currency},\n\t\t{Buy Net Amount},\n\t\t{Sell Currency},\n\t\t{Sell Net Amount},\n\t\t{FX Rate},\n\t\t{FX Deal Entry Date},\n\t\tFUND = StaticFUND,\n\t\tCODE = StaticCode,\n\t\tTXN_MODEL_PORTFOLIO\n\t),\n\tskipDuplicateMapInputs: true,\n\tskipDuplicateMapOutputs: true) ~> SelectRelevantFields\nJoinDMPortfolio derive(UniqueId = substring(replace(uuid(), \"-\", \"\") , 1, 30),\n\t\tNominal = abs(toDecimal({Buy Net Amount},23,2)),\n\t\tNominal_L2 = abs(toDecimal({Sell Net Amount},23,2)),\n\t\t{Trade Date_formatted} = toString(toDate({Trade Date}, \"dd MMM yyyy\"),\"dd-MM-yyyy\"),\n\t\t{Value Date_formatted} = toString(toDate({Value Date}, \"dd MMM yyyy\"),\"dd-MM-yyyy\"),\n\t\tNominal_currency = {Buy Currency},\n\t\tNominal_L2_currency = {Sell Currency},\n\t\t{FX Rate_forward} = \"\",\n\t\tMaturity_date = toString(toDate({Value Date}, \"dd MMM yyyy\"),\"dd-MM-yyyy\"),\n\t\tCurrency_blank = \"\",\n\t\tCurrencyL2_blank = \"\",\n\t\tMODEL_PORTFOLIO_D = coalesce(TXN_MODEL_PORTFOLIO, DMPORTFOLIO_MODEL_PORTFOLIO, 'CENTRAL'),\n\t\t{FX Rate_Blank} = \"\") ~> AddDerivedColumns\nTransactions derive(BUSINESS_TRANSACTION_CODE = toString(byName('BUSINESS_TRANSACTION_CODE')),\n\t\tPORTFOLIO = toString(byName('PORTFOLIO')),\n\t\tPAYMENT_AMOUNT_SC = toString(byName('PAYMENT_AMOUNT_SC')),\n\t\tSETTLEMENT_DATE_FULL = toString(byName('SETTLEMENT_DATE_FULL')),\n\t\tTRADE_DATE_FULL = toString(byName('TRADE_DATE_FULL')),\n\t\tMODEL_PORTFOLIO = toString(byName('MODEL_PORTFOLIO')),\n\t\tSETTLEMENT_DATE_FULL_yyyyMMdd = toString(toDate(SETTLEMENT_DATE_FULL), 'yyyyMMdd')) ~> MapIMWColumns\nJoinAtPortfolioCode, SelectTxnCols join(StaticCode == trim(PORTFOLIO)\n\t&& toString(toDate({Value Date}, \"dd MMM yyyy\"),\"yyyyMMdd\") == SETTLEMENT_DATE_FULL_yyyyMMdd\n\t&& abs(toDecimal({Sell Net Amount},20,2)) == abs(toDecimal(PAYMENT_AMOUNT_SC,20,2))\n\t&& concat('AllocSellExt') == BUSINESS_TRANSACTION_CODE,\n\tjoinType:'left',\n\tmatchType:'exact',\n\tignoreSpaces: false,\n\tbroadcast: 'both')~> JoinWithCustodianMappingSell\nJoinAtPortfolioCode, SelectTxnCols join(trim(StaticCode) == trim(PORTFOLIO)\n\t&& toString(toDate({Value Date}, \"dd MMM yyyy\"),\"yyyyMMdd\") == SETTLEMENT_DATE_FULL_yyyyMMdd\n\t&& abs(toDecimal({Buy Net Amount},20,2)) == abs(toDecimal(PAYMENT_AMOUNT_SC,20,2))\n\t&& concat('AllocBuyExt') == BUSINESS_TRANSACTION_CODE,\n\tjoinType:'left',\n\tmatchType:'exact',\n\tignoreSpaces: false,\n\tbroadcast: 'both')~> JoinWithCustodianMappingBuy\nStaticFundPortfolioMappingFile select(mapColumn(\n\t\tStaticFUND = FUND,\n\t\tStaticCode = CODE\n\t),\n\tskipDuplicateMapInputs: true,\n\tskipDuplicateMapOutputs: true) ~> SelectStaticFundPortfolioMappingFile\nMapIMWColumns select(mapColumn(\n\t\tBUSINESS_TRANSACTION_CODE,\n\t\tPORTFOLIO,\n\t\tPAYMENT_AMOUNT_SC,\n\t\tSETTLEMENT_DATE_FULL,\n\t\tTRADE_DATE_FULL,\n\t\tTXN_MODEL_PORTFOLIO = MODEL_PORTFOLIO,\n\t\tSETTLEMENT_DATE_FULL_yyyyMMdd\n\t),\n\tskipDuplicateMapInputs: true,\n\tskipDuplicateMapOutputs: true) ~> SelectTxnCols\nDMPortfolio select(mapColumn(\n\t\tPORTFOLIO,\n\t\tDMPORTFOLIO_MODEL_PORTFOLIO = MODEL_PORTFOLIO\n\t),\n\tskipDuplicateMapInputs: true,\n\tskipDuplicateMapOutputs: true) ~> SelectDMPortfolioCols\nSelectRelevantFields, SelectDMPortfolioCols join(trim(CODE) == PORTFOLIO,\n\tjoinType:'left',\n\tmatchType:'exact',\n\tignoreSpaces: false,\n\tbroadcast: 'auto')~> JoinDMPortfolio\nCSVSourceFile filter(not(or(equals(Fund,'MMDI'),equals(Fund,'MMEA')))) ~> filterSomeFunds\nAddDerivedColumns sink(allowSchemaDrift: true,\n\tvalidateSchema: false,\n\tinput(\n\t\tInstrument_Type as string,\n\t\tTransaction_Cancellation_Flag as string,\n\t\tBlock_Trade_ID as string,\n\t\tTransaction_origin_Trade_ID as string,\n\t\tSecurity_ID as string,\n\t\tSecurity_ID_Type as string,\n\t\tPortfolio as string,\n\t\tModel_portfolio as string,\n\t\tPurpose as string,\n\t\tCounterparty as string,\n\t\tCounterpartys_dealer as string,\n\t\tBroker as string,\n\t\tTrans_code as string,\n\t\tOpen_close as string,\n\t\tNominal as string,\n\t\tPrice as string,\n\t\tQuotation as string,\n\t\tExchange as string,\n\t\tMargin_clearer as string,\n\t\tTrade_date as string,\n\t\tPayment_date as string,\n\t\tType_of_settlement as string,\n\t\tTrade_time as string,\n\t\tExecution_time as string,\n\t\tClearing_time as string,\n\t\tConfirmation_time as string,\n\t\tSettlement_comment as string,\n\t\tInstruction_comment as string,\n\t\tDealer as string,\n\t\tBlocking_type as string,\n\t\tBlocked_until as string,\n\t\tIFRS_9_purpose as string,\n\t\tSpecial_holding_mark as string,\n\t\tClient as string,\n\t\tCompound as string,\n\t\tCommission as string,\n\t\tNominal_currency as string,\n\t\tNominal_L2 as string,\n\t\tKeep_amount_in_base_Ccy as string,\n\t\tNominal_L2_currency as string,\n\t\tFX_rate_spot as string,\n\t\tFX_tics as string,\n\t\tFX_rate_forward as string,\n\t\tPlace_PL_on_price_Ccy as string,\n\t\tMaturity_date as string,\n\t\tFirst_fixing_date as string,\n\t\tFixing_date as string,\n\t\tTime_option_start_date as string,\n\t\tNominal_far as string,\n\t\tNominal_L2_far as string,\n\t\tMaturity_date_drawn_bonds as string,\n\t\tMaturity_price_drawn_bonds as string,\n\t\tCollateral_pool as string,\n\t\tCurrency_overlay_model_portfolio as string,\n\t\tCurrency_overlay_model_portfolio_L2 as string,\n\t\tSettlement_currency as string,\n\t\tFX_rate_QS as string,\n\t\tTransaction_origin as string,\n\t\tCost as string,\n\t\tCurrency as string,\n\t\tAmount_cost_currency as string,\n\t\tCost_L2 as string,\n\t\tCurrency_L2 as string,\n\t\tAmount_cost_currency_L2 as string,\n\t\tUnderlying_Security_ID as string,\n\t\tRequirement_percentage as string,\n\t\tDividend_all_in_percentage as string,\n\t\tRate as string,\n\t\tFee_type as string,\n\t\tSplit_trade as string,\n\t\tUnderlying_price as string,\n\t\tUnderlying_holding_portfolio as string,\n\t\tUnderlying_holding_nominal as string,\n\t\tPledge_Security_ID as string,\n\t\tAccount_ID as string,\n\t\tExecution_reference as string,\n\t\tQuote_currency as string,\n\t\tCurrent_face as string,\n\t\tDaylight_indicator as string,\n\t\tCollateral_result_ID as string,\n\t\tDestination_custody as string,\n\t\tDelivery_type as string,\n\t\tExpansionPlaceHolder83 as string,\n\t\tExpansionPlaceHolder84 as string,\n\t\tExpansionPlaceHolder85 as string,\n\t\tExpansionPlaceHolder86 as string,\n\t\tExpansionPlaceHolder87 as string,\n\t\tExpansionPlaceHolder88 as string,\n\t\tExpansionPlaceHolder89 as string,\n\t\tExpansionPlaceHolder90 as string,\n\t\tExpansionPlaceHolder91 as string,\n\t\tExpansionPlaceHolder92 as string,\n\t\tExpansionPlaceHolder93 as string,\n\t\tExpansionPlaceHolder94 as string,\n\t\tExpansionPlaceHolder95 as string,\n\t\tExpansionPlaceHolder96 as string,\n\t\tExpansionPlaceHolder97 as string,\n\t\tExpansionPlaceHolder98 as string,\n\t\tExpansionPlaceHolder99 as string,\n\t\tExpansionPlaceHolder100 as string,\n\t\tExpansionPlaceHolder101 as string,\n\t\tExpansionPlaceHolder102 as string,\n\t\tExpansionPlaceHolder103 as string,\n\t\tExpansionPlaceHolder104 as string,\n\t\tExpansionPlaceHolder105 as string,\n\t\tExpansionPlaceHolder106 as string,\n\t\tExpansionPlaceHolder107 as string,\n\t\tExpansionPlaceHolder108 as string,\n\t\tExpansionPlaceHolder109 as string,\n\t\tExpansionPlaceHolder110 as string,\n\t\tExpansionPlaceHolder111 as string,\n\t\tExpansionPlaceHolder112 as string,\n\t\tExpansionPlaceHolder113 as string,\n\t\tExpansionPlaceHolder114 as string,\n\t\tExpansionPlaceHolder115 as string,\n\t\tExpansionPlaceHolder116 as string,\n\t\tExpansionPlaceHolder117 as string,\n\t\tExpansionPlaceHolder118 as string,\n\t\tExpansionPlaceHolder119 as string,\n\t\tExpansionPlaceHolder120 as string,\n\t\tExpansionPlaceHolder121 as string,\n\t\tExpansionPlaceHolder122 as string,\n\t\tExpansionPlaceHolder123 as string,\n\t\tExpansionPlaceHolder124 as string,\n\t\tExpansionPlaceHolder125 as string,\n\t\tExpansionPlaceHolder126 as string,\n\t\tExpansionPlaceHolder127 as string,\n\t\tExpansionPlaceHolder128 as string,\n\t\tExpansionPlaceHolder129 as string,\n\t\tExpansionPlaceHolder130 as string,\n\t\tExpansionPlaceHolder131 as string,\n\t\tExpansionPlaceHolder132 as string,\n\t\tExpansionPlaceHolder133 as string,\n\t\tExpansionPlaceHolder134 as string,\n\t\tExpansionPlaceHolder135 as string,\n\t\tExpansionPlaceHolder136 as string,\n\t\tExpansionPlaceHolder137 as string,\n\t\tExpansionPlaceHolder138 as string,\n\t\tExpansionPlaceHolder139 as string,\n\t\tExpansionPlaceHolder140 as string,\n\t\tExpansionPlaceHolder141 as string,\n\t\tExpansionPlaceHolder142 as string,\n\t\tExpansionPlaceHolder143 as string,\n\t\tExpansionPlaceHolder144 as string,\n\t\tExpansionPlaceHolder145 as string,\n\t\tExpansionPlaceHolder146 as string,\n\t\tExpansionPlaceHolder147 as string,\n\t\tExpansionPlaceHolder148 as string,\n\t\tExpansionPlaceHolder149 as string,\n\t\tExpansionPlaceHolder150 as string,\n\t\tExpansionPlaceHolder151 as string,\n\t\tExpansionPlaceHolder152 as string,\n\t\tExpansionPlaceHolder153 as string,\n\t\tExpansionPlaceHolder154 as string,\n\t\tExpansionPlaceHolder155 as string,\n\t\tExpansionPlaceHolder156 as string,\n\t\tExpansionPlaceHolder157 as string,\n\t\tExpansionPlaceHolder158 as string,\n\t\tExpansionPlaceHolder159 as string,\n\t\tExpansionPlaceHolder160 as string,\n\t\tExpansionPlaceHolder161 as string,\n\t\tExpansionPlaceHolder162 as string,\n\t\tExpansionPlaceHolder163 as string,\n\t\tExpansionPlaceHolder164 as string,\n\t\tExpansionPlaceHolder165 as string,\n\t\tExpansionPlaceHolder166 as string,\n\t\tExpansionPlaceHolder167 as string,\n\t\tExpansionPlaceHolder168 as string,\n\t\tExpansionPlaceHolder169 as string,\n\t\tExpansionPlaceHolder170 as string,\n\t\tExpansionPlaceHolder171 as string,\n\t\tExpansionPlaceHolder172 as string,\n\t\tExpansionPlaceHolder173 as string,\n\t\tExpansionPlaceHolder174 as string,\n\t\tExpansionPlaceHolder175 as string,\n\t\tExpansionPlaceHolder176 as string,\n\t\tExpansionPlaceHolder177 as string,\n\t\tExpansionPlaceHolder178 as string,\n\t\tExpansionPlaceHolder179 as string,\n\t\t{Expansion placeholders for instrument specific fields} as string,\n\t\tTransaction_free_code_1 as string,\n\t\tTransaction_free_code_2 as string,\n\t\tFrench_insurance_code as string,\n\t\tTransaction_free_code_4 as string,\n\t\tHedging_strategy as string,\n\t\tTransaction_free_code_42 as string,\n\t\tTransaction_free_code_43 as string,\n\t\tTransaction_free_code_44 as string,\n\t\tTransaction_free_code_45 as string,\n\t\tTransaction_free_code_46 as string,\n\t\tTransaction_free_code_47 as string,\n\t\tTransaction_free_code_48 as string,\n\t\tTransaction_free_code_49 as string,\n\t\tTransaction_free_code_50 as string,\n\t\tNAIC_pledged_securities as string,\n\t\tTransaction_free_code_6 as string,\n\t\tTransaction_free_code_7 as string,\n\t\tTransaction_free_code_8 as string,\n\t\tTransaction_free_code_9 as string,\n\t\tTransaction_free_code_10 as string,\n\t\tTransaction_free_code_11 as string,\n\t\tCentra_date as string,\n\t\tTransaction_free_code_13 as string,\n\t\tTransaction_free_code_14 as string,\n\t\tTransaction_netting as string,\n\t\tCLS_bypass as string,\n\t\tNAV_date as string,\n\t\tTransaction_free_code_20 as string,\n\t\tLinked_Trade_ID as string,\n\t\tTransaction_free_code_22 as string,\n\t\tTransaction_free_code_23 as string,\n\t\tTransaction_free_code_24 as string,\n\t\tTransaction_free_code_25 as string,\n\t\tTransaction_free_code_26 as string,\n\t\tStatus_update as string,\n\t\tCTM_status_broker as string,\n\t\tMSC_novation_fee_cpty as string,\n\t\tSWIFT_bypass as string,\n\t\tTrade_confirmation_bypass as string,\n\t\tConfirmations_means as string,\n\t\tSWIFT_MT_type as string,\n\t\tTransaction_free_code_34 as string,\n\t\tTransaction_free_code_35 as string,\n\t\tTransaction_free_code_36 as string,\n\t\tTransaction_free_code_37 as string,\n\t\tTransaction_free_code_38 as string,\n\t\tTransaction_free_code_39 as string,\n\t\tTransaction_free_code_40 as string,\n\t\tTransaction_free_code_41 as string,\n\t\tTransaction_free_code_52 as string,\n\t\tMiFIR_relevant_modification as string,\n\t\tMMSC_alt_trans_type as string,\n\t\tMMSC_MTM_status_APL as string,\n\t\tMSC_clearing_status_APL as string,\n\t\tTransaction_free_code_57 as string,\n\t\tTransaction_free_code_58 as string,\n\t\tTransaction_free_code_59 as string,\n\t\tTransaction_free_code_60 as string,\n\t\tTransaction_free_code_61 as string,\n\t\tTransaction_free_code_62 as string,\n\t\tTransaction_free_code_63 as string,\n\t\tTransaction_free_code_64 as string,\n\t\tTransaction_free_code_65 as string,\n\t\tTransaction_free_code_66 as string,\n\t\tTransaction_free_code_67 as string,\n\t\tTransaction_free_code_68 as string,\n\t\tSubscription_mode as string,\n\t\tTransaction_free_code_70 as string,\n\t\tTransaction_free_code_71 as string,\n\t\tTransaction_free_code_72 as string,\n\t\tTransaction_free_code_73 as string,\n\t\tMSC_independent_val as string,\n\t\tCTM_forward_payment_amount as string,\n\t\tCTM_total_repo_interest as string,\n\t\tTransaction_free_code_77 as string,\n\t\tTransaction_free_code_78 as string,\n\t\tTransaction_free_code_79 as string,\n\t\tTransaction_free_code_80 as string,\n\t\tTransaction_free_code_81 as string,\n\t\tTransaction_free_code_82 as string,\n\t\tTransaction_free_code_83 as string,\n\t\tTransaction_free_code_84 as string,\n\t\tTransaction_free_code_85 as string,\n\t\tTransaction_free_code_86 as string,\n\t\tTransaction_free_code_87 as string,\n\t\tTransaction_free_code_88 as string,\n\t\tTransaction_free_code_89 as string,\n\t\tTransaction_free_code_90 as string,\n\t\tTransaction_free_code_91 as string,\n\t\tTransaction_free_code_92 as string,\n\t\tTransaction_free_code_93 as string,\n\t\tETD_position_UTI as string,\n\t\tCTM_transaction_no_on_allocation as string,\n\t\tTransaction_free_code_96 as string,\n\t\tWash_sales as string,\n\t\tNAIC_purpose_derivatives as string,\n\t\tNAIC_transaction_purpose as string,\n\t\tCTM_type_of_repo_contract as string,\n\t\tTransaction_free_code_101 as string,\n\t\t{Expansion placeholders for generic trade fields} as string\n\t),\n\tpartitionFileNames:[($outputFileName)],\n\tumask: 0022,\n\tpreCommands: [],\n\tpostCommands: [],\n\tskipDuplicateMapInputs: true,\n\tskipDuplicateMapOutputs: true,\n\tmapColumn(\n\t\tTrade_date = {Trade Date_formatted},\n\t\tPayment_date = {Value Date_formatted},\n\t\tCurrency = Currency_blank,\n\t\tNominal,\n\t\tCurrency_L2 = CurrencyL2_blank,\n\t\tNominal_L2,\n\t\tFX_rate_spot = {FX Rate_Blank},\n\t\tPortfolio = CODE,\n\t\tTransaction_origin_Trade_ID = UniqueId,\n\t\tNominal_currency,\n\t\tNominal_L2_currency,\n\t\tFX_rate_forward = {FX Rate_forward},\n\t\tMaturity_date,\n\t\tModel_portfolio = MODEL_PORTFOLIO_D\n\t),\n\tpartitionBy('hash', 1)) ~> GenerateCSVOutputFile"}}}