{"name": "DECOM- IN-330 MBS Fact Sets", "properties": {"activities": [{"name": "Generate source file using data from various DB", "type": "Copy", "dependsOn": [], "policy": {"timeout": "7.00:00:00", "retry": 0, "retryIntervalInSeconds": 30, "secureOutput": false, "secureInput": false}, "userProperties": [], "typeProperties": {"source": {"type": "SqlServerSource", "sqlReaderQuery": "DECLARE @BloombergDate DATE\n\nSET @BloombergDate = (SELECT MAX(DateID) AS BloombergDate FROM [dbo].[BloombergData])\n\n;WITH CouponTyp AS \n\t(SELECT F.SecurityKey\n\t\t  , IIF(F.CouponNextResetDate IS NULL, 'Fixed', 'Variable') AS CouponType\n\tFROM [MWR-SRV-EDW\\MSSQLSERVER].[DATAMART].[dbo].[FactFixedIncomeSecurityDaily] AS F JOIN [MWR-SRV-EDW\\MSSQLSERVER].DATAMART.[dbo].[SecurityDim] AS SD\n\t\t\tON F.SecurityKey = SD.SecurityKey\n\tWHERE 1 = 1 \n\tAND SD.CurrentInd = 'Y'\n\tAND SD.SecurityStatusCd = 'A')\n\n\nSELECT CAST(FORMAT(GETDATE() -1, 'yyyyMMdd') AS VARCHAR(8)) AS CurrentDate\t\t\t\n     , REPLACE(i.SEDOL, '.old', '')\t\t\t\t\t\t\tAS InstrumentSEDOL\n\t , REPLACE(i.ISIN_NO, '.old', '')\t\t\t\t\t\tAS InstrumentISIN\n\t , REPLACE(i.CUSIP, '.old', '')\t\t\t\t\t\t\tAS InstrumentCUSIP\n\t , CASE \n            WHEN (i.SEDOL IS NOT NULL AND i.SEDOL != ' ') THEN replace(i.SEDOL, '.old', '') \n            WHEN (i.ISIN_NO IS NOT NULL AND i.ISIN_NO != ' ') THEN replace(i.ISIN_NO, '.old', '') \n            WHEN (i.CUSIP IS NOT NULL AND i.CUSIP != ' ') THEN replace(i.CUSIP, '.old', '') \n            WHEN LEN(i.UDF_CHAR2) > 0 THEN 'MAW' + convert(varchar(5), i.UDF_FLOAT2)   -- Ticket# 5900 (Darren Tan)\n            WHEN i.SEC_NAME LIKE '%Dividend Accrual%' THEN 'DIVA_' + s1.CurrencyQuotationCd\n            WHEN i.UDF_CHAR6 = '40105' -- for Spot rate \n                     THEN 'CASH_' + s1.CurrencyQuotationCd\n            ELSE NULL \n\t\t\tEND\t\t\t\t\t\t\t\t\t\t\t\tAS Symbol\n\t , s1.SecurityNm\n\t , s1.MawerSecurityNm\n\t , s1.BBIssuerNm\t\t\n\t , b.ISSUER\t\t\n\t , CASE WHEN LTRIM(RTRIM(s1.InstrumentTypeTier3Nm)) = 'MBS (includes pass-throughs)' THEN s1.InstrumentTypeTier3Nm\t\n\t         ELSE a.[IndustryGroupName]\n\t\t\t END\t\t\t\t\t\tAS IndustryGroup\t\n\t , s1.MawerAssetClassTier1Nm\n\t , s1.MawerAssetClassTier2Nm\n\t , s1.MawerAssetClassTier3Nm\t\t\n\t , b.ISSUE_DT\n\t , b.MATURITY \t\t\n\t , b.MTG_WAM\n\t , b.MTG_WALA_CALC\n\t , b.MTG_WACPN\t\t\n\t , REPLACE(b.MTG_PAY_DELAY, 'DAYS', '')\t\tAS MTG_PAY_DELAY\t\t\n\t , b.MTG_WAL\n\t , CAST(REPLACE(b.RTG_DBRS, 'N.A.', 'N/A') AS VARCHAR(50))\t\tAS RTG_DBRS\n\t , CAST(REPLACE(b.RTG_SP, 'N.A.', 'N/A') AS VARCHAR(50))\t\tAS RTG_SP\n\t , CAST(REPLACE(b.RTG_MOODY, 'N.A.', 'N/A') AS VARCHAR(50))\t\tAS RTG_MOODY\n\t , CAST(REPLACE(b.YLD_YTM_MID, 'N.A.', 'N/A') AS VARCHAR(50))\tAS YLD_YTM_MID\n\t , CAST(REPLACE(b.YLD_CUR_MID, 'N.A.', 'N/A') AS VARCHAR(50))\tAS YLD_CUR_MID\n\t , CAST(REPLACE(b.DUR_ADJ_MID, 'N.A.', 'N/A') AS VARCHAR(50))\tAS DUR_ADJ_MID\n\t , CAST(REPLACE(b.CPN, 'N.A.', 'N/A') AS VARCHAR(50))\t\t\tAS CPN\n\t , CAST(REPLACE(b.CNVX_MID, 'N.A.', 'N/A') AS VARCHAR(50))\t\tAS CNVX_MID\n\t , s1.securitystatuscd\t\t\n\t , S1.CurrencyQuotationCd\t\t\t\t\tAS Currency\n\t , C.CouponType\n\t , b.MTG_FACTOR\t\t\t\t\t\t\t\tAS Factor\n\t , IIF(CASE WHEN LTRIM(RTRIM(s1.InstrumentTypeTier3Nm)) = 'MBS (includes pass-throughs)' THEN s1.InstrumentTypeTier3Nm              \n                        ELSE a.[IndustryGroupName]\n\t\t\t\t\t\tEND = 'ABS', '', CONVERT(VARCHAR(10), DATEADD(dd, -14, b.MTG_FACTOR_SET_DT_PAY_DT), 121))  AS FactorData \t \nFROM [MWR-SRV-EDW\\MSSQLSERVER].DATAMART.[dbo].[SecurityDim] s1 JOIN \n\t\t\t(SELECT s.[SecurityID] \n\t\t\t\t  , s.[SectorID]\n\t\t\t\t  , c.[SectorName]\n\t\t\t\t  , s.[IndustryGroupID]\n\t\t\t\t  , g.[IndustryGroupName]\n\t\t\t FROM [MWR-SRV-SQL3A\\MSSQLSERVER].[APXFirm].[APXUser].[vSecurity] s JOIN [MWR-SRV-SQL3A\\MSSQLSERVER].[APXFirm].[APXUser].[vIndustryGroup] g \n\t\t\t\t\tON g.[IndustryGroupID] = s.[IndustryGroupID]\n\t\t\t\t  JOIN [MWR-SRV-SQL3A\\MSSQLSERVER].[APXFirm].[APXUser].[vIndustrySector] c \n\t\t\t\t\tON c.[SectorID] = s.[SectorID]\n\t\t\t  WHERE 1 = 1\n\t\t\t  AND LTRIM(RTRIM(c.[SectorName])) = 'Securitization' \n\t\t\t  AND LTRIM(RTRIM(g.[IndustryGroupName])) IN ('ABS')\n\t\t\t) a ON s1.[ApxSecuritycd] = a.[SecurityID]\n\tJOIN [dbo].[BloombergData] b \t\t\t\n\t\tON (ltrim(rtrim(b.ID_SEDOL1)) = s1.SEDOLcd)\t\t\t\n\t\t\tAND isnull(s1.SEDOLcd,'') <> '' \n\t\t\tAND b.dateid = @BloombergDate\n\tLEFT JOIN CouponTyp C\n\t\tON S1.SecurityKey = C.SecurityKey\n\tLEFT JOIN [MWR-SRV-CRSQL1A].[CRDMAINMAWIM].dbo.CSM_SECURITY i \n\t\tON i.SEC_ID = s1.CrdSecurityCd\nWHERE 1 = 1\t\t\t\nAND s1.currentind = 'y'\t\t\t\nAND s1.securitystatuscd = 'A'\t\n\nUNION \n\nSELECT CAST(FORMAT(GETDATE() -1, 'yyyyMMdd') AS VARCHAR(8)) AS CurrentDate\t\t\t\n     , REPLACE(i.SEDOL, '.old', '')\t\t\t\t\t\t\tAS InstrumentSEDOL\n\t , REPLACE(i.ISIN_NO, '.old', '')\t\t\t\t\t\tAS InstrumentISIN\n\t , REPLACE(i.CUSIP, '.old', '')\t\t\t\t\t\t\tAS InstrumentCUSIP\n\t , CASE \n            WHEN (i.SEDOL IS NOT NULL AND i.SEDOL != ' ') THEN replace(i.SEDOL, '.old', '') \n            WHEN (i.ISIN_NO IS NOT NULL AND i.ISIN_NO != ' ') THEN replace(i.ISIN_NO, '.old', '') \n            WHEN (i.CUSIP IS NOT NULL AND i.CUSIP != ' ') THEN replace(i.CUSIP, '.old', '') \n            WHEN LEN(i.UDF_CHAR2) > 0 THEN 'MAW' + convert(varchar(5), i.UDF_FLOAT2)   -- Ticket# 5900 (Darren Tan)\n            WHEN i.SEC_NAME LIKE '%Dividend Accrual%' THEN 'DIVA_' + s1.CurrencyQuotationCd\n            WHEN i.UDF_CHAR6 = '40105' -- for Spot rate \n                     THEN 'CASH_' + s1.CurrencyQuotationCd\n            ELSE NULL \n\t\t\tEND\t\t\t\t\t\t\t\t\t\t\t\tAS Symbol\t\t\n\t , s1.SecurityNm\n\t , s1.MawerSecurityNm\n\t , s1.BBIssuerNm\t\t\n\t , b.ISSUER\t\t\n\t , CASE WHEN LTRIM(RTRIM(s1.InstrumentTypeTier3Nm)) = 'MBS (includes pass-throughs)' THEN s1.InstrumentTypeTier3Nm\t\n\t         ELSE a.[IndustryGroupName]\n\t\t\t END\t\t\t\t\t\tAS IndustryGroup\t\n\t , s1.MawerAssetClassTier1Nm\n\t , s1.MawerAssetClassTier2Nm\n\t , s1.MawerAssetClassTier3Nm\t\t\n\t , b.ISSUE_DT\n\t , b.MATURITY \t\t\n\t , b.MTG_WAM\n\t , b.MTG_WALA_CALC\n\t , b.MTG_WACPN\t\t\n\t , REPLACE(b.MTG_PAY_DELAY, 'DAYS', '')\t\tAS MTG_PAY_DELAY\t\t\n\t , b.MTG_WAL\n\t , CAST(REPLACE(b.RTG_DBRS, 'N.A.', 'N/A') AS VARCHAR(50))\t\tAS RTG_DBRS\n\t , CAST(REPLACE(b.RTG_SP, 'N.A.', 'N/A') AS VARCHAR(50))\t\tAS RTG_SP\n\t , CAST(REPLACE(b.RTG_MOODY, 'N.A.', 'N/A') AS VARCHAR(50))\t\tAS RTG_MOODY\n\t , CAST(REPLACE(b.YLD_YTM_MID, 'N.A.', 'N/A') AS VARCHAR(50))\tAS YLD_YTM_MID\n\t , CAST(REPLACE(b.YLD_CUR_MID, 'N.A.', 'N/A') AS VARCHAR(50))\tAS YLD_CUR_MID\n\t , CAST(REPLACE(b.DUR_ADJ_MID, 'N.A.', 'N/A') AS VARCHAR(50))\tAS DUR_ADJ_MID\n\t , CAST(REPLACE(b.CPN, 'N.A.', 'N/A') AS VARCHAR(50))\t\t\tAS CPN\n\t , CAST(REPLACE(b.CNVX_MID, 'N.A.', 'N/A') AS VARCHAR(50))\t\tAS CNVX_MID\n\t , s1.securitystatuscd\t\t\n\t , S1.CurrencyQuotationCd\t\t\t\t\tAS Currency\n\t , C.CouponType\n\t , b.MTG_FACTOR\t\t\t\t\t\t\t\tAS Factor\n\t , IIF(CASE WHEN LTRIM(RTRIM(s1.InstrumentTypeTier3Nm)) = 'MBS (includes pass-throughs)' THEN s1.InstrumentTypeTier3Nm              \n                        ELSE a.[IndustryGroupName]\n\t\t\t\t\t\tEND = 'ABS', '', CONVERT(VARCHAR(10), DATEADD(dd, -14, b.MTG_FACTOR_SET_DT_PAY_DT), 121))  AS FactorData \nFROM   [MWR-SRV-EDW\\MSSQLSERVER].DATAMART.[dbo].[SecurityDim] s1 JOIN \n\t\t\t(SELECT s.[SecurityID] \n\t\t\t\n\t\t\t\t  , s.[SectorID]\n\t\t\t\t  , c.[SectorName]\n\t\t\t\t  , s.[IndustryGroupID]\n\t\t\t\t  , g.[IndustryGroupName]\n\t\t\t FROM [MWR-SRV-SQL3A\\MSSQLSERVER].[APXFirm].[APXUser].[vSecurity] s JOIN [MWR-SRV-SQL3A\\MSSQLSERVER].[APXFirm].[APXUser].[vIndustryGroup] g \n\t\t\t\t\tON g.[IndustryGroupID] = s.[IndustryGroupID]\n\t\t\t\t  JOIN [MWR-SRV-SQL3A\\MSSQLSERVER].[APXFirm].[APXUser].[vIndustrySector] c \n\t\t\t\t\tON c.[SectorID] = s.[SectorID]\n\t\t\t  WHERE 1 = 1\n\t\t\t  AND LTRIM(RTRIM(c.[SectorName])) = 'Securitization' \n\t\t\t  AND LTRIM(RTRIM(g.[IndustryGroupName])) IN ('ABS')\n\t\t\t) a ON s1.[ApxSecuritycd] = a.[SecurityID]\n\tJOIN [dbo].[BloombergData] b \t\t\t\n\t\tON (LTRIM(RTRIM(b.ID_CUSIP)) = s1.CUSIPCd) \t\t\t\n\t\t\tAND ISNULL(s1.CUSIPCd,'') <> ''\n\t\t\tAND b.dateid = @BloombergDate\n\tLEFT JOIN CouponTyp C\n\t\tON S1.SecurityKey = C.SecurityKey\n\tLEFT JOIN [MWR-SRV-CRSQL1A].[CRDMAINMAWIM].dbo.CSM_SECURITY i \n\t\tON i.SEC_ID = s1.CrdSecurityCd\nWHERE 1 = 1\t\t\t\nAND s1.currentind = 'y'\t\t\t\nAND s1.securitystatuscd = 'A'\n", "queryTimeout": "02:00:00", "partitionOption": "None"}, "sink": {"type": "DelimitedTextSink", "storeSettings": {"type": "AzureBlobFSWriteSettings"}, "formatSettings": {"type": "DelimitedTextWriteSettings", "quoteAllText": true, "fileExtension": ".txt"}}, "enableStaging": false, "translator": {"type": "TabularTranslator", "typeConversion": true, "typeConversionSettings": {"allowDataTruncation": true, "treatBooleanAsNumber": false}}}, "inputs": [{"referenceName": "APXSQL", "type": "DatasetReference"}], "outputs": [{"referenceName": "MBSFACT_PIPE", "type": "DatasetReference", "parameters": {"RootFolder": "scdintegration", "FolderPath": "DDI Migration/MBSFactSet/Temp", "FileName": {"value": "@variables('FileName')", "type": "Expression"}}}]}, {"name": "Generate text File", "type": "ExecuteDataFlow", "dependsOn": [{"activity": "Generate source file using data from various DB", "dependencyConditions": ["Succeeded"]}], "policy": {"timeout": "1.00:00:00", "retry": 4, "retryIntervalInSeconds": 1800, "secureOutput": false, "secureInput": false}, "userProperties": [], "typeProperties": {"dataflow": {"referenceName": "MBSFACT_DF", "type": "DataFlowReference", "parameters": {"outPutFileName": {"value": "'@{variables('OutPutFileName')}'", "type": "Expression"}, "inputFileName": {"value": "'@{variables('FileName')}'", "type": "Expression"}}, "datasetParameters": {"SourceFileOfMbsFacts": {"RootFolder": "scdintegration", "FolderPath": "DDI Migration/MBSFactSet/Temp", "FileName": {"value": "@variables('FileName')", "type": "Expression"}}, "GenerateOutputFile": {"Root": "scdintegration", "FolderPath": "DDI Migration/MBSFactSet/OutPutFile"}}}, "compute": {"coreCount": 8, "computeType": "General"}, "traceLevel": "Fine"}}, {"name": "Email", "type": "WebActivity", "dependsOn": [{"activity": "Archive", "dependencyConditions": ["Succeeded"]}], "policy": {"timeout": "7.00:00:00", "retry": 0, "retryIntervalInSeconds": 30, "secureOutput": false, "secureInput": false}, "userProperties": [], "typeProperties": {"method": "POST", "headers": {"Content-Type": "application/json"}, "url": {"value": "@pipeline().globalParameters.SendEmailLogicFunctionUrl", "type": "Expression"}, "body": {"value": "@concat('{\"BodyContents\":\"All, please find attached todays MBS FACT  Reports.\",\"FolderLocation\":\"/scdintegration/DDI Migration/MBSFactSet/OutPutFile/',\n'\",',\n'\"SubjectContents\":\"MBS Factset Security File Downloaded Successfully\",\"ToContents\"',\n':\"',\npipeline().globalParameters.ProdReportEmails,',<EMAIL>,<EMAIL>',\n'\"',\n' }'\n)", "type": "Expression"}}}, {"name": "Archive", "type": "Copy", "dependsOn": [{"activity": "Check Output File existence", "dependencyConditions": ["Succeeded"]}], "policy": {"timeout": "7.00:00:00", "retry": 0, "retryIntervalInSeconds": 30, "secureOutput": false, "secureInput": false}, "userProperties": [], "typeProperties": {"source": {"type": "DelimitedTextSource", "storeSettings": {"type": "AzureBlobFSReadSettings", "recursive": true, "wildcardFolderPath": "DDI Migration/MBSFactSet/OutPutFile", "wildcardFileName": {"value": "@variables('OutPutFileName')", "type": "Expression"}, "enablePartitionDiscovery": false}, "formatSettings": {"type": "DelimitedTextReadSettings"}}, "sink": {"type": "DelimitedTextSink", "storeSettings": {"type": "AzureBlobFSWriteSettings"}, "formatSettings": {"type": "DelimitedTextWriteSettings", "quoteAllText": true, "fileExtension": ".txt"}}, "enableStaging": false, "translator": {"type": "TabularTranslator", "typeConversion": true, "typeConversionSettings": {"allowDataTruncation": true, "treatBooleanAsNumber": false}}}, "inputs": [{"referenceName": "MBSFactOutFile", "type": "DatasetReference", "parameters": {"Root": "scdintegration", "FolderPath": "DDI Migration/MBSFactSet/OutPutFile"}}], "outputs": [{"referenceName": "MBSFACT_PIPE", "type": "DatasetReference", "parameters": {"RootFolder": "scdintegration", "FolderPath": {"value": "@concat('DDI Migration/MBSFactSet/Archive/',utcnow('yyyy-MM-dd'),'/OutPutFile/')\n", "type": "Expression"}, "FileName": {"value": "@variables('OutPutFileName')", "type": "Expression"}}}]}, {"name": "Delete output file", "type": "Delete", "dependsOn": [{"activity": "Email", "dependencyConditions": ["Succeeded"]}], "policy": {"timeout": "7.00:00:00", "retry": 0, "retryIntervalInSeconds": 30, "secureOutput": false, "secureInput": false}, "userProperties": [], "typeProperties": {"dataset": {"referenceName": "MBSFactOutFile", "type": "DatasetReference", "parameters": {"Root": "scdintegration", "FolderPath": "DDI Migration/MBSFactSet/OutPutFile"}}, "enableLogging": false, "storeSettings": {"type": "AzureBlobFSReadSettings", "recursive": true, "wildcardFileName": {"value": "@{variables('OutPutFileName')}", "type": "Expression"}, "enablePartitionDiscovery": false}}}, {"name": "Check Output File existence", "type": "Until", "dependsOn": [{"activity": "Generate text File", "dependencyConditions": ["Succeeded"]}], "userProperties": [], "typeProperties": {"expression": {"value": "@equals(variables('CheckIsFileExist'),true)", "type": "Expression"}, "activities": [{"name": "GetMetadataOfOutputfolder", "type": "GetMetadata", "dependsOn": [], "policy": {"timeout": "7.00:00:00", "retry": 0, "retryIntervalInSeconds": 30, "secureOutput": false, "secureInput": false}, "userProperties": [], "typeProperties": {"dataset": {"referenceName": "MBSFACT_PIPE", "type": "DatasetReference", "parameters": {"RootFolder": "scdintegration", "FolderPath": "DDI Migration/MBSFactSet/OutPutFile", "FileName": {"value": "@variables('OutPutFileName')", "type": "Expression"}}}, "fieldList": ["exists"], "storeSettings": {"type": "AzureBlobFSReadSettings", "recursive": true, "enablePartitionDiscovery": false}, "formatSettings": {"type": "DelimitedTextReadSettings"}}}, {"name": "If Condition1", "type": "IfCondition", "dependsOn": [{"activity": "GetMetadataOfOutputfolder", "dependencyConditions": ["Succeeded"]}], "userProperties": [], "typeProperties": {"expression": {"value": "@activity('GetMetadataOfOutputfolder').output.exists", "type": "Expression"}, "ifFalseActivities": [{"name": "SetFileExist", "type": "SetVariable", "dependsOn": [], "policy": {"secureOutput": false, "secureInput": false}, "userProperties": [], "typeProperties": {"variableName": "CheckIsFileExist", "value": {"value": "@bool(false)", "type": "Expression"}}}], "ifTrueActivities": [{"name": "FileExist", "type": "SetVariable", "dependsOn": [], "policy": {"secureOutput": false, "secureInput": false}, "userProperties": [], "typeProperties": {"variableName": "CheckIsFileExist", "value": {"value": "@bool(true)", "type": "Expression"}}}]}}], "timeout": "0.00:01:30"}}], "variables": {"FileName": {"type": "String", "defaultValue": "mbsfactset.csv"}, "OutPutFileName": {"type": "String", "defaultValue": "SecuritizationDailyOutput.txt"}, "CheckIsFileExist": {"type": "Boolean", "defaultValue": false}}, "folder": {"name": "Internal/DDI/IN-329 DDI Migration/IN-330 MBSFact Sets"}, "annotations": [], "lastPublishTime": "2023-12-01T04:11:27Z"}, "type": "Microsoft.DataFactory/factories/pipelines"}