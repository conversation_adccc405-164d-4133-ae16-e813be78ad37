{"name": "DECOM- IN-370 CoppClark Holiday Calendar - CHILD", "properties": {"description": "The pipeline cleans ADL's TEMP folder.", "activities": [{"name": "Clean TEMP", "type": "Delete", "dependsOn": [], "policy": {"timeout": "7.00:00:00", "retry": 0, "retryIntervalInSeconds": 30, "secureOutput": false, "secureInput": false}, "userProperties": [], "typeProperties": {"dataset": {"referenceName": "AzureDataLakeStorage_CoppClark_Dir", "type": "DatasetReference", "parameters": {"dir_name": "TEMP"}}, "enableLogging": false, "storeSettings": {"type": "AzureBlobFSReadSettings", "recursive": true, "enablePartitionDiscovery": false}}}], "folder": {"name": "Internal/Copp Clark Holiday Calendar"}, "annotations": [], "lastPublishTime": "2023-12-01T04:11:17Z"}, "type": "Microsoft.DataFactory/factories/pipelines"}