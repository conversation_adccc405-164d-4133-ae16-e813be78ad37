{"name": "SQLMI_Billing", "properties": {"linkedServiceName": {"referenceName": "SQLMI_RESEARCH", "type": "LinkedServiceReference", "parameters": {"DBName": "Billing"}}, "parameters": {"SchemaName": {"type": "string"}, "TableName": {"type": "string"}}, "folder": {"name": "Inbound/Billing and QFM"}, "annotations": [], "type": "AzureSqlMITable", "schema": [], "typeProperties": {"schema": {"value": "@dataset().SchemaName", "type": "Expression"}, "table": {"value": "@dataset().TableName", "type": "Expression"}}}, "type": "Microsoft.DataFactory/factories/datasets"}