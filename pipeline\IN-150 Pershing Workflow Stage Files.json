{"name": "IN-150 Pershing Workflow Stage Files", "properties": {"activities": [{"name": "Download All Files", "type": "Copy", "dependsOn": [], "policy": {"timeout": "7.00:00:00", "retry": 0, "retryIntervalInSeconds": 30, "secureOutput": false, "secureInput": false}, "userProperties": [], "typeProperties": {"source": {"type": "BinarySource", "storeSettings": {"type": "SftpReadSettings", "recursive": true, "wildcardFolderPath": "Inbox", "wildcardFileName": "*.ZIP", "deleteFilesAfterCompletion": false, "disableChunking": false}, "formatSettings": {"type": "BinaryReadSettings"}}, "sink": {"type": "BinarySink", "storeSettings": {"type": "AzureBlobFSWriteSettings"}}, "enableStaging": false}, "inputs": [{"referenceName": "SFTP_Pershing", "type": "DatasetReference"}], "outputs": [{"referenceName": "ADL_Binary_Dir", "type": "DatasetReference", "parameters": {"Path": {"value": "@pipeline().parameters.pADLPath", "type": "Expression"}}}]}, {"name": "Send Email with Attachment", "description": "Send email with attachment from ADL", "type": "WebActivity", "dependsOn": [{"activity": "Download All Files", "dependencyConditions": ["Succeeded"]}], "policy": {"timeout": "7.00:00:00", "retry": 0, "retryIntervalInSeconds": 30, "secureOutput": false, "secureInput": false}, "userProperties": [], "typeProperties": {"method": "POST", "url": {"value": "@pipeline().globalParameters.SendEmailLogicFunctionUrl", "type": "Expression"}, "body": {"value": "@concat('{\"BodyContents\":\"Pershing FX Files downloaded successfully from SFTP!\"',\n', \"SubjectContents\":\"Pershing FX Files (SFTP)\", \"ToContents\":\"',pipeline().parameters.pRecipientEmail,'\", \"FolderLocation\":\"', concat('/scdintegration/', pipeline().parameters.pADLPath), '\"}')", "type": "Expression"}}}, {"name": "ReadInputFilesMetaData", "type": "GetMetadata", "dependsOn": [{"activity": "Download All Files", "dependencyConditions": ["Succeeded"]}], "policy": {"timeout": "0.12:00:00", "retry": 0, "retryIntervalInSeconds": 30, "secureOutput": false, "secureInput": false}, "userProperties": [], "typeProperties": {"dataset": {"referenceName": "ADL_Binary_Dir", "type": "DatasetReference", "parameters": {"Path": {"value": "@pipeline().parameters.pADLPath", "type": "Expression"}}}, "fieldList": ["childItems"], "storeSettings": {"type": "AzureBlobFSReadSettings", "recursive": true, "enablePartitionDiscovery": false}, "formatSettings": {"type": "BinaryReadSettings"}}}, {"name": "Loop Files", "type": "ForEach", "dependsOn": [{"activity": "ReadInputFilesMetaData", "dependencyConditions": ["Succeeded"]}], "userProperties": [], "typeProperties": {"items": {"value": "@activity('ReadInputFilesMetaData').output.childItems", "type": "Expression"}, "isSequential": true, "activities": [{"name": "Unzip zipped file", "type": "Copy", "dependsOn": [], "policy": {"timeout": "0.12:00:00", "retry": 0, "retryIntervalInSeconds": 30, "secureOutput": false, "secureInput": false}, "userProperties": [], "typeProperties": {"source": {"type": "BinarySource", "storeSettings": {"type": "AzureBlobFSReadSettings", "recursive": true}, "formatSettings": {"type": "BinaryReadSettings", "compressionProperties": {"type": "ZipDeflateReadSettings", "preserveZipFileNameAsFolder": false}}}, "sink": {"type": "BinarySink", "storeSettings": {"type": "AzureBlobFSWriteSettings"}}, "enableStaging": false}, "inputs": [{"referenceName": "ADL_Binary_File_ZIP", "type": "DatasetReference", "parameters": {"FileName": {"value": "@item().name", "type": "Expression"}, "Path": {"value": "@pipeline().parameters.pADLPath", "type": "Expression"}, "Container": "scdintegration"}}], "outputs": [{"referenceName": "ADL_Binary_File", "type": "DatasetReference", "parameters": {"FileName": {"value": "@concat(pipeline().parameters.pDate,\nsubstring(item().name,7,4))", "type": "Expression"}, "Path": {"value": "@replace(pipeline().parameters.pADLPath,'Zipped','Unzipped')", "type": "Expression"}, "Container": "scdintegration"}}]}, {"name": "Stage to SQLMI", "type": "Copy", "dependsOn": [{"activity": "Unzip zipped file", "dependencyConditions": ["Succeeded"]}], "policy": {"timeout": "0.12:00:00", "retry": 0, "retryIntervalInSeconds": 30, "secureOutput": false, "secureInput": false}, "userProperties": [], "typeProperties": {"source": {"type": "DelimitedTextSource", "additionalColumns": [{"name": "DataDate", "value": {"value": "@pipeline().parameters.pDate", "type": "Expression"}}, {"name": "FileName", "value": {"value": "@item().name", "type": "Expression"}}, {"name": "DataType", "value": {"value": "@concat('Prsh',substring(item().name,7,4))", "type": "Expression"}}], "storeSettings": {"type": "AzureBlobFSReadSettings", "recursive": true, "enablePartitionDiscovery": false}, "formatSettings": {"type": "DelimitedTextReadSettings"}}, "sink": {"type": "SqlMISink", "preCopyScript": {"value": "@{concat('DELETE FROM [Integration].[General].[InputFileStaging] \nWHERE [DataDate] = ''',pipeline().parameters.pDate,\n''' AND [DataType] = ''',\nconcat('Prsh',substring(item().name,7,4)),'''')}", "type": "Expression"}, "writeBehavior": "insert", "sqlWriterUseTableLock": false}, "enableStaging": false, "translator": {"type": "TabularTranslator", "mappings": [{"source": {"type": "String", "ordinal": 1}, "sink": {"name": "<PERSON><PERSON><PERSON><PERSON>", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}, {"source": {"name": "DataDate", "type": "String"}, "sink": {"name": "DataDate", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}, {"source": {"name": "FileName", "type": "String"}, "sink": {"name": "FileName", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}, {"source": {"name": "DataType", "type": "String"}, "sink": {"name": "DataType", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}], "typeConversion": true, "typeConversionSettings": {"allowDataTruncation": true, "treatBooleanAsNumber": false}}}, "inputs": [{"referenceName": "ADL_Delimited_File_NoQuote", "type": "DatasetReference", "parameters": {"FilePath": {"value": "@replace(pipeline().parameters.pADLPath,'Zipped','Unzipped')", "type": "Expression"}, "FileName": {"value": "@concat(pipeline().parameters.pDate,\nsubstring(item().name,7,4))", "type": "Expression"}, "ColDelimiter": "|||", "FirstRowHeader": false}}], "outputs": [{"referenceName": "SQLMI_MOW_PARAM", "type": "DatasetReference", "parameters": {"DBName": "Integration", "SchemaName": "General", "TableName": "InputFileStaging"}}]}]}}], "parameters": {"pRecipientEmail": {"type": "string", "defaultValue": "<EMAIL>"}, "pDate": {"type": "string"}, "pADLPath": {"type": "string"}}, "folder": {"name": "Custodians and Vendors/Pershing/Pershing Workflow"}, "annotations": [], "lastPublishTime": "2024-08-14T21:31:32Z"}, "type": "Microsoft.DataFactory/factories/pipelines"}