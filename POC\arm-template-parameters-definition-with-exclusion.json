{"Microsoft.DataFactory/factories": {"properties": {"globalParameters": {"*": {"value": "="}}, "globalConfigurations": {"*": "="}, "encryption": {"*": "=", "identity": {"*": "="}}}, "location": "=", "identity": {"type": "=", "userAssignedIdentities": "="}}, "Microsoft.DataFactory/factories/pipelines": {"*": {"properties": {"activities": "=", "parameters": "=", "variables": "=", "concurrency": "=", "annotations": "=", "lastPublishTime": "=", "folder": "=", "description": "="}}, "DEV-ONLY-*": {"condition": "[equals(parameters('environment'), 'dev')]", "properties": {"activities": "=", "parameters": "=", "variables": "=", "concurrency": "=", "annotations": "=", "lastPublishTime": "=", "folder": "=", "description": "="}}}, "Microsoft.DataFactory/factories/triggers": {"*": {"properties": {"pipelines": "=", "type": "=", "typeProperties": "=", "annotations": "=", "description": "=", "runtimeState": "="}}, "DEV-ONLY-*": {"condition": "[equals(parameters('environment'), 'dev')]", "properties": {"pipelines": "=", "type": "=", "typeProperties": "=", "annotations": "=", "description": "=", "runtimeState": "="}}}, "Microsoft.DataFactory/factories/linkedServices": {"*": {"properties": {"typeProperties": {"accountName": "=", "accountEndpoint": "=", "username": "=", "userName": "=", "accessKeyId": "=", "endpoint": "=", "servicePrincipalId": "=", "userId": "=", "host": "=", "clientId": "=", "existingClusterId": "=", "clusterUserName": "=", "clusterSshUserName": "=", "hostSubscriptionId": "=", "clusterResourceGroup": "=", "subscriptionId": "=", "resourceGroupName": "=", "tenant": "=", "dataLakeStoreUri": "=", "baseUrl": "=", "database": "=", "serviceEndpoint": "=", "batchUri": "=", "poolName": "=", "databaseName": "=", "systemNumber": "=", "server": "=", "url": "=", "functionAppUrl": "=", "environmentUrl": "=", "aadResourceId": "=", "sasUri": "|:-sasUri:secureString", "sasToken": "|", "connectionString": "|:-connectionString:secureString", "hostKeyFingerprint": "=", "mlWorkspaceName": "="}}}, "Odbc": {"properties": {"typeProperties": {"userName": "=", "connectionString": {"secretName": "="}}}}}, "Microsoft.DataFactory/factories/datasets": {"*": {"properties": {"typeProperties": {"folderPath": "=", "fileName": "="}}}}, "Microsoft.DataFactory/factories/credentials": {"*": {"properties": {"typeProperties": {"token": "="}}}}, "Microsoft.DataFactory/factories/managedVirtualNetworks/managedPrivateEndpoints": {"properties": {"privateLinkResourceId": "=", "groupId": "=", "fqdns": "="}}, "Microsoft.DataFactory/factories/globalparameters": {"properties": {"*": {"value": "="}}}}