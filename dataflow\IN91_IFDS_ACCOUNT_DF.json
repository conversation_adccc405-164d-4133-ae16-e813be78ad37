{"name": "IN91_IFDS_ACCOUNT_DF", "properties": {"folder": {"name": "Inbound"}, "type": "MappingDataFlow", "typeProperties": {"sources": [{"dataset": {"referenceName": "AzurDataLakeStorage_IFDS_Account_RAW", "type": "DatasetReference"}, "name": "RawFile"}], "sinks": [{"dataset": {"referenceName": "AzurDataLakeStorage_IFDS_Account_Temp", "type": "DatasetReference"}, "name": "OutPutFile"}], "transformations": [{"name": "DerivedTwoColumn"}, {"name": "SelectedColumn"}, {"name": "AddedDriftedColumn", "description": "Creates an explicit mapping for each drifted column"}], "script": "parameters{\n\tRawFile as string,\n\tOutFile as string,\n\tADLFilesDirectory as string,\n\tDate as string\n}\nsource(output(\n\t\t{Record Type} as string,\n\t\t{Account Number} as string,\n\t\t{Shareholder Number} as string,\n\t\t{Alt Account Number} as string,\n\t\t{Account Other Reference Number} as string,\n\t\t{Open Date} as string,\n\t\t{Registered Name Line 1} as string,\n\t\t{Registered Name Line 2} as string,\n\t\t{Registered Owner Address Line 1} as string,\n\t\t{Registered Owner Address Line 2} as string,\n\t\t{Registered Owner Address Line 3} as string,\n\t\t{Registered Owner Address Line 4} as string,\n\t\t{Registered Owner Address Line 5} as string,\n\t\t{Registered Owner ProvinceORState} as string,\n\t\t{Registered Owner Postal Code} as string,\n\t\t{Registered Owner Country} as string,\n\t\t{Dealer Code} as string,\n\t\t{Dealer Branch} as string,\n\t\t{Agent Code} as string,\n\t\t{Statement Mailing Name Line 1} as string,\n\t\t{Statement Mailing Name Line 2} as string,\n\t\t{Statement Address Line 1} as string,\n\t\t{Statement Address Line 2} as string,\n\t\t{Statement Address Line 3} as string,\n\t\t{Statement Address Line 4} as string,\n\t\t{Statement Address Line 5} as string,\n\t\t{Statement Province State} as string,\n\t\t{Statement Postal Code} as string,\n\t\t{Statement Country} as string,\n\t\t{Statement Hold Flag} as string,\n\t\t{Acct Owner Salutation 1} as string,\n\t\t{Acct Owner Last Name 1} as string,\n\t\t{Acct Owner First Name 1} as string,\n\t\t{Acct Owner Birth Date 1} as string,\n\t\t{Acct Owner SIN 1} as string,\n\t\t{Acct Owner Sex 1} as string,\n\t\t{Acct Owner Lang 1} as string,\n\t\t{Acct Owner Home Phone 1} as string,\n\t\t{Acct Owner Bus Phone 1} as string,\n\t\t{Acct Owner Bus Fax 1} as string,\n\t\t{Acct Owner Salutation 2} as string,\n\t\t{Acct Owner Last Name 2} as string,\n\t\t{Acct Owner First Name 2} as string,\n\t\t{Acct Owner Birth Date 2} as string,\n\t\t{Acct Owner SIN 2} as string,\n\t\t{Acct Owner Sex 2} as string,\n\t\t{Acct Owner Lang 2} as string,\n\t\t{Acct Owner Home Phone 2} as string,\n\t\t{Acct Owner Bus Phone 2} as string,\n\t\t{Acct Owner Bus Fax 2} as string,\n\t\t{Acct Owner Salutation 3} as string,\n\t\t{Acct Owner Last Name 3} as string,\n\t\t{Acct Owner First Name 3} as string,\n\t\t{Acct Owner Birth Date 3} as string,\n\t\t{Acct Owner SIN 3} as string,\n\t\t{Acct Owner Sex 3} as string,\n\t\t{Acct Owner Lang 3} as string,\n\t\t{Acct Owner Home Phone 3} as string,\n\t\t{Acct Owner Bus Phone 3} as string,\n\t\t{Acct Owner Bus Fax 3} as string,\n\t\t{Acct Owner Salutation 4} as string,\n\t\t{Acct Owner Last Name 4} as string,\n\t\t{Acct Owner First Name 4} as string,\n\t\t{Acct Owner Birth Date 4} as string,\n\t\t{Acct Owner SIN 4} as string,\n\t\t{Acct Owner Sex 4} as string,\n\t\t{Acct Owner Lang 4} as string,\n\t\t{Acct Owner Home Phone 4} as string,\n\t\t{Acct Owner Bus Phone 4} as string,\n\t\t{Acct Owner Bus Fax 4} as string,\n\t\t{Beneficiary Last Name 1} as string,\n\t\t{Beneficiary First Name 1} as string,\n\t\t{Beneficiary Percentage 1} as string,\n\t\t{Beneficiary Relation 1} as string,\n\t\t{Beneficiary Last Name 2} as string,\n\t\t{Beneficiary First Name 2} as string,\n\t\t{Beneficiary Percentage 2} as string,\n\t\t{Beneficiary Relation 2} as string,\n\t\t{Beneficiary Last Name 3} as string,\n\t\t{Beneficiary First Name 3} as string,\n\t\t{Beneficiary Percentage 3} as string,\n\t\t{Beneficiary Relation 3} as string,\n\t\t{Account Tax Type Code} as string,\n\t\t{Account Type Code} as string,\n\t\t{Account Currency} as string,\n\t\t{Account Status Code} as string,\n\t\t{Bank Code} as string,\n\t\t{Bank Acct Number} as string,\n\t\t{Bank Transit Number} as string,\n\t\t{Fund Company Code} as string,\n\t\t{RRIF Payment Frequency} as string,\n\t\t{Tax Jurisdiction} as string,\n\t\t{RRIF Payment Option} as string,\n\t\t{RRIF Effective Date} as string,\n\t\t{RRIF Minimum Payment} as string,\n\t\t{RRIF Maximum Payment} as string,\n\t\t{RRIF Gross Payment} as string,\n\t\t{RRIF Net Amount} as string,\n\t\t{RRIF GrossNet Indicator} as string,\n\t\t{RRIF Index Rate} as string,\n\t\t{Free Amount} as string,\n\t\t{RRIF Year To Date Redemption} as string,\n\t\t{RRIF Minimum PaidLeft} as string,\n\t\t{RRIF Current Year Minimum Schedule} as string,\n\t\t{Paying Agent First Name} as string,\n\t\t{Paying Agent Last Name} as string,\n\t\t{Paying Agent Address Line 1} as string,\n\t\t{Paying Agent Address Line 2} as string,\n\t\t{Paying Agent Address Line 3} as string,\n\t\t{Paying Agent Address Line 4} as string,\n\t\t{Paying Agent Address Line 5} as string,\n\t\t{Paying Agent City} as string,\n\t\t{Paying Agent ProvinceORState} as string,\n\t\t{Paying Agent Postal} as string,\n\t\t{Paying Agent Country} as string,\n\t\t{Paying Agent Tax Jurisdiction} as string,\n\t\t{Paying Agent EUSD Option} as string,\n\t\t{Acct owner Tax Exemption Expiry Date 1} as string,\n\t\t{Acct owner Certificate number 1} as string,\n\t\t{Acct owner Exemption reason code 1} as string,\n\t\t{Acct owner EUSD Option 1} as string,\n\t\t{Acct owner Tax Exemption Expiry Date 2} as string,\n\t\t{Acct owner Certificate number 2} as string,\n\t\t{Acct owner Exemption reason code 2} as string,\n\t\t{Acct owner EUSD Option 2} as string,\n\t\t{Acct owner Tax Exemption Expiry Date 3} as string,\n\t\t{Acct owner Certificate number 3} as string,\n\t\t{Acct owner Exemption reason code 3} as string,\n\t\t{Acct owner EUSD Option 3} as string,\n\t\t{Acct owner Tax Exemption Expiry Date 4} as string,\n\t\t{Acct owner Certificate number 4} as string,\n\t\t{Acct owner Exemption reason code 4} as string,\n\t\t{Acct owner EUSD Option 4} as string,\n\t\t{Entity ID 1} as string,\n\t\t{Entity Type 1} as string,\n\t\t{vAuthorizedTrade: string} as string,\n\t\t{Nationality: string} as string,\n\t\t{BirthPlace: string} as string,\n\t\t{EMail: string} as string,\n\t\t{Entity ID 2} as string,\n\t\t{Entity Type 2} as string,\n\t\t{Authorized To Trade 2} as string,\n\t\t{Nationality 2} as string,\n\t\t{BirthPlace 2} as string,\n\t\t{EMail 2} as string,\n\t\t{Entity ID 3} as string,\n\t\t{Entity Type 3} as string,\n\t\t{Authorized To Trade 3} as string,\n\t\t{Nationality 3} as string,\n\t\t{BirthPlace 3} as string,\n\t\t{EMail 3} as string,\n\t\t{Entity ID 4} as string,\n\t\t{Entity Type 4} as string,\n\t\t{Authorized To Trade 4} as string,\n\t\t{Nationality 4} as string,\n\t\t{BirthPlace 4} as string,\n\t\t{EMail 4} as string,\n\t\t{Registration Complete} as string,\n\t\t{Deff: string} as string,\n\t\t{Purchase Stop} as string,\n\t\t{Redemption Stop} as string,\n\t\t{Stop Redemption Settle} as string,\n\t\t{Transfer Out Stop} as string,\n\t\t{Transfer In Stop} as string,\n\t\t{Stop Foreign Content Rebal} as string,\n\t\t{Stop AMS Rebal} as string,\n\t\t{Bank Type 1} as string,\n\t\t{Bank Use 1} as string,\n\t\t{Institution Code 1} as string,\n\t\t{Bank Acct Number 1} as string,\n\t\t{Transit No 1} as string,\n\t\t{Bank Name 1} as string,\n\t\t{Corr Bank No 1} as string,\n\t\t{Corr Bank Name 1} as string,\n\t\t{Corr BankAddr1 1} as string,\n\t\t{Corr BankAddr2 1} as string,\n\t\t{Corr BankAddr3 1} as string,\n\t\t{Corr BankAddr4 1} as string,\n\t\t{Corr BankAddr5 1} as string,\n\t\t{Corr Bank Postal Code 1} as string,\n\t\t{Corr Bank Country 1} as string,\n\t\t{Credit Information 1} as string,\n\t\t{Instruction Line1 1} as string,\n\t\t{Instruction Line2 1} as string,\n\t\t{Bank Type 2} as string,\n\t\t{Bank Use 2} as string,\n\t\t{Institution Code 2} as string,\n\t\t{Bank Acct Number 2} as string,\n\t\t{Transit No 2} as string,\n\t\t{Bank Name 2} as string,\n\t\t{Corr Bank No 2} as string,\n\t\t{Corr Bank Name 2} as string,\n\t\t{Corr BankAddr1 2} as string,\n\t\t{Corr BankAddr2 2} as string,\n\t\t{Corr BankAddr3 2} as string,\n\t\t{Corr BankAddr4 2} as string,\n\t\t{Corr BankAddr5 2} as string,\n\t\t{Corr Bank Postal Code 2} as string,\n\t\t{Corr Bank Country 2} as string,\n\t\t{Credit Information 2} as string,\n\t\t{Instruction Line1 2} as string,\n\t\t{Instruction Line2 2} as string,\n\t\t{Bank Type 3} as string,\n\t\t{Bank Use 3} as string,\n\t\t{Institution Code 3} as string,\n\t\t{Bank Acct Number 3} as string,\n\t\t{Transit No 3} as string,\n\t\t{Bank Name 3} as string,\n\t\t{Corr Bank No 3} as string,\n\t\t{Corr Bank Name 3} as string,\n\t\t{Corr BankAddr1 3} as string,\n\t\t{Corr BankAddr2 3} as string,\n\t\t{Corr BankAddr3 3} as string,\n\t\t{Corr BankAddr4 3} as string,\n\t\t{Corr BankAddr5 3} as string,\n\t\t{Corr Bank Postal Code 3} as string,\n\t\t{Corr Bank Country 3} as string,\n\t\t{Credit Information 3} as string,\n\t\t{Instruction Line1 3} as string,\n\t\t{Instruction Line2 3} as string,\n\t\t{Bank Type 4} as string,\n\t\t{Bank Use 4} as string,\n\t\t{Institution Code 4} as string,\n\t\t{Bank Acct Number 4} as string,\n\t\t{Transit No 4} as string,\n\t\t{Bank Name 4} as string,\n\t\t{Corr Bank No 4} as string,\n\t\t{Corr Bank Name 4} as string,\n\t\t{Corr BankAddr1 4} as string,\n\t\t{Corr BankAddr2 4} as string,\n\t\t{Corr BankAddr3 4} as string,\n\t\t{Corr BankAddr4 4} as string,\n\t\t{Corr BankAddr5 4} as string,\n\t\t{Corr Bank Postal Code 4} as string,\n\t\t{Corr Bank Country 4} as string,\n\t\t{Credit Information 4} as string,\n\t\t{Instruction Line1 4} as string,\n\t\t{Instruction Line2 4} as string,\n\t\t{Dealer Other IDs} as string,\n\t\t{Branch Other IDs} as string,\n\t\t{Rep Other IDs} as string,\n\t\t{AO Country Of Birth} as string,\n\t\t{JO Country Of Birth 1} as string,\n\t\t{JO Country Of Birth 2} as string,\n\t\t{JO Country Of Birth 3} as string,\n\t\t{Social Code} as string,\n\t\t{Social Code Description} as string,\n\t\t{Account Category Code 1} as string,\n\t\t{Account Category Code 1 Description} as string,\n\t\t{Account Category Code 2} as string,\n\t\t{Account Category Code 2 Description} as string,\n\t\t{Account Category Code 3} as string,\n\t\t{Account Category Code 3 Description} as string,\n\t\t{Account Category Code 4} as string,\n\t\t{Account Category Code 4 Description} as string,\n\t\t{Account Category Code 5} as string,\n\t\t{Account Category Code 5 Description} as string,\n\t\t{Shareholder Group} as string,\n\t\t{Account Clearing ID} as string,\n\t\t{Management Settlement Location} as string,\n\t\t{Management Settlement Account} as string,\n\t\t{Management Default Setting} as string,\n\t\t{Account Settlement Instruction Code 1} as string,\n\t\t{Accont Settlement Instruction Delivery To 1} as string,\n\t\t{Account Settlement Instruction for Account 1} as string,\n\t\t{Account Settlement Instruction in Favour of 1} as string,\n\t\t{Account Settlement Instruction Default Setting 1} as string,\n\t\t{Account Settlement Instruction Code 2} as string,\n\t\t{Accont Settlement Instruction Delivery To 2} as string,\n\t\t{Account Settlement Instruction for Account 2} as string,\n\t\t{Account Settlement Instruction in Favour of 2} as string,\n\t\t{Account Settlement Instruction Default Setting 2} as string,\n\t\t{Account Settlement Instruction Code 3} as string,\n\t\t{Accont Settlement Instruction Delivery To 3} as string,\n\t\t{Account Settlement Instruction for Account 3} as string,\n\t\t{Account Settlement Instruction in Favour of 3} as string,\n\t\t{Account Settlement Instruction Default Setting 3} as string,\n\t\t{Account Settlement Instruction Code 4} as string,\n\t\t{Accont Settlement Instruction Delivery To 4} as string,\n\t\t{Account Settlement Instruction for Account 4} as string,\n\t\t{Account Settlement Instruction in Favour of 4} as string,\n\t\t{Account Settlement Instruction Default Setting 4} as string,\n\t\t{Account Disposition Code} as string,\n\t\t{CIF Number} as string,\n\t\t{Intermediary Account Identifier} as string,\n\t\t{Assigned Flag} as string,\n\t\t{RRIF Periodic Mandatory Payment Amount} as string,\n\t\t{RRIF Override Mandatory Flag} as string,\n\t\t{RRIF Qualified Flag} as string,\n\t\t{RRIF Spouse DateOfBirth} as string,\n\t\t{RRIF Use Spouse Date of Birth} as string,\n\t\t{Nonresident Agreement Flag} as string,\n\t\t{Account Designation} as string,\n\t\t{Sibling Only} as string,\n\t\t{Pension Jurisdiction} as string,\n\t\t{Tax Jurisdiction1} as string,\n\t\t{Status of Management Settlement Location} as string,\n\t\t{Status Date Change} as string,\n\t\t{Acct Admin First Name} as string,\n\t\t{Acct Admin Last Name} as string,\n\t\t{Acct Admin First Name1} as string,\n\t\t{Acct Admin Last Name1} as string,\n\t\t{Acct Admin First Name2} as string,\n\t\t{Acct Admin Last Name2} as string,\n\t\t{Acct Admin First Name3} as string,\n\t\t{Acct Admin Last Name3} as string,\n\t\t{Stop Reason} as string,\n\t\t{Other Stop Reason} as string,\n\t\t{Clearing Code} as string,\n\t\t{Clearing Description} as string,\n\t\t{Clearing Code1} as string,\n\t\t{Clearing Description1} as string,\n\t\t{Clearing Code2} as string,\n\t\t{Clearing Description2} as string,\n\t\t{Clearing Code3} as string,\n\t\t{Clearing Description3} as string,\n\t\t{Clearing Code4} as string,\n\t\t{Clearing Description4} as string,\n\t\t{Tax Type} as string,\n\t\t{EPA: string} as string,\n\t\t{Tax Identification Number} as string,\n\t\t{Territory Code} as string,\n\t\t{Tax Jurisdiction ISO3} as string,\n\t\t{Residence Province ISO3} as string,\n\t\t{Organization Type} as string,\n\t\t{Sub Organization Type} as string,\n\t\t{UUID: string} as string,\n\t\t{GUID: string} as string,\n\t\t{Product Portfolio Name} as string,\n\t\t{Product Portfolio Code} as string,\n\t\t{Product Portfolio Plan Name} as string,\n\t\t{Product Portfolio Plan Code} as string,\n\t\t{NonResident Effective Date} as string,\n\t\t{NonResident Stop Date} as string,\n\t\t{NonResident Tax Jurisdiction} as string,\n\t\t{National ID} as string,\n\t\t{Country of Issued National ID} as string,\n\t\t{Passport Number} as string,\n\t\t{Country of Issued Passport} as string,\n\t\t{Settlement Currency} as string,\n\t\t{Pay Type Redemption} as string,\n\t\t{Pay Type Dividend} as string,\n\t\t{Pay Type Purchase} as string,\n\t\t{Registration Agent Number} as string,\n\t\t{CPFB Account Number} as string,\n\t\t{Second Phone Number} as string,\n\t\t{Second Phone Number Extension} as string,\n\t\t{EntityTypeSeq: string} as string,\n\t\t{Joint EntityTypeSeq1} as string,\n\t\t{Joint EntityTypeSeq2} as string,\n\t\t{Joint EntityTypeSeq3} as string,\n\t\t{Business Number} as string,\n\t\t{In Trust For} as string,\n\t\t{Intermediary: string} as string,\n\t\t{Schedule: string} as string,\n\t\t{Affluent: string} as string,\n\t\t{Trust Number} as string,\n\t\t{Access ID} as string,\n\t\t{FAST Client xRef Num} as string,\n\t\t{iFAST SICAV Client xRef Num} as string,\n\t\t{iFAST OEIC Client xRef Num} as string,\n\t\t{PSD Account Exemption} as string,\n\t\t{RDR Applicable} as string,\n\t\t{Payment Routing Method} as string,\n\t\t{Registration Information} as string,\n\t\t{Investor Classification} as string,\n\t\t{Investor Categorization} as string,\n\t\t{Hold Dividend Payment} as string,\n\t\t{Aggregation Mode} as string,\n\t\tTerminateDate as string\n\t),\n\tallowSchemaDrift: true,\n\tvalidateSchema: false,\n\tignoreNoFilesFound: false,\n\twildcardPaths:[(concat($ADLFilesDirectory, '/',$RawFile))]) ~> RawFile\nRawFile derive(ID = uuid(),\n\t\tLoaded = toTimestamp($Date,'yyyyMMdd')) ~> DerivedTwoColumn\nAddedDriftedColumn select(mapColumn(\n\t\t{Record Type},\n\t\t{Account Number},\n\t\t{Shareholder Number},\n\t\t{Alt Account Number},\n\t\t{Account Other Reference Number},\n\t\t{Open Date},\n\t\t{Registered Name Line 1},\n\t\t{Registered Name Line 2},\n\t\t{Registered Owner Address Line 1},\n\t\t{Registered Owner Address Line 2},\n\t\t{Registered Owner Address Line 3},\n\t\t{Registered Owner Address Line 4},\n\t\t{Registered Owner Address Line 5},\n\t\t{Registered Owner ProvinceORState},\n\t\t{Registered Owner Postal Code},\n\t\t{Registered Owner Country},\n\t\t{Dealer Code},\n\t\t{Dealer Branch},\n\t\t{Agent Code},\n\t\t{Statement Mailing Name Line 1},\n\t\t{Statement Mailing Name Line 2},\n\t\t{Statement Address Line 1},\n\t\t{Statement Address Line 2},\n\t\t{Statement Address Line 3},\n\t\t{Statement Address Line 4},\n\t\t{Statement Address Line 5},\n\t\t{Statement Province State},\n\t\t{Statement Postal Code},\n\t\t{Statement Country},\n\t\t{Statement Hold Flag},\n\t\t{Acct Owner Salutation 1},\n\t\t{Acct Owner Last Name 1},\n\t\t{Acct Owner First Name 1},\n\t\t{Acct Owner Birth Date 1},\n\t\t{Acct Owner SIN 1},\n\t\t{Acct Owner Sex 1},\n\t\t{Acct Owner Lang 1},\n\t\t{Acct Owner Home Phone 1},\n\t\t{Acct Owner Bus Phone 1},\n\t\t{Acct Owner Bus Fax 1},\n\t\t{Acct Owner Salutation 2},\n\t\t{Acct Owner Last Name 2},\n\t\t{Acct Owner First Name 2},\n\t\t{Acct Owner Birth Date 2},\n\t\t{Acct Owner SIN 2},\n\t\t{Acct Owner Sex 2},\n\t\t{Acct Owner Lang 2},\n\t\t{Acct Owner Home Phone 2},\n\t\t{Acct Owner Bus Phone 2},\n\t\t{Acct Owner Bus Fax 2},\n\t\t{Acct Owner Salutation 3},\n\t\t{Acct Owner Last Name 3},\n\t\t{Acct Owner First Name 3},\n\t\t{Acct Owner Birth Date 3},\n\t\t{Acct Owner SIN 3},\n\t\t{Acct Owner Sex 3},\n\t\t{Acct Owner Lang 3},\n\t\t{Acct Owner Home Phone 3},\n\t\t{Acct Owner Bus Phone 3},\n\t\t{Acct Owner Bus Fax 3},\n\t\t{Acct Owner Salutation 4},\n\t\t{Acct Owner Last Name 4},\n\t\t{Acct Owner First Name 4},\n\t\t{Acct Owner Birth Date 4},\n\t\t{Acct Owner SIN 4},\n\t\t{Acct Owner Sex 4},\n\t\t{Acct Owner Lang 4},\n\t\t{Acct Owner Home Phone 4},\n\t\t{Acct Owner Bus Phone 4},\n\t\t{Acct Owner Bus Fax 4},\n\t\t{Beneficiary Last Name 1},\n\t\t{Beneficiary First Name 1},\n\t\t{Beneficiary Percentage 1},\n\t\t{Beneficiary Relation 1},\n\t\t{Beneficiary Last Name 2},\n\t\t{Beneficiary First Name 2},\n\t\t{Beneficiary Percentage 2},\n\t\t{Beneficiary Relation 2},\n\t\t{Beneficiary Last Name 3},\n\t\t{Beneficiary First Name 3},\n\t\t{Beneficiary Percentage 3},\n\t\t{Beneficiary Relation 3},\n\t\t{Account Tax Type Code},\n\t\t{Account Type Code},\n\t\t{Account Currency},\n\t\t{Account Status Code},\n\t\t{Bank Code},\n\t\t{Bank Acct Number},\n\t\t{Bank Transit Number},\n\t\t{Fund Company Code},\n\t\t{RRIF Payment Frequency},\n\t\t{Tax Jurisdiction},\n\t\t{RRIF Payment Option},\n\t\t{RRIF Effective Date},\n\t\t{RRIF Minimum Payment},\n\t\t{RRIF Maximum Payment},\n\t\t{RRIF Gross Payment},\n\t\t{RRIF Net Amount},\n\t\t{RRIF GrossNet Indicator},\n\t\t{RRIF Index Rate},\n\t\t{Free Amount},\n\t\t{RRIF Year To Date Redemption},\n\t\t{RRIF Minimum PaidLeft},\n\t\t{RRIF Current Year Minimum Schedule},\n\t\t{Paying Agent First Name},\n\t\t{Paying Agent Last Name},\n\t\t{Paying Agent Address Line 1},\n\t\t{Paying Agent Address Line 2},\n\t\t{Paying Agent Address Line 3},\n\t\t{Paying Agent Address Line 4},\n\t\t{Paying Agent Address Line 5},\n\t\t{Paying Agent City},\n\t\t{Paying Agent ProvinceORState},\n\t\t{Paying Agent Postal},\n\t\t{Paying Agent Country},\n\t\t{Paying Agent Tax Jurisdiction},\n\t\t{Paying Agent EUSD Option},\n\t\t{Acct owner Tax Exemption Expiry Date 1},\n\t\t{Acct owner Certificate number 1},\n\t\t{Acct owner Exemption reason code 1},\n\t\t{Acct owner EUSD Option 1},\n\t\t{Acct owner Tax Exemption Expiry Date 2},\n\t\t{Acct owner Certificate number 2},\n\t\t{Acct owner Exemption reason code 2},\n\t\t{Acct owner EUSD Option 2},\n\t\t{Acct owner Tax Exemption Expiry Date 3},\n\t\t{Acct owner Certificate number 3},\n\t\t{Acct owner Exemption reason code 3},\n\t\t{Acct owner EUSD Option 3},\n\t\t{Acct owner Tax Exemption Expiry Date 4},\n\t\t{Acct owner Certificate number 4},\n\t\t{Acct owner Exemption reason code 4},\n\t\t{Acct owner EUSD Option 4},\n\t\t{Entity ID 1},\n\t\t{Entity Type 1},\n\t\t{vAuthorizedTrade: string},\n\t\t{Nationality: string},\n\t\t{BirthPlace: string},\n\t\t{EMail: string},\n\t\t{Entity ID 2},\n\t\t{Entity Type 2},\n\t\t{Authorized To Trade 2},\n\t\t{Nationality 2},\n\t\t{BirthPlace 2},\n\t\t{EMail 2},\n\t\t{Entity ID 3},\n\t\t{Entity Type 3},\n\t\t{Authorized To Trade 3},\n\t\t{Nationality 3},\n\t\t{BirthPlace 3},\n\t\t{EMail 3},\n\t\t{Entity ID 4},\n\t\t{Entity Type 4},\n\t\t{Authorized To Trade 4},\n\t\t{Nationality 4},\n\t\t{BirthPlace 4},\n\t\t{EMail 4},\n\t\t{Registration Complete},\n\t\t{Deff: string},\n\t\t{Purchase Stop},\n\t\t{Redemption Stop},\n\t\t{Stop Redemption Settle},\n\t\t{Transfer Out Stop},\n\t\t{Transfer In Stop},\n\t\t{Stop Foreign Content Rebal},\n\t\t{Stop AMS Rebal},\n\t\t{Bank Type 1},\n\t\t{Bank Use 1},\n\t\t{Institution Code 1},\n\t\t{Bank Acct Number 1},\n\t\t{Transit No 1},\n\t\t{Bank Name 1},\n\t\t{Corr Bank No 1},\n\t\t{Corr Bank Name 1},\n\t\t{Corr BankAddr1 1},\n\t\t{Corr BankAddr2 1},\n\t\t{Corr BankAddr3 1},\n\t\t{Corr BankAddr4 1},\n\t\t{Corr BankAddr5 1},\n\t\t{Corr Bank Postal Code 1},\n\t\t{Corr Bank Country 1},\n\t\t{Credit Information 1},\n\t\t{Instruction Line1 1},\n\t\t{Instruction Line2 1},\n\t\t{Bank Type 2},\n\t\t{Bank Use 2},\n\t\t{Institution Code 2},\n\t\t{Bank Acct Number 2},\n\t\t{Transit No 2},\n\t\t{Bank Name 2},\n\t\t{Corr Bank No 2},\n\t\t{Corr Bank Name 2},\n\t\t{Corr BankAddr1 2},\n\t\t{Corr BankAddr2 2},\n\t\t{Corr BankAddr3 2},\n\t\t{Corr BankAddr4 2},\n\t\t{Corr BankAddr5 2},\n\t\t{Corr Bank Postal Code 2},\n\t\t{Corr Bank Country 2},\n\t\t{Credit Information 2},\n\t\t{Instruction Line1 2},\n\t\t{Instruction Line2 2},\n\t\t{Bank Type 3},\n\t\t{Bank Use 3},\n\t\t{Institution Code 3},\n\t\t{Bank Acct Number 3},\n\t\t{Transit No 3},\n\t\t{Bank Name 3},\n\t\t{Corr Bank No 3},\n\t\t{Corr Bank Name 3},\n\t\t{Corr BankAddr1 3},\n\t\t{Corr BankAddr2 3},\n\t\t{Corr BankAddr3 3},\n\t\t{Corr BankAddr4 3},\n\t\t{Corr BankAddr5 3},\n\t\t{Corr Bank Postal Code 3},\n\t\t{Corr Bank Country 3},\n\t\t{Credit Information 3},\n\t\t{Instruction Line1 3},\n\t\t{Instruction Line2 3},\n\t\t{Bank Type 4},\n\t\t{Bank Use 4},\n\t\t{Institution Code 4},\n\t\t{Bank Acct Number 4},\n\t\t{Transit No 4},\n\t\t{Bank Name 4},\n\t\t{Corr Bank No 4},\n\t\t{Corr Bank Name 4},\n\t\t{Corr BankAddr1 4},\n\t\t{Corr BankAddr2 4},\n\t\t{Corr BankAddr3 4},\n\t\t{Corr BankAddr4 4},\n\t\t{Corr BankAddr5 4},\n\t\t{Corr Bank Postal Code 4},\n\t\t{Corr Bank Country 4},\n\t\t{Credit Information 4},\n\t\t{Instruction Line1 4},\n\t\t{Instruction Line2 4},\n\t\t{Dealer Other IDs},\n\t\t{Branch Other IDs},\n\t\t{Rep Other IDs},\n\t\t{AO Country Of Birth},\n\t\t{JO Country Of Birth 1},\n\t\t{JO Country Of Birth 2},\n\t\t{JO Country Of Birth 3},\n\t\t{Social Code},\n\t\t{Social Code Description},\n\t\t{Account Category Code 1},\n\t\t{Account Category Code 1 Description},\n\t\t{Account Category Code 2},\n\t\t{Account Category Code 2 Description},\n\t\t{Account Category Code 3},\n\t\t{Account Category Code 3 Description},\n\t\t{Account Category Code 4},\n\t\t{Account Category Code 4 Description},\n\t\t{Account Category Code 5},\n\t\t{Account Category Code 5 Description},\n\t\t{Shareholder Group},\n\t\t{Account Clearing ID},\n\t\t{Management Settlement Location},\n\t\t{Management Settlement Account},\n\t\t{Management Default Setting},\n\t\t{Account Settlement Instruction Code 1},\n\t\t{Accont Settlement Instruction Delivery To 1},\n\t\t{Account Settlement Instruction for Account 1},\n\t\t{Account Settlement Instruction in Favour of 1},\n\t\t{Account Settlement Instruction Default Setting 1},\n\t\t{Account Settlement Instruction Code 2},\n\t\t{Accont Settlement Instruction Delivery To 2},\n\t\t{Account Settlement Instruction for Account 2},\n\t\t{Account Settlement Instruction in Favour of 2},\n\t\t{Account Settlement Instruction Default Setting 2},\n\t\t{Account Settlement Instruction Code 3},\n\t\t{Accont Settlement Instruction Delivery To 3},\n\t\t{Account Settlement Instruction for Account 3},\n\t\t{Account Settlement Instruction in Favour of 3},\n\t\t{Account Settlement Instruction Default Setting 3},\n\t\t{Account Settlement Instruction Code 4},\n\t\t{Accont Settlement Instruction Delivery To 4},\n\t\t{Account Settlement Instruction for Account 4},\n\t\t{Account Settlement Instruction in Favour of 4},\n\t\t{Account Settlement Instruction Default Setting 4},\n\t\t{Account Disposition Code},\n\t\t{CIF Number},\n\t\t{Intermediary Account Identifier},\n\t\t{Assigned Flag},\n\t\t{RRIF Periodic Mandatory Payment Amount},\n\t\t{RRIF Override Mandatory Flag},\n\t\t{RRIF Qualified Flag},\n\t\t{RRIF Spouse DateOfBirth},\n\t\t{RRIF Use Spouse Date of Birth},\n\t\t{Nonresident Agreement Flag},\n\t\t{Account Designation},\n\t\t{Sibling Only},\n\t\t{Pension Jurisdiction},\n\t\t{Tax Jurisdiction1},\n\t\t{Status of Management Settlement Location},\n\t\t{Status Date Change},\n\t\t{Acct Admin First Name},\n\t\t{Acct Admin Last Name},\n\t\t{Acct Admin First Name1},\n\t\t{Acct Admin Last Name1},\n\t\t{Acct Admin First Name2},\n\t\t{Acct Admin Last Name2},\n\t\t{Acct Admin First Name3},\n\t\t{Acct Admin Last Name3},\n\t\t{Stop Reason},\n\t\t{Other Stop Reason},\n\t\t{Clearing Code},\n\t\t{Clearing Description},\n\t\t{Clearing Code1},\n\t\t{Clearing Description1},\n\t\t{Clearing Code2},\n\t\t{Clearing Description2},\n\t\t{Clearing Code3},\n\t\t{Clearing Description3},\n\t\t{Clearing Code4},\n\t\t{Clearing Description4},\n\t\t{Tax Type},\n\t\t{EPA: string},\n\t\t{Tax Identification Number},\n\t\t{Territory Code},\n\t\t{Tax Jurisdiction ISO3},\n\t\t{Residence Province ISO3},\n\t\t{Organization Type},\n\t\t{Sub Organization Type},\n\t\t{UUID: string},\n\t\t{GUID: string},\n\t\t{Product Portfolio Name},\n\t\t{Product Portfolio Code},\n\t\t{Product Portfolio Plan Name},\n\t\t{Product Portfolio Plan Code},\n\t\t{NonResident Effective Date},\n\t\t{NonResident Stop Date},\n\t\t{NonResident Tax Jurisdiction},\n\t\t{National ID},\n\t\t{Country of Issued National ID},\n\t\t{Passport Number},\n\t\t{Country of Issued Passport},\n\t\t{Settlement Currency},\n\t\t{Pay Type Redemption},\n\t\t{Pay Type Dividend},\n\t\t{Pay Type Purchase},\n\t\t{Registration Agent Number},\n\t\t{CPFB Account Number},\n\t\t{Second Phone Number},\n\t\t{Second Phone Number Extension},\n\t\t{EntityTypeSeq: string},\n\t\t{Joint EntityTypeSeq1},\n\t\t{Joint EntityTypeSeq2},\n\t\t{Joint EntityTypeSeq3},\n\t\t{Business Number},\n\t\t{In Trust For},\n\t\t{Intermediary: string},\n\t\t{Schedule: string},\n\t\t{Affluent: string},\n\t\t{Trust Number},\n\t\t{Access ID},\n\t\t{FAST Client xRef Num},\n\t\t{iFAST SICAV Client xRef Num},\n\t\t{iFAST OEIC Client xRef Num},\n\t\t{PSD Account Exemption},\n\t\t{RDR Applicable},\n\t\t{Payment Routing Method},\n\t\t{Registration Information},\n\t\t{Investor Classification},\n\t\t{Investor Categorization},\n\t\t{Hold Dividend Payment},\n\t\t{Aggregation Mode},\n\t\tTerminateDate = {_col44_},\n\t\tID,\n\t\tLoaded\n\t),\n\tskipDuplicateMapInputs: true,\n\tskipDuplicateMapOutputs: true) ~> SelectedColumn\nDerivedTwoColumn derive({_col0_} = toString(byName('_col0_')),\n\t\t{_col1_} = toString(byName('_col1_')),\n\t\t{_col2_} = toString(byName('_col2_')),\n\t\t{_col3_} = toString(byName('_col3_')),\n\t\t{_col4_} = toString(byName('_col4_')),\n\t\t{_col5_} = toString(byName('_col5_')),\n\t\t{_col6_} = toString(byName('_col6_')),\n\t\t{_col7_} = toString(byName('_col7_')),\n\t\t{_col8_} = toString(byName('_col8_')),\n\t\t{_col9_} = toString(byName('_col9_')),\n\t\t{_col10_} = toString(byName('_col10_')),\n\t\t{_col11_} = toString(byName('_col11_')),\n\t\t{_col12_} = toString(byName('_col12_')),\n\t\t{_col13_} = toString(byName('_col13_')),\n\t\t{_col14_} = toString(byName('_col14_')),\n\t\t{_col15_} = toString(byName('_col15_')),\n\t\t{_col16_} = toString(byName('_col16_')),\n\t\t{_col17_} = toString(byName('_col17_')),\n\t\t{_col18_} = toString(byName('_col18_')),\n\t\t{_col19_} = toString(byName('_col19_')),\n\t\t{_col20_} = toString(byName('_col20_')),\n\t\t{_col21_} = toString(byName('_col21_')),\n\t\t{_col22_} = toString(byName('_col22_')),\n\t\t{_col23_} = toString(byName('_col23_')),\n\t\t{_col24_} = toString(byName('_col24_')),\n\t\t{_col25_} = toString(byName('_col25_')),\n\t\t{_col26_} = toString(byName('_col26_')),\n\t\t{_col27_} = toString(byName('_col27_')),\n\t\t{_col28_} = toString(byName('_col28_')),\n\t\t{_col29_} = toString(byName('_col29_')),\n\t\t{_col30_} = toString(byName('_col30_')),\n\t\t{_col31_} = toString(byName('_col31_')),\n\t\t{_col32_} = toString(byName('_col32_')),\n\t\t{_col33_} = toString(byName('_col33_')),\n\t\t{_col34_} = toString(byName('_col34_')),\n\t\t{_col35_} = toString(byName('_col35_')),\n\t\t{_col36_} = toString(byName('_col36_')),\n\t\t{_col37_} = toString(byName('_col37_')),\n\t\t{_col38_} = toString(byName('_col38_')),\n\t\t{_col39_} = toString(byName('_col39_')),\n\t\t{_col40_} = toString(byName('_col40_')),\n\t\t{_col41_} = toString(byName('_col41_')),\n\t\t{_col42_} = toString(byName('_col42_')),\n\t\t{_col43_} = toString(byName('_col43_')),\n\t\t{_col44_} = toString(byName('_col44_')),\n\t\t{_col45_} = toString(byName('_col45_')),\n\t\t{_col46_} = toString(byName('_col46_')),\n\t\t{_col47_} = toString(byName('_col47_'))) ~> AddedDriftedColumn\nSelectedColumn sink(allowSchemaDrift: true,\n\tvalidateSchema: false,\n\tinput(\n\t\t{Record Type} as string,\n\t\t{Account Number} as string,\n\t\t{Shareholder Number} as string,\n\t\t{Alt Account Number} as string,\n\t\t{Account Other Reference Number} as string,\n\t\t{Open Date} as string,\n\t\t{Registered Name Line 1} as string,\n\t\t{Registered Name Line 2} as string,\n\t\t{Registered Owner Address Line 1} as string,\n\t\t{Registered Owner Address Line 2} as string,\n\t\t{Registered Owner Address Line 3} as string,\n\t\t{Registered Owner Address Line 4} as string,\n\t\t{Registered Owner Address Line 5} as string,\n\t\t{Registered Owner ProvinceORState} as string,\n\t\t{Registered Owner Postal Code} as string,\n\t\t{Registered Owner Country} as string,\n\t\t{Dealer Code} as string,\n\t\t{Dealer Branch} as string,\n\t\t{Agent Code} as string,\n\t\t{Statement Mailing Name Line 1} as string,\n\t\t{Statement Mailing Name Line 2} as string,\n\t\t{Statement Address Line 1} as string,\n\t\t{Statement Address Line 2} as string,\n\t\t{Statement Address Line 3} as string,\n\t\t{Statement Address Line 4} as string,\n\t\t{Statement Address Line 5} as string,\n\t\t{Statement Province State} as string,\n\t\t{Statement Postal Code} as string,\n\t\t{Statement Country} as string,\n\t\t{Statement Hold Flag} as string,\n\t\t{Acct Owner Salutation 1} as string,\n\t\t{Acct Owner Last Name 1} as string,\n\t\t{Acct Owner First Name 1} as string,\n\t\t{Acct Owner Birth Date 1} as string,\n\t\t{Acct Owner SIN 1} as string,\n\t\t{Acct Owner Sex 1} as string,\n\t\t{Acct Owner Lang 1} as string,\n\t\t{Acct Owner Home Phone 1} as string,\n\t\t{Acct Owner Bus Phone 1} as string,\n\t\t{Acct Owner Bus Fax 1} as string,\n\t\t{Acct Owner Salutation 2} as string,\n\t\t{Acct Owner Last Name 2} as string,\n\t\t{Acct Owner First Name 2} as string,\n\t\t{Acct Owner Birth Date 2} as string,\n\t\t{Acct Owner SIN 2} as string,\n\t\t{Acct Owner Sex 2} as string,\n\t\t{Acct Owner Lang 2} as string,\n\t\t{Acct Owner Home Phone 2} as string,\n\t\t{Acct Owner Bus Phone 2} as string,\n\t\t{Acct Owner Bus Fax 2} as string,\n\t\t{Acct Owner Salutation 3} as string,\n\t\t{Acct Owner Last Name 3} as string,\n\t\t{Acct Owner First Name 3} as string,\n\t\t{Acct Owner Birth Date 3} as string,\n\t\t{Acct Owner SIN 3} as string,\n\t\t{Acct Owner Sex 3} as string,\n\t\t{Acct Owner Lang 3} as string,\n\t\t{Acct Owner Home Phone 3} as string,\n\t\t{Acct Owner Bus Phone 3} as string,\n\t\t{Acct Owner Bus Fax 3} as string,\n\t\t{Acct Owner Salutation 4} as string,\n\t\t{Acct Owner Last Name 4} as string,\n\t\t{Acct Owner First Name 4} as string,\n\t\t{Acct Owner Birth Date 4} as string,\n\t\t{Acct Owner SIN 4} as string,\n\t\t{Acct Owner Sex 4} as string,\n\t\t{Acct Owner Lang 4} as string,\n\t\t{Acct Owner Home Phone 4} as string,\n\t\t{Acct Owner Bus Phone 4} as string,\n\t\t{Acct Owner Bus Fax 4} as string,\n\t\t{Beneficiary Last Name 1} as string,\n\t\t{Beneficiary First Name 1} as string,\n\t\t{Beneficiary Percentage 1} as string,\n\t\t{Beneficiary Relation 1} as string,\n\t\t{Beneficiary Last Name 2} as string,\n\t\t{Beneficiary First Name 2} as string,\n\t\t{Beneficiary Percentage 2} as string,\n\t\t{Beneficiary Relation 2} as string,\n\t\t{Beneficiary Last Name 3} as string,\n\t\t{Beneficiary First Name 3} as string,\n\t\t{Beneficiary Percentage 3} as string,\n\t\t{Beneficiary Relation 3} as string,\n\t\t{Account Tax Type Code} as string,\n\t\t{Account Type Code} as string,\n\t\t{Account Currency} as string,\n\t\t{Account Status Code} as string,\n\t\t{Bank Code} as string,\n\t\t{Bank Acct Number} as string,\n\t\t{Bank Transit Number} as string,\n\t\t{Fund Company Code} as string,\n\t\t{RRIF Payment Frequency} as string,\n\t\t{Tax Jurisdiction} as string,\n\t\t{RRIF Payment Option} as string,\n\t\t{RRIF Effective Date} as string,\n\t\t{RRIF Minimum Payment} as string,\n\t\t{RRIF Maximum Payment} as string,\n\t\t{RRIF Gross Payment} as string,\n\t\t{RRIF Net Amount} as string,\n\t\t{RRIF GrossNet Indicator} as string,\n\t\t{RRIF Index Rate} as string,\n\t\t{Free Amount} as string,\n\t\t{RRIF Year To Date Redemption} as string,\n\t\t{RRIF Minimum PaidLeft} as string,\n\t\t{RRIF Current Year Minimum Schedule} as string,\n\t\t{Paying Agent First Name} as string,\n\t\t{Paying Agent Last Name} as string,\n\t\t{Paying Agent Address Line 1} as string,\n\t\t{Paying Agent Address Line 2} as string,\n\t\t{Paying Agent Address Line 3} as string,\n\t\t{Paying Agent Address Line 4} as string,\n\t\t{Paying Agent Address Line 5} as string,\n\t\t{Paying Agent City} as string,\n\t\t{Paying Agent ProvinceORState} as string,\n\t\t{Paying Agent Postal} as string,\n\t\t{Paying Agent Country} as string,\n\t\t{Paying Agent Tax Jurisdiction} as string,\n\t\t{Paying Agent EUSD Option} as string,\n\t\t{Acct owner Tax Exemption Expiry Date 1} as string,\n\t\t{Acct owner Certificate number 1} as string,\n\t\t{Acct owner Exemption reason code 1} as string,\n\t\t{Acct owner EUSD Option 1} as string,\n\t\t{Acct owner Tax Exemption Expiry Date 2} as string,\n\t\t{Acct owner Certificate number 2} as string,\n\t\t{Acct owner Exemption reason code 2} as string,\n\t\t{Acct owner EUSD Option 2} as string,\n\t\t{Acct owner Tax Exemption Expiry Date 3} as string,\n\t\t{Acct owner Certificate number 3} as string,\n\t\t{Acct owner Exemption reason code 3} as string,\n\t\t{Acct owner EUSD Option 3} as string,\n\t\t{Acct owner Tax Exemption Expiry Date 4} as string,\n\t\t{Acct owner Certificate number 4} as string,\n\t\t{Acct owner Exemption reason code 4} as string,\n\t\t{Acct owner EUSD Option 4} as string,\n\t\t{Entity ID 1} as string,\n\t\t{Entity Type 1} as string,\n\t\t{vAuthorizedTrade: string} as string,\n\t\t{Nationality: string} as string,\n\t\t{BirthPlace: string} as string,\n\t\t{EMail: string} as string,\n\t\t{Entity ID 2} as string,\n\t\t{Entity Type 2} as string,\n\t\t{Authorized To Trade 2} as string,\n\t\t{Nationality 2} as string,\n\t\t{BirthPlace 2} as string,\n\t\t{EMail 2} as string,\n\t\t{Entity ID 3} as string,\n\t\t{Entity Type 3} as string,\n\t\t{Authorized To Trade 3} as string,\n\t\t{Nationality 3} as string,\n\t\t{BirthPlace 3} as string,\n\t\t{EMail 3} as string,\n\t\t{Entity ID 4} as string,\n\t\t{Entity Type 4} as string,\n\t\t{Authorized To Trade 4} as string,\n\t\t{Nationality 4} as string,\n\t\t{BirthPlace 4} as string,\n\t\t{EMail 4} as string,\n\t\t{Registration Complete} as string,\n\t\t{Deff: string} as string,\n\t\t{Purchase Stop} as string,\n\t\t{Redemption Stop} as string,\n\t\t{Stop Redemption Settle} as string,\n\t\t{Transfer Out Stop} as string,\n\t\t{Transfer In Stop} as string,\n\t\t{Stop Foreign Content Rebal} as string,\n\t\t{Stop AMS Rebal} as string,\n\t\t{Bank Type 1} as string,\n\t\t{Bank Use 1} as string,\n\t\t{Institution Code 1} as string,\n\t\t{Bank Acct Number 1} as string,\n\t\t{Transit No 1} as string,\n\t\t{Bank Name 1} as string,\n\t\t{Corr Bank No 1} as string,\n\t\t{Corr Bank Name 1} as string,\n\t\t{Corr BankAddr1 1} as string,\n\t\t{Corr BankAddr2 1} as string,\n\t\t{Corr BankAddr3 1} as string,\n\t\t{Corr BankAddr4 1} as string,\n\t\t{Corr BankAddr5 1} as string,\n\t\t{Corr Bank Postal Code 1} as string,\n\t\t{Corr Bank Country 1} as string,\n\t\t{Credit Information 1} as string,\n\t\t{Instruction Line1 1} as string,\n\t\t{Instruction Line2 1} as string,\n\t\t{Bank Type 2} as string,\n\t\t{Bank Use 2} as string,\n\t\t{Institution Code 2} as string,\n\t\t{Bank Acct Number 2} as string,\n\t\t{Transit No 2} as string,\n\t\t{Bank Name 2} as string,\n\t\t{Corr Bank No 2} as string,\n\t\t{Corr Bank Name 2} as string,\n\t\t{Corr BankAddr1 2} as string,\n\t\t{Corr BankAddr2 2} as string,\n\t\t{Corr BankAddr3 2} as string,\n\t\t{Corr BankAddr4 2} as string,\n\t\t{Corr BankAddr5 2} as string,\n\t\t{Corr Bank Postal Code 2} as string,\n\t\t{Corr Bank Country 2} as string,\n\t\t{Credit Information 2} as string,\n\t\t{Instruction Line1 2} as string,\n\t\t{Instruction Line2 2} as string,\n\t\t{Bank Type 3} as string,\n\t\t{Bank Use 3} as string,\n\t\t{Institution Code 3} as string,\n\t\t{Bank Acct Number 3} as string,\n\t\t{Transit No 3} as string,\n\t\t{Bank Name 3} as string,\n\t\t{Corr Bank No 3} as string,\n\t\t{Corr Bank Name 3} as string,\n\t\t{Corr BankAddr1 3} as string,\n\t\t{Corr BankAddr2 3} as string,\n\t\t{Corr BankAddr3 3} as string,\n\t\t{Corr BankAddr4 3} as string,\n\t\t{Corr BankAddr5 3} as string,\n\t\t{Corr Bank Postal Code 3} as string,\n\t\t{Corr Bank Country 3} as string,\n\t\t{Credit Information 3} as string,\n\t\t{Instruction Line1 3} as string,\n\t\t{Instruction Line2 3} as string,\n\t\t{Bank Type 4} as string,\n\t\t{Bank Use 4} as string,\n\t\t{Institution Code 4} as string,\n\t\t{Bank Acct Number 4} as string,\n\t\t{Transit No 4} as string,\n\t\t{Bank Name 4} as string,\n\t\t{Corr Bank No 4} as string,\n\t\t{Corr Bank Name 4} as string,\n\t\t{Corr BankAddr1 4} as string,\n\t\t{Corr BankAddr2 4} as string,\n\t\t{Corr BankAddr3 4} as string,\n\t\t{Corr BankAddr4 4} as string,\n\t\t{Corr BankAddr5 4} as string,\n\t\t{Corr Bank Postal Code 4} as string,\n\t\t{Corr Bank Country 4} as string,\n\t\t{Credit Information 4} as string,\n\t\t{Instruction Line1 4} as string,\n\t\t{Instruction Line2 4} as string,\n\t\t{Dealer Other IDs} as string,\n\t\t{Branch Other IDs} as string,\n\t\t{Rep Other IDs} as string,\n\t\t{AO Country Of Birth} as string,\n\t\t{JO Country Of Birth 1} as string,\n\t\t{JO Country Of Birth 2} as string,\n\t\t{JO Country Of Birth 3} as string,\n\t\t{Social Code} as string,\n\t\t{Social Code Description} as string,\n\t\t{Account Category Code 1} as string,\n\t\t{Account Category Code 1 Description} as string,\n\t\t{Account Category Code 2} as string,\n\t\t{Account Category Code 2 Description} as string,\n\t\t{Account Category Code 3} as string,\n\t\t{Account Category Code 3 Description} as string,\n\t\t{Account Category Code 4} as string,\n\t\t{Account Category Code 4 Description} as string,\n\t\t{Account Category Code 5} as string,\n\t\t{Account Category Code 5 Description} as string,\n\t\t{Shareholder Group} as string,\n\t\t{Account Clearing ID} as string,\n\t\t{Management Settlement Location} as string,\n\t\t{Management Settlement Account} as string,\n\t\t{Management Default Setting} as string,\n\t\t{Account Settlement Instruction Code 1} as string,\n\t\t{Accont Settlement Instruction Delivery To 1} as string,\n\t\t{Account Settlement Instruction for Account 1} as string,\n\t\t{Account Settlement Instruction in Favour of 1} as string,\n\t\t{Account Settlement Instruction Default Setting 1} as string,\n\t\t{Account Settlement Instruction Code 2} as string,\n\t\t{Accont Settlement Instruction Delivery To 2} as string,\n\t\t{Account Settlement Instruction for Account 2} as string,\n\t\t{Account Settlement Instruction in Favour of 2} as string,\n\t\t{Account Settlement Instruction Default Setting 2} as string,\n\t\t{Account Settlement Instruction Code 3} as string,\n\t\t{Accont Settlement Instruction Delivery To 3} as string,\n\t\t{Account Settlement Instruction for Account 3} as string,\n\t\t{Account Settlement Instruction in Favour of 3} as string,\n\t\t{Account Settlement Instruction Default Setting 3} as string,\n\t\t{Account Settlement Instruction Code 4} as string,\n\t\t{Accont Settlement Instruction Delivery To 4} as string,\n\t\t{Account Settlement Instruction for Account 4} as string,\n\t\t{Account Settlement Instruction in Favour of 4} as string,\n\t\t{Account Settlement Instruction Default Setting 4} as string,\n\t\t{Account Disposition Code} as string,\n\t\t{CIF Number} as string,\n\t\t{Intermediary Account Identifier} as string,\n\t\t{Assigned Flag} as string,\n\t\t{RRIF Periodic Mandatory Payment Amount} as string,\n\t\t{RRIF Override Mandatory Flag} as string,\n\t\t{RRIF Qualified Flag} as string,\n\t\t{RRIF Spouse DateOfBirth} as string,\n\t\t{RRIF Use Spouse Date of Birth} as string,\n\t\t{Nonresident Agreement Flag} as string,\n\t\t{Account Designation} as string,\n\t\t{Sibling Only} as string,\n\t\t{Pension Jurisdiction} as string,\n\t\t{Tax Jurisdiction1} as string,\n\t\t{Status of Management Settlement Location} as string,\n\t\t{Status Date Change} as string,\n\t\t{Acct Admin First Name} as string,\n\t\t{Acct Admin Last Name} as string,\n\t\t{Acct Admin First Name1} as string,\n\t\t{Acct Admin Last Name1} as string,\n\t\t{Acct Admin First Name2} as string,\n\t\t{Acct Admin Last Name2} as string,\n\t\t{Acct Admin First Name3} as string,\n\t\t{Acct Admin Last Name3} as string,\n\t\t{Stop Reason} as string,\n\t\t{Other Stop Reason} as string,\n\t\t{Clearing Code} as string,\n\t\t{Clearing Description} as string,\n\t\t{Clearing Code1} as string,\n\t\t{Clearing Description1} as string,\n\t\t{Clearing Code2} as string,\n\t\t{Clearing Description2} as string,\n\t\t{Clearing Code3} as string,\n\t\t{Clearing Description3} as string,\n\t\t{Clearing Code4} as string,\n\t\t{Clearing Description4} as string,\n\t\t{Tax Type} as string,\n\t\t{EPA: string} as string,\n\t\t{Tax Identification Number} as string,\n\t\t{Territory Code} as string,\n\t\t{Tax Jurisdiction ISO3} as string,\n\t\t{Residence Province ISO3} as string,\n\t\t{Organization Type} as string,\n\t\t{Sub Organization Type} as string,\n\t\t{UUID: string} as string,\n\t\t{GUID: string} as string,\n\t\t{Product Portfolio Name} as string,\n\t\t{Product Portfolio Code} as string,\n\t\t{Product Portfolio Plan Name} as string,\n\t\t{Product Portfolio Plan Code} as string,\n\t\t{NonResident Effective Date} as string,\n\t\t{NonResident Stop Date} as string,\n\t\t{NonResident Tax Jurisdiction} as string,\n\t\t{National ID} as string,\n\t\t{Country of Issued National ID} as string,\n\t\t{Passport Number} as string,\n\t\t{Country of Issued Passport} as string,\n\t\t{Settlement Currency} as string,\n\t\t{Pay Type Redemption} as string,\n\t\t{Pay Type Dividend} as string,\n\t\t{Pay Type Purchase} as string,\n\t\t{Registration Agent Number} as string,\n\t\t{CPFB Account Number} as string,\n\t\t{Second Phone Number} as string,\n\t\t{Second Phone Number Extension} as string,\n\t\t{EntityTypeSeq: string} as string,\n\t\t{Joint EntityTypeSeq1} as string,\n\t\t{Joint EntityTypeSeq2} as string,\n\t\t{Joint EntityTypeSeq3} as string,\n\t\t{Business Number} as string,\n\t\t{In Trust For} as string,\n\t\t{Intermediary: string} as string,\n\t\t{Schedule: string} as string,\n\t\t{Affluent: string} as string,\n\t\t{Trust Number} as string,\n\t\t{Access ID} as string,\n\t\t{FAST Client xRef Num} as string,\n\t\t{iFAST SICAV Client xRef Num} as string,\n\t\t{iFAST OEIC Client xRef Num} as string,\n\t\t{PSD Account Exemption} as string,\n\t\t{RDR Applicable} as string,\n\t\t{Payment Routing Method} as string,\n\t\t{Registration Information} as string,\n\t\t{Investor Classification} as string,\n\t\t{Investor Categorization} as string,\n\t\t{Hold Dividend Payment} as string,\n\t\t{Aggregation Mode} as string,\n\t\tTerminateDate as string,\n\t\tID as string,\n\t\tLoaded as string\n\t),\n\tpartitionFileNames:[($OutFile)],\n\tumask: 0022,\n\tpreCommands: [],\n\tpostCommands: [],\n\tskipDuplicateMapInputs: true,\n\tskipDuplicateMapOutputs: true,\n\tmapColumn(\n\t\t{Record Type},\n\t\t{Account Number},\n\t\t{Shareholder Number},\n\t\t{Alt Account Number},\n\t\t{Account Other Reference Number},\n\t\t{Open Date},\n\t\t{Registered Name Line 1},\n\t\t{Registered Name Line 2},\n\t\t{Registered Owner Address Line 1},\n\t\t{Registered Owner Address Line 2},\n\t\t{Registered Owner Address Line 3},\n\t\t{Registered Owner Address Line 4},\n\t\t{Registered Owner Address Line 5},\n\t\t{Registered Owner ProvinceORState},\n\t\t{Registered Owner Postal Code},\n\t\t{Registered Owner Country},\n\t\t{Dealer Code},\n\t\t{Dealer Branch},\n\t\t{Agent Code},\n\t\t{Statement Mailing Name Line 1},\n\t\t{Statement Mailing Name Line 2},\n\t\t{Statement Address Line 1},\n\t\t{Statement Address Line 2},\n\t\t{Statement Address Line 3},\n\t\t{Statement Address Line 4},\n\t\t{Statement Address Line 5},\n\t\t{Statement Province State},\n\t\t{Statement Postal Code},\n\t\t{Statement Country},\n\t\t{Statement Hold Flag},\n\t\t{Acct Owner Salutation 1},\n\t\t{Acct Owner Last Name 1},\n\t\t{Acct Owner First Name 1},\n\t\t{Acct Owner Birth Date 1},\n\t\t{Acct Owner SIN 1},\n\t\t{Acct Owner Sex 1},\n\t\t{Acct Owner Lang 1},\n\t\t{Acct Owner Home Phone 1},\n\t\t{Acct Owner Bus Phone 1},\n\t\t{Acct Owner Bus Fax 1},\n\t\t{Acct Owner Salutation 2},\n\t\t{Acct Owner Last Name 2},\n\t\t{Acct Owner First Name 2},\n\t\t{Acct Owner Birth Date 2},\n\t\t{Acct Owner SIN 2},\n\t\t{Acct Owner Sex 2},\n\t\t{Acct Owner Lang 2},\n\t\t{Acct Owner Home Phone 2},\n\t\t{Acct Owner Bus Phone 2},\n\t\t{Acct Owner Bus Fax 2},\n\t\t{Acct Owner Salutation 3},\n\t\t{Acct Owner Last Name 3},\n\t\t{Acct Owner First Name 3},\n\t\t{Acct Owner Birth Date 3},\n\t\t{Acct Owner SIN 3},\n\t\t{Acct Owner Sex 3},\n\t\t{Acct Owner Lang 3},\n\t\t{Acct Owner Home Phone 3},\n\t\t{Acct Owner Bus Phone 3},\n\t\t{Acct Owner Bus Fax 3},\n\t\t{Acct Owner Salutation 4},\n\t\t{Acct Owner Last Name 4},\n\t\t{Acct Owner First Name 4},\n\t\t{Acct Owner Birth Date 4},\n\t\t{Acct Owner SIN 4},\n\t\t{Acct Owner Sex 4},\n\t\t{Acct Owner Lang 4},\n\t\t{Acct Owner Home Phone 4},\n\t\t{Acct Owner Bus Phone 4},\n\t\t{Acct Owner Bus Fax 4},\n\t\t{Beneficiary Last Name 1},\n\t\t{Beneficiary First Name 1},\n\t\t{Beneficiary Percentage 1},\n\t\t{Beneficiary Relation 1},\n\t\t{Beneficiary Last Name 2},\n\t\t{Beneficiary First Name 2},\n\t\t{Beneficiary Percentage 2},\n\t\t{Beneficiary Relation 2},\n\t\t{Beneficiary Last Name 3},\n\t\t{Beneficiary First Name 3},\n\t\t{Beneficiary Percentage 3},\n\t\t{Beneficiary Relation 3},\n\t\t{Account Tax Type Code},\n\t\t{Account Type Code},\n\t\t{Account Currency},\n\t\t{Account Status Code},\n\t\t{Bank Code},\n\t\t{Bank Acct Number},\n\t\t{Bank Transit Number},\n\t\t{Fund Company Code},\n\t\t{RRIF Payment Frequency},\n\t\t{Tax Jurisdiction},\n\t\t{RRIF Payment Option},\n\t\t{RRIF Effective Date},\n\t\t{RRIF Minimum Payment},\n\t\t{RRIF Maximum Payment},\n\t\t{RRIF Gross Payment},\n\t\t{RRIF Net Amount},\n\t\t{RRIF GrossNet Indicator},\n\t\t{RRIF Index Rate},\n\t\t{Free Amount},\n\t\t{RRIF Year To Date Redemption},\n\t\t{RRIF Minimum PaidLeft},\n\t\t{RRIF Current Year Minimum Schedule},\n\t\t{Paying Agent First Name},\n\t\t{Paying Agent Last Name},\n\t\t{Paying Agent Address Line 1},\n\t\t{Paying Agent Address Line 2},\n\t\t{Paying Agent Address Line 3},\n\t\t{Paying Agent Address Line 4},\n\t\t{Paying Agent Address Line 5},\n\t\t{Paying Agent City},\n\t\t{Paying Agent ProvinceORState},\n\t\t{Paying Agent Postal},\n\t\t{Paying Agent Country},\n\t\t{Paying Agent Tax Jurisdiction},\n\t\t{Paying Agent EUSD Option},\n\t\t{Acct owner Tax Exemption Expiry Date 1},\n\t\t{Acct owner Certificate number 1},\n\t\t{Acct owner Exemption reason code 1},\n\t\t{Acct owner EUSD Option 1},\n\t\t{Acct owner Tax Exemption Expiry Date 2},\n\t\t{Acct owner Certificate number 2},\n\t\t{Acct owner Exemption reason code 2},\n\t\t{Acct owner EUSD Option 2},\n\t\t{Acct owner Tax Exemption Expiry Date 3},\n\t\t{Acct owner Certificate number 3},\n\t\t{Acct owner Exemption reason code 3},\n\t\t{Acct owner EUSD Option 3},\n\t\t{Acct owner Tax Exemption Expiry Date 4},\n\t\t{Acct owner Certificate number 4},\n\t\t{Acct owner Exemption reason code 4},\n\t\t{Acct owner EUSD Option 4},\n\t\t{Entity ID 1},\n\t\t{Entity Type 1},\n\t\t{vAuthorizedTrade: string},\n\t\t{Nationality: string},\n\t\t{BirthPlace: string},\n\t\t{EMail: string},\n\t\t{Entity ID 2},\n\t\t{Entity Type 2},\n\t\t{Authorized To Trade 2},\n\t\t{Nationality 2},\n\t\t{BirthPlace 2},\n\t\t{EMail 2},\n\t\t{Entity ID 3},\n\t\t{Entity Type 3},\n\t\t{Authorized To Trade 3},\n\t\t{Nationality 3},\n\t\t{BirthPlace 3},\n\t\t{EMail 3},\n\t\t{Entity ID 4},\n\t\t{Entity Type 4},\n\t\t{Authorized To Trade 4},\n\t\t{Nationality 4},\n\t\t{BirthPlace 4},\n\t\t{EMail 4},\n\t\t{Registration Complete},\n\t\t{Deff: string},\n\t\t{Purchase Stop},\n\t\t{Redemption Stop},\n\t\t{Stop Redemption Settle},\n\t\t{Transfer Out Stop},\n\t\t{Transfer In Stop},\n\t\t{Stop Foreign Content Rebal},\n\t\t{Stop AMS Rebal},\n\t\t{Bank Type 1},\n\t\t{Bank Use 1},\n\t\t{Institution Code 1},\n\t\t{Bank Acct Number 1},\n\t\t{Transit No 1},\n\t\t{Bank Name 1},\n\t\t{Corr Bank No 1},\n\t\t{Corr Bank Name 1},\n\t\t{Corr BankAddr1 1},\n\t\t{Corr BankAddr2 1},\n\t\t{Corr BankAddr3 1},\n\t\t{Corr BankAddr4 1},\n\t\t{Corr BankAddr5 1},\n\t\t{Corr Bank Postal Code 1},\n\t\t{Corr Bank Country 1},\n\t\t{Credit Information 1},\n\t\t{Instruction Line1 1},\n\t\t{Instruction Line2 1},\n\t\t{Bank Type 2},\n\t\t{Bank Use 2},\n\t\t{Institution Code 2},\n\t\t{Bank Acct Number 2},\n\t\t{Transit No 2},\n\t\t{Bank Name 2},\n\t\t{Corr Bank No 2},\n\t\t{Corr Bank Name 2},\n\t\t{Corr BankAddr1 2},\n\t\t{Corr BankAddr2 2},\n\t\t{Corr BankAddr3 2},\n\t\t{Corr BankAddr4 2},\n\t\t{Corr BankAddr5 2},\n\t\t{Corr Bank Postal Code 2},\n\t\t{Corr Bank Country 2},\n\t\t{Credit Information 2},\n\t\t{Instruction Line1 2},\n\t\t{Instruction Line2 2},\n\t\t{Bank Type 3},\n\t\t{Bank Use 3},\n\t\t{Institution Code 3},\n\t\t{Bank Acct Number 3},\n\t\t{Transit No 3},\n\t\t{Bank Name 3},\n\t\t{Corr Bank No 3},\n\t\t{Corr Bank Name 3},\n\t\t{Corr BankAddr1 3},\n\t\t{Corr BankAddr2 3},\n\t\t{Corr BankAddr3 3},\n\t\t{Corr BankAddr4 3},\n\t\t{Corr BankAddr5 3},\n\t\t{Corr Bank Postal Code 3},\n\t\t{Corr Bank Country 3},\n\t\t{Credit Information 3},\n\t\t{Instruction Line1 3},\n\t\t{Instruction Line2 3},\n\t\t{Bank Type 4},\n\t\t{Bank Use 4},\n\t\t{Institution Code 4},\n\t\t{Bank Acct Number 4},\n\t\t{Transit No 4},\n\t\t{Bank Name 4},\n\t\t{Corr Bank No 4},\n\t\t{Corr Bank Name 4},\n\t\t{Corr BankAddr1 4},\n\t\t{Corr BankAddr2 4},\n\t\t{Corr BankAddr3 4},\n\t\t{Corr BankAddr4 4},\n\t\t{Corr BankAddr5 4},\n\t\t{Corr Bank Postal Code 4},\n\t\t{Corr Bank Country 4},\n\t\t{Credit Information 4},\n\t\t{Instruction Line1 4},\n\t\t{Instruction Line2 4},\n\t\t{Dealer Other IDs},\n\t\t{Branch Other IDs},\n\t\t{Rep Other IDs},\n\t\t{AO Country Of Birth},\n\t\t{JO Country Of Birth 1},\n\t\t{JO Country Of Birth 2},\n\t\t{JO Country Of Birth 3},\n\t\t{Social Code},\n\t\t{Social Code Description},\n\t\t{Account Category Code 1},\n\t\t{Account Category Code 1 Description},\n\t\t{Account Category Code 2},\n\t\t{Account Category Code 2 Description},\n\t\t{Account Category Code 3},\n\t\t{Account Category Code 3 Description},\n\t\t{Account Category Code 4},\n\t\t{Account Category Code 4 Description},\n\t\t{Account Category Code 5},\n\t\t{Account Category Code 5 Description},\n\t\t{Shareholder Group},\n\t\t{Account Clearing ID},\n\t\t{Management Settlement Location},\n\t\t{Management Settlement Account},\n\t\t{Management Default Setting},\n\t\t{Account Settlement Instruction Code 1},\n\t\t{Accont Settlement Instruction Delivery To 1},\n\t\t{Account Settlement Instruction for Account 1},\n\t\t{Account Settlement Instruction in Favour of 1},\n\t\t{Account Settlement Instruction Default Setting 1},\n\t\t{Account Settlement Instruction Code 2},\n\t\t{Accont Settlement Instruction Delivery To 2},\n\t\t{Account Settlement Instruction for Account 2},\n\t\t{Account Settlement Instruction in Favour of 2},\n\t\t{Account Settlement Instruction Default Setting 2},\n\t\t{Account Settlement Instruction Code 3},\n\t\t{Accont Settlement Instruction Delivery To 3},\n\t\t{Account Settlement Instruction for Account 3},\n\t\t{Account Settlement Instruction in Favour of 3},\n\t\t{Account Settlement Instruction Default Setting 3},\n\t\t{Account Settlement Instruction Code 4},\n\t\t{Accont Settlement Instruction Delivery To 4},\n\t\t{Account Settlement Instruction for Account 4},\n\t\t{Account Settlement Instruction in Favour of 4},\n\t\t{Account Settlement Instruction Default Setting 4},\n\t\t{Account Disposition Code},\n\t\t{CIF Number},\n\t\t{Intermediary Account Identifier},\n\t\t{Assigned Flag},\n\t\t{RRIF Periodic Mandatory Payment Amount},\n\t\t{RRIF Override Mandatory Flag},\n\t\t{RRIF Qualified Flag},\n\t\t{RRIF Spouse DateOfBirth},\n\t\t{RRIF Use Spouse Date of Birth},\n\t\t{Nonresident Agreement Flag},\n\t\t{Account Designation},\n\t\t{Sibling Only},\n\t\t{Pension Jurisdiction},\n\t\t{Tax Jurisdiction1},\n\t\t{Status of Management Settlement Location},\n\t\t{Status Date Change},\n\t\t{Acct Admin First Name},\n\t\t{Acct Admin Last Name},\n\t\t{Acct Admin First Name1},\n\t\t{Acct Admin Last Name1},\n\t\t{Acct Admin First Name2},\n\t\t{Acct Admin Last Name2},\n\t\t{Acct Admin First Name3},\n\t\t{Acct Admin Last Name3},\n\t\t{Stop Reason},\n\t\t{Other Stop Reason},\n\t\t{Clearing Code},\n\t\t{Clearing Description},\n\t\t{Clearing Code1},\n\t\t{Clearing Description1},\n\t\t{Clearing Code2},\n\t\t{Clearing Description2},\n\t\t{Clearing Code3},\n\t\t{Clearing Description3},\n\t\t{Clearing Code4},\n\t\t{Clearing Description4},\n\t\t{Tax Type},\n\t\t{EPA: string},\n\t\t{Tax Identification Number},\n\t\t{Territory Code},\n\t\t{Tax Jurisdiction ISO3},\n\t\t{Residence Province ISO3},\n\t\t{Organization Type},\n\t\t{Sub Organization Type},\n\t\t{UUID: string},\n\t\t{GUID: string},\n\t\t{Product Portfolio Name},\n\t\t{Product Portfolio Code},\n\t\t{Product Portfolio Plan Name},\n\t\t{Product Portfolio Plan Code},\n\t\t{NonResident Effective Date},\n\t\t{NonResident Stop Date},\n\t\t{NonResident Tax Jurisdiction},\n\t\t{National ID},\n\t\t{Country of Issued National ID},\n\t\t{Passport Number},\n\t\t{Country of Issued Passport},\n\t\t{Settlement Currency},\n\t\t{Pay Type Redemption},\n\t\t{Pay Type Dividend},\n\t\t{Pay Type Purchase},\n\t\t{Registration Agent Number},\n\t\t{CPFB Account Number},\n\t\t{Second Phone Number},\n\t\t{Second Phone Number Extension},\n\t\t{EntityTypeSeq: string},\n\t\t{Joint EntityTypeSeq1},\n\t\t{Joint EntityTypeSeq2},\n\t\t{Joint EntityTypeSeq3},\n\t\t{Business Number},\n\t\t{In Trust For},\n\t\t{Intermediary: string},\n\t\t{Schedule: string},\n\t\t{Affluent: string},\n\t\t{Trust Number},\n\t\t{Access ID},\n\t\t{FAST Client xRef Num},\n\t\t{iFAST SICAV Client xRef Num},\n\t\t{iFAST OEIC Client xRef Num},\n\t\t{PSD Account Exemption},\n\t\t{RDR Applicable},\n\t\t{Payment Routing Method},\n\t\t{Registration Information},\n\t\t{Investor Classification},\n\t\t{Investor Categorization},\n\t\t{Hold Dividend Payment},\n\t\t{Aggregation Mode},\n\t\tTerminateDate,\n\t\tID,\n\t\tLoaded\n\t),\n\tpartitionBy('hash', 1)) ~> OutPutFile"}}}