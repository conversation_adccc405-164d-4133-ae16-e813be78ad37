{"name": "DECOM- SF to SCD GRANDPARENT", "properties": {"activities": [{"name": "If SSID is Updated", "type": "IfCondition", "dependsOn": [], "userProperties": [], "typeProperties": {"expression": {"value": "@and(equals(pipeline().parameters.ObjectName,'FinancialAccount'),equals(pipeline().parameters.SSIDChange,1))", "type": "Expression"}, "ifFalseActivities": [{"name": "Trigger from Subscriber_copy1", "type": "Switch", "dependsOn": [], "userProperties": [], "typeProperties": {"on": {"value": "@pipeline().parameters.ObjectName", "type": "Expression"}, "cases": [{"value": "FinancialAccount", "activities": [{"name": "Portfolios - SF to SCD_copy4", "type": "ExecutePipeline", "dependsOn": [], "policy": {"secureInput": false}, "userProperties": [], "typeProperties": {"pipeline": {"referenceName": "DECOM- Portfolios CSV Template - SF to SCD", "type": "PipelineReference"}, "waitOnCompletion": true, "parameters": {"ObjectId": {"value": "@pipeline().parameters.ObjectId", "type": "Expression"}, "ObjectName": {"value": "@pipeline().parameters.ObjectName", "type": "Expression"}}}}]}, {"value": "Contact", "activities": [{"name": "Clients- SF to SCD_2_copy1", "type": "ExecutePipeline", "dependsOn": [], "policy": {"secureInput": false}, "userProperties": [], "typeProperties": {"pipeline": {"referenceName": "DECOM- Clients CSV Template - SF to SCD", "type": "PipelineReference"}, "waitOnCompletion": true, "parameters": {"ObjectId": {"value": "@pipeline().parameters.ObjectId", "type": "Expression"}, "ObjectName": {"value": "@pipeline().parameters.ObjectName", "type": "Expression"}}}}, {"name": "ClientGroup- SF to SCD_2_copy1", "type": "ExecutePipeline", "dependsOn": [{"activity": "Clients- SF to SCD_2_copy1", "dependencyConditions": ["Succeeded"]}], "policy": {"secureInput": false}, "userProperties": [], "typeProperties": {"pipeline": {"referenceName": "DECOM- ClientGroup CSV Template - SF to SCD", "type": "PipelineReference"}, "waitOnCompletion": true, "parameters": {"ObjectId": {"value": "@pipeline().parameters.ObjectId", "type": "Expression"}, "ObjectName": {"value": "@pipeline().parameters.ObjectName", "type": "Expression"}}}}]}, {"value": "Account", "activities": [{"name": "Portfolios - SF to SCD_copy1_copy1", "type": "ExecutePipeline", "dependsOn": [], "policy": {"secureInput": false}, "userProperties": [], "typeProperties": {"pipeline": {"referenceName": "DECOM- Portfolios CSV Template - SF to SCD", "type": "PipelineReference"}, "waitOnCompletion": true, "parameters": {"ObjectId": {"value": "@pipeline().parameters.ObjectId", "type": "Expression"}, "ObjectName": {"value": "@pipeline().parameters.ObjectName", "type": "Expression"}}}}, {"name": "Clients- SF to SCD_copy1_copy1", "type": "ExecutePipeline", "dependsOn": [{"activity": "Portfolios - SF to SCD_copy1_copy1", "dependencyConditions": ["Succeeded"]}], "policy": {"secureInput": false}, "userProperties": [], "typeProperties": {"pipeline": {"referenceName": "DECOM- Clients CSV Template - SF to SCD", "type": "PipelineReference"}, "waitOnCompletion": true, "parameters": {"ObjectId": {"value": "@pipeline().parameters.ObjectId", "type": "Expression"}, "ObjectName": {"value": "@pipeline().parameters.ObjectName", "type": "Expression"}}}}, {"name": "Bank Account - SF to SCD_copy1_copy2", "type": "ExecutePipeline", "dependsOn": [{"activity": "Clients- SF to SCD_copy1_copy1", "dependencyConditions": ["Succeeded"]}], "policy": {"secureInput": false}, "userProperties": [], "typeProperties": {"pipeline": {"referenceName": "DECOM- Bank Accounts CSV Template - SF to SCD", "type": "PipelineReference"}, "waitOnCompletion": true, "parameters": {"ObjectId": {"value": "@pipeline().parameters.ObjectId", "type": "Expression"}, "ObjectName": {"value": "@pipeline().parameters.ObjectName", "type": "Expression"}}}}, {"name": "Real Bank Account- SF to SCD_copy1_copy2", "type": "ExecutePipeline", "dependsOn": [{"activity": "Bank Account - SF to SCD_copy1_copy2", "dependencyConditions": ["Succeeded"]}], "policy": {"secureInput": false}, "userProperties": [], "typeProperties": {"pipeline": {"referenceName": "DECOM- Real Bank Account CSV Template - SF to SCD", "type": "PipelineReference"}, "waitOnCompletion": true, "parameters": {"ObjectName": {"value": "@pipeline().parameters.ObjectName", "type": "Expression"}, "ObjectId": {"value": "@pipeline().parameters.ObjectId", "type": "Expression"}}}}]}, {"value": "<PERSON><PERSON>", "activities": [{"name": "Portfolios - SF to SCD_copy2_copy1", "type": "ExecutePipeline", "dependsOn": [], "policy": {"secureInput": false}, "userProperties": [], "typeProperties": {"pipeline": {"referenceName": "DECOM- Portfolios CSV Template - SF to SCD", "type": "PipelineReference"}, "waitOnCompletion": true, "parameters": {"ObjectId": {"value": "@pipeline().parameters.ObjectId", "type": "Expression"}, "ObjectName": {"value": "@pipeline().parameters.ObjectName", "type": "Expression"}}}}]}, {"value": "User", "activities": [{"name": "Portfolios - SF to SCD_copy3_copy1", "type": "ExecutePipeline", "dependsOn": [], "policy": {"secureInput": false}, "userProperties": [], "typeProperties": {"pipeline": {"referenceName": "DECOM- Portfolios CSV Template - SF to SCD", "type": "PipelineReference"}, "waitOnCompletion": true, "parameters": {"ObjectId": {"value": "@pipeline().parameters.ObjectId", "type": "Expression"}, "ObjectName": {"value": "@pipeline().parameters.ObjectName", "type": "Expression"}}}}, {"name": "Clients- SF to SCD_copy2_copy1", "type": "ExecutePipeline", "dependsOn": [{"activity": "Portfolios - SF to SCD_copy3_copy1", "dependencyConditions": ["Succeeded"]}], "policy": {"secureInput": false}, "userProperties": [], "typeProperties": {"pipeline": {"referenceName": "DECOM- Clients CSV Template - SF to SCD", "type": "PipelineReference"}, "waitOnCompletion": true, "parameters": {"ObjectId": {"value": "@pipeline().parameters.ObjectId", "type": "Expression"}, "ObjectName": {"value": "@pipeline().parameters.ObjectName", "type": "Expression"}}}}]}, {"value": "BankAccount", "activities": [{"name": "Bank Account - SF to SCD_copy1_copy1_copy1", "type": "ExecutePipeline", "dependsOn": [], "policy": {"secureInput": false}, "userProperties": [], "typeProperties": {"pipeline": {"referenceName": "DECOM- Bank Accounts CSV Template - SF to SCD", "type": "PipelineReference"}, "waitOnCompletion": true, "parameters": {"ObjectId": {"value": "@pipeline().parameters.ObjectId", "type": "Expression"}, "ObjectName": {"value": "@pipeline().parameters.ObjectName", "type": "Expression"}}}}, {"name": "Real Bank Account- SF to SCD_copy1_copy1_copy1", "type": "ExecutePipeline", "dependsOn": [{"activity": "Bank Account - SF to SCD_copy1_copy1_copy1", "dependencyConditions": ["Succeeded"]}], "policy": {"secureInput": false}, "userProperties": [], "typeProperties": {"pipeline": {"referenceName": "DECOM- Real Bank Account CSV Template - SF to SCD", "type": "PipelineReference"}, "waitOnCompletion": true, "parameters": {"ObjectName": {"value": "@pipeline().parameters.ObjectName", "type": "Expression"}, "ObjectId": {"value": "@pipeline().parameters.ObjectId", "type": "Expression"}}}}]}]}}], "ifTrueActivities": [{"name": "Portfolios - SF to SCD_copy4_copy1", "type": "ExecutePipeline", "dependsOn": [], "policy": {"secureInput": false}, "userProperties": [], "typeProperties": {"pipeline": {"referenceName": "DECOM- Portfolios CSV Template - SF to SCD", "type": "PipelineReference"}, "waitOnCompletion": true, "parameters": {"ObjectId": {"value": "@pipeline().parameters.ObjectId", "type": "Expression"}, "ObjectName": {"value": "@pipeline().parameters.ObjectName", "type": "Expression"}}}}, {"name": "Clients- SF to SCD_copy3_copy1", "type": "ExecutePipeline", "dependsOn": [{"activity": "Portfolios - SF to SCD_copy4_copy1", "dependencyConditions": ["Succeeded"]}], "policy": {"secureInput": false}, "userProperties": [], "typeProperties": {"pipeline": {"referenceName": "DECOM- Clients CSV Template - SF to SCD", "type": "PipelineReference"}, "waitOnCompletion": true, "parameters": {"ObjectId": {"value": "@pipeline().parameters.ObjectId", "type": "Expression"}, "ObjectName": {"value": "@pipeline().parameters.ObjectName", "type": "Expression"}}}}, {"name": "ClientGroup- SF to SCD_copy1_copy1", "type": "ExecutePipeline", "dependsOn": [{"activity": "Clients- SF to SCD_copy3_copy1", "dependencyConditions": ["Succeeded"]}], "policy": {"secureInput": false}, "userProperties": [], "typeProperties": {"pipeline": {"referenceName": "DECOM- ClientGroup CSV Template - SF to SCD", "type": "PipelineReference"}, "waitOnCompletion": true, "parameters": {"ObjectId": {"value": "@pipeline().parameters.ObjectId", "type": "Expression"}, "ObjectName": {"value": "@pipeline().parameters.ObjectName", "type": "Expression"}}}}, {"name": "Custodian- SF to SCD_copy1_copy1", "type": "ExecutePipeline", "dependsOn": [{"activity": "ClientGroup- SF to SCD_copy1_copy1", "dependencyConditions": ["Succeeded"]}], "policy": {"secureInput": false}, "userProperties": [], "typeProperties": {"pipeline": {"referenceName": "DECOM- Custodian CSV Template - SF to SCD", "type": "PipelineReference"}, "waitOnCompletion": true, "parameters": {"ObjectId": {"value": "@pipeline().parameters.ObjectId", "type": "Expression"}, "ObjectName": {"value": "@pipeline().parameters.ObjectName", "type": "Expression"}}}}, {"name": "Custodies - SF to SCD_copy1_copy1", "type": "ExecutePipeline", "dependsOn": [{"activity": "Custodian- SF to SCD_copy1_copy1", "dependencyConditions": ["Succeeded"]}], "policy": {"secureInput": false}, "userProperties": [], "typeProperties": {"pipeline": {"referenceName": "DECOM- Custodies CSV Template - SF to SCD", "type": "PipelineReference"}, "waitOnCompletion": true, "parameters": {"ObjectId": {"value": "@pipeline().parameters.ObjectId", "type": "Expression"}, "ObjectName": {"value": "@pipeline().parameters.ObjectName", "type": "Expression"}}}}, {"name": "Bank Account - SF to SCD_copy2_copy1", "type": "ExecutePipeline", "dependsOn": [{"activity": "Custodies - SF to SCD_copy1_copy1", "dependencyConditions": ["Succeeded"]}], "policy": {"secureInput": false}, "userProperties": [], "typeProperties": {"pipeline": {"referenceName": "DECOM- Bank Accounts CSV Template - SF to SCD", "type": "PipelineReference"}, "waitOnCompletion": true, "parameters": {"ObjectId": {"value": "@pipeline().parameters.ObjectId", "type": "Expression"}, "ObjectName": {"value": "@pipeline().parameters.ObjectName", "type": "Expression"}}}}, {"name": "Real Bank Account- SF to SCD_copy2_copy1", "type": "ExecutePipeline", "dependsOn": [{"activity": "Bank Account - SF to SCD_copy2_copy1", "dependencyConditions": ["Succeeded"]}], "policy": {"secureInput": false}, "userProperties": [], "typeProperties": {"pipeline": {"referenceName": "DECOM- Real Bank Account CSV Template - SF to SCD", "type": "PipelineReference"}, "waitOnCompletion": true, "parameters": {"ObjectName": {"value": "@pipeline().parameters.ObjectName", "type": "Expression"}, "ObjectId": {"value": "@pipeline().parameters.ObjectId", "type": "Expression"}}}}]}}], "parameters": {"ObjectName": {"type": "string", "defaultValue": "FinancialAccount"}, "ObjectId": {"type": "string", "defaultValue": "a09f4000003gltQAAQ"}, "SSIDChange": {"type": "int", "defaultValue": 1}}, "folder": {"name": "Internal/SFtoSCD/SF-SCD Real Time"}, "annotations": [], "lastPublishTime": "2023-12-01T04:12:42Z"}, "type": "Microsoft.DataFactory/factories/pipelines"}