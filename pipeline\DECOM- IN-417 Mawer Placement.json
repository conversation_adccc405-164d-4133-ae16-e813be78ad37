{"name": "DECOM- IN-417 Mawer Placement", "properties": {"activities": [{"name": "Generate Mawer placement source file", "type": "Copy", "dependsOn": [{"activity": "Set variable1", "dependencyConditions": ["Succeeded"]}], "policy": {"timeout": "7.00:00:00", "retry": 0, "retryIntervalInSeconds": 30, "secureOutput": false, "secureInput": false}, "userProperties": [], "typeProperties": {"source": {"type": "SqlServerSource", "sqlReaderQuery": "Declare @startdate Datetime;\nset @Startdate=CAST(CURRENT_TIMESTAMP -1 AS DATE);\n\nSELECT        o.ORDER_ID AS orderid, op.PLACE_ID AS placeid, op.EXEC_BROKER AS placebrokerid, op.PLACE_DATE AS placedate, op.BROKER_REASON AS placebrokerreason, op.CREATE_DATE AS placecreatedate,\n                         op.CREATE_USER AS placecreateuser, op.PLACE_QTY AS placeqty, op.EXEC_QTY AS placeexecamt, op.EXEC_PRICE AS placeexecprice, op.EXEC_AMT AS placeexecamt2, op.INSTRUCTION AS placeinstruction,\n                         op.LIMIT_PRICE AS placelimitprice, op.PLACE_DURATION AS placetimeinforce, op.COMMENTS AS placecomments, op.NET_TRADE_IND AS placenettradeindicator, op.FIX_ORDER_ID AS placefixorderid,\n                         op.FIX_CLORDID AS placefixclientorderid, o.TRADE_DATE AS tradedate\nFROM            [MWR-SRV-crSQL1A].[CRDMAINMAWIM].[dbo].[TS_ORDER] AS o INNER JOIN\n                         [MWR-SRV-crSQL1A].[CRDMAINMAWIM].[dbo].[TS_ORDER_PLACEMENT] AS op ON o.ORDER_ID = op.ORDER_ID\nWHERE        (o.TRADE_DATE >=@startdate) AND (o.TRADE_DATE <=@startdate)", "queryTimeout": "02:00:00", "partitionOption": "None"}, "sink": {"type": "DelimitedTextSink", "storeSettings": {"type": "AzureBlobFSWriteSettings"}, "formatSettings": {"type": "DelimitedTextWriteSettings", "quoteAllText": true, "fileExtension": ".txt"}}, "enableStaging": false, "translator": {"type": "TabularTranslator", "typeConversion": true, "typeConversionSettings": {"allowDataTruncation": true, "treatBooleanAsNumber": false}}}, "inputs": [{"referenceName": "ODSExternalData_MawerIHSMarkit_sql", "type": "DatasetReference"}], "outputs": [{"referenceName": "AzureDataLakeStorage_MawerIHSmarkitsink_csv", "type": "DatasetReference", "parameters": {"FileName": {"value": "@variables('FileName')", "type": "Expression"}}}]}, {"name": "Set variable1", "type": "SetVariable", "dependsOn": [], "policy": {"secureOutput": false, "secureInput": false}, "userProperties": [], "typeProperties": {"variableName": "FileName", "value": {"value": "@concat('Mawer Placement_',formatDateTime(convertFromUtc(adddays(utcnow(),-1),'Mountain Standard Time'),'MMddyy'),'.txt')", "type": "Expression"}}}, {"name": "Archive", "type": "Copy", "dependsOn": [{"activity": "Generate Mawer placement source file", "dependencyConditions": ["Succeeded"]}], "policy": {"timeout": "7.00:00:00", "retry": 0, "retryIntervalInSeconds": 30, "secureOutput": false, "secureInput": false}, "userProperties": [], "typeProperties": {"source": {"type": "BinarySource", "storeSettings": {"type": "AzureBlobFSReadSettings", "recursive": false, "wildcardFolderPath": "DDI Migration/Mawer IHSMarkit - workflow/Outputfiles", "wildcardFileName": {"value": "@variables('FileName')", "type": "Expression"}, "deleteFilesAfterCompletion": true}, "formatSettings": {"type": "BinaryReadSettings"}}, "sink": {"type": "BinarySink", "storeSettings": {"type": "AzureBlobFSWriteSettings"}}, "enableStaging": false}, "inputs": [{"referenceName": "AzureDataLakeStorageMawerIHSMarkit_Binary_Source", "type": "DatasetReference"}], "outputs": [{"referenceName": "AzureDataLakeStorage_MawerIHSMarkit_Binary_Sink", "type": "DatasetReference"}]}], "variables": {"FileName": {"type": "String"}}, "folder": {"name": "Internal/DDI/IN-329 DDI Migration/IN-384 Mawer IHSMarkit - TransactionCostAnalysis - Workflow"}, "annotations": [], "lastPublishTime": "2023-12-01T04:11:07Z"}, "type": "Microsoft.DataFactory/factories/pipelines"}