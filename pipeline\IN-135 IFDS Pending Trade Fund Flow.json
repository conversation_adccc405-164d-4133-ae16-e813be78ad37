{"name": "IN-135 IFDS Pending Trade Fund Flow", "properties": {"activities": [{"name": "Stage Data into SQLMI", "type": "Copy", "dependsOn": [], "policy": {"timeout": "7.00:00:00", "retry": 0, "retryIntervalInSeconds": 30, "secureOutput": false, "secureInput": false}, "userProperties": [], "typeProperties": {"source": {"type": "DelimitedTextSource", "additionalColumns": [{"name": "DataDate", "value": {"value": "@pipeline().parameters.pDate", "type": "Expression"}}, {"name": "DataType", "value": {"value": "@pipeline().parameters.pDataType", "type": "Expression"}}, {"name": "FileName", "value": {"value": "@pipeline().parameters.pFileName", "type": "Expression"}}], "storeSettings": {"type": "AzureBlobFSReadSettings", "recursive": false, "enablePartitionDiscovery": false}, "formatSettings": {"type": "DelimitedTextReadSettings"}}, "sink": {"type": "SqlMISink", "preCopyScript": {"value": "@{concat('DELETE [Integration].[ifds].[IFDSPendingTradeFundsStaging] \nWHERE DataDate = ''', pipeline().parameters.pDate, ''' \nAND DataType = ''', pipeline().parameters.pDataType, ''' \nAND FileName = ''', pipeline().parameters.pFileName, '''')}\n", "type": "Expression"}, "writeBehavior": "insert", "sqlWriterUseTableLock": false}, "enableStaging": false, "translator": {"type": "TabularTranslator", "mappings": [{"source": {"name": "Fund", "type": "String", "physicalType": "String"}, "sink": {"name": "Fund", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}, {"source": {"name": "Class", "type": "String", "physicalType": "String"}, "sink": {"name": "Class", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}, {"source": {"name": "Fund Name", "type": "String", "physicalType": "String"}, "sink": {"name": "Fund Name", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}, {"source": {"name": "Broker Code", "type": "String", "physicalType": "String"}, "sink": {"name": "Broker Code", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}, {"source": {"name": "Broker Name", "type": "String", "physicalType": "String"}, "sink": {"name": "Broker Name", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}, {"source": {"name": "Transaction Number", "type": "String", "physicalType": "String"}, "sink": {"name": "Transaction Number", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}, {"source": {"name": "Wire Order", "type": "String", "physicalType": "String"}, "sink": {"name": "Wire Order", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}, {"source": {"name": "Transaction Type", "type": "String", "physicalType": "String"}, "sink": {"name": "Transaction Type", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}, {"source": {"name": "Trade Date", "type": "String", "physicalType": "String"}, "sink": {"name": "Trade Date", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}, {"source": {"name": "Settlement Date", "type": "String", "physicalType": "String"}, "sink": {"name": "Settlement Date", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}, {"source": {"name": "Account", "type": "String", "physicalType": "String"}, "sink": {"name": "Account", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}, {"source": {"name": "Shareholder Name", "type": "String", "physicalType": "String"}, "sink": {"name": "Shareholder Name", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}, {"source": {"name": "Shareholder Group", "type": "String", "physicalType": "String"}, "sink": {"name": "Shareholder Group", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}, {"source": {"name": "Amount", "type": "String", "physicalType": "String"}, "sink": {"name": "Amount", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}, {"source": {"name": "Fee", "type": "String", "physicalType": "String"}, "sink": {"name": "Fee", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}, {"source": {"name": "D/U", "type": "String", "physicalType": "String"}, "sink": {"name": "D/U", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}, {"source": {"name": "Fund Currency", "type": "String", "physicalType": "String"}, "sink": {"name": "Fund Currency", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}, {"source": {"name": "Settlement Currency", "type": "String", "physicalType": "String"}, "sink": {"name": "Settlement Currency", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}, {"source": {"name": "Exchange Rate", "type": "String", "physicalType": "String"}, "sink": {"name": "Exchange Rate", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}, {"source": {"name": "Est Amt in Settle Curr", "type": "String", "physicalType": "String"}, "sink": {"name": "Est Amt in Settle Curr", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}, {"source": {"name": "Estimated Units", "type": "String", "physicalType": "String"}, "sink": {"name": "Estimated Units", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}, {"source": {"name": "User", "type": "String", "physicalType": "String"}, "sink": {"name": "User", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}, {"source": {"name": "Process Date", "type": "String", "physicalType": "String"}, "sink": {"name": "Process Date", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}, {"source": {"name": "Deal Date", "type": "String", "physicalType": "String"}, "sink": {"name": "Deal Date", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}, {"source": {"name": "Deal Time", "type": "String", "physicalType": "String"}, "sink": {"name": "Deal Time", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}, {"source": {"name": "Order Remarks", "type": "String", "physicalType": "String"}, "sink": {"name": "Order Remarks", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}, {"source": {"name": "Network ID", "type": "String", "physicalType": "String"}, "sink": {"name": "Network ID", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}, {"source": {"name": "Status", "type": "String", "physicalType": "String"}, "sink": {"name": "Status", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}, {"source": {"name": "Confirm Required", "type": "String", "physicalType": "String"}, "sink": {"name": "Confirm Required", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}, {"source": {"name": "Account Alt Account Number", "type": "String", "physicalType": "String"}, "sink": {"name": "Account Alt Account Number", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}, {"source": {"name": "Est Amt in Class Curr", "type": "String", "physicalType": "String"}, "sink": {"name": "Est Amt in Class Curr", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}, {"source": {"name": "Order Type", "type": "String", "physicalType": "String"}, "sink": {"name": "Order Type", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}, {"source": {"name": "Payment Type", "type": "String", "physicalType": "String"}, "sink": {"name": "Payment Type", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}, {"source": {"name": "FOF", "type": "String", "physicalType": "String"}, "sink": {"name": "FOF", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}, {"source": {"name": "Rej/Rev Code", "type": "String", "physicalType": "String"}, "sink": {"name": "Reject Code", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}, {"source": {"name": "Rej/Rev <PERSON>", "type": "String", "physicalType": "String"}, "sink": {"name": "Reject Reason", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}, {"source": {"name": "DataDate", "type": "String"}, "sink": {"name": "DataDate", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}, {"source": {"name": "DataType", "type": "String"}, "sink": {"name": "DataType", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}, {"source": {"name": "FileName", "type": "String"}, "sink": {"name": "FileName", "type": "String", "physicalType": "<PERSON><PERSON><PERSON>"}}], "typeConversion": true, "typeConversionSettings": {"allowDataTruncation": false, "treatBooleanAsNumber": false}}}, "inputs": [{"referenceName": "ADL_Delimited_File", "type": "DatasetReference", "parameters": {"FilePath": {"value": "@concat(pipeline().parameters.pADLPath,'/IN')", "type": "Expression"}, "FileName": {"value": "@pipeline().parameters.pFileName", "type": "Expression"}, "ColDelimiter": "|", "FirstRowHeader": true}}], "outputs": [{"referenceName": "SQLMI_MOW_PARAM", "type": "DatasetReference", "parameters": {"DBName": "Integration", "SchemaName": "ifds", "TableName": "IFDSPendingTradeFundsStaging"}}]}, {"name": "Create Output File", "type": "Copy", "dependsOn": [{"activity": "Stage Data into SQLMI", "dependencyConditions": ["Succeeded"]}], "policy": {"timeout": "7.00:00:00", "retry": 0, "retryIntervalInSeconds": 30, "secureOutput": false, "secureInput": false}, "userProperties": [], "typeProperties": {"source": {"type": "SqlMISource", "sqlReaderStoredProcedureName": "ifds.sp_IFDSPendingTradeFunds", "storedProcedureParameters": {"DataDate": {"type": "String", "value": {"value": "@pipeline().parameters.pDate", "type": "Expression"}}, "DataType": {"type": "String", "value": {"value": "@pipeline().parameters.pDataType", "type": "Expression"}}, "FileName": {"type": "String", "value": {"value": "@pipeline().parameters.pFileName", "type": "Expression"}}}, "partitionOption": "None"}, "sink": {"type": "DelimitedTextSink", "storeSettings": {"type": "AzureBlobFSWriteSettings"}, "formatSettings": {"type": "DelimitedTextWriteSettings", "quoteAllText": true, "fileExtension": ".txt"}}, "enableStaging": false, "translator": {"type": "TabularTranslator", "typeConversion": true, "typeConversionSettings": {"allowDataTruncation": true, "treatBooleanAsNumber": false}}}, "inputs": [{"referenceName": "SQLMI_MOW_PARAM", "type": "DatasetReference", "parameters": {"DBName": "Integration", "SchemaName": "ifds", "TableName": "sp_IFDSPendingTradeFunds"}}], "outputs": [{"referenceName": "ADL_Delimited_File", "type": "DatasetReference", "parameters": {"FilePath": {"value": "@concat(pipeline().parameters.pADLPath,'/OUT')", "type": "Expression"}, "FileName": {"value": "@concat('IFDS_PENDING_TRADE_FUNDS_',\npipeline().parameters.pDate,\nsubstring(pipeline().parameters.pDataType,4 , 4),\n'.csv')", "type": "Expression"}, "ColDelimiter": ",", "FirstRowHeader": true}}]}, {"name": "If Send File to SCD", "type": "IfCondition", "dependsOn": [{"activity": "Create Output File", "dependencyConditions": ["Succeeded"]}], "userProperties": [], "typeProperties": {"expression": {"value": "@pipeline().parameters.pSendToSCD", "type": "Expression"}, "ifTrueActivities": [{"name": "Copy Output file to SCD SFTP", "type": "Copy", "dependsOn": [], "policy": {"timeout": "7.00:00:00", "retry": 0, "retryIntervalInSeconds": 30, "secureOutput": false, "secureInput": false}, "userProperties": [], "typeProperties": {"source": {"type": "BinarySource", "storeSettings": {"type": "AzureBlobFSReadSettings", "recursive": false, "deleteFilesAfterCompletion": false}, "formatSettings": {"type": "BinaryReadSettings"}}, "sink": {"type": "BinarySink", "storeSettings": {"type": "SftpWriteSettings", "operationTimeout": "01:00:00", "useTempFileRename": true}}, "enableStaging": false}, "inputs": [{"referenceName": "ADL_Binary_Dir", "type": "DatasetReference", "parameters": {"Path": {"value": "@concat(pipeline().parameters.pADLPath,'/OUT')", "type": "Expression"}}}], "outputs": [{"referenceName": "SCD_SFTP_Binary_Dir", "type": "DatasetReference", "parameters": {"Path": {"value": "@pipeline().parameters.pSCDsftpPath", "type": "Expression"}}}]}]}}, {"name": "Archive OUT", "type": "Copy", "dependsOn": [{"activity": "If Send File to SCD", "dependencyConditions": ["Succeeded"]}], "policy": {"timeout": "7.00:00:00", "retry": 0, "retryIntervalInSeconds": 30, "secureOutput": false, "secureInput": false}, "userProperties": [], "typeProperties": {"source": {"type": "BinarySource", "storeSettings": {"type": "AzureBlobFSReadSettings", "recursive": false, "deleteFilesAfterCompletion": true}, "formatSettings": {"type": "BinaryReadSettings"}}, "sink": {"type": "BinarySink", "storeSettings": {"type": "AzureBlobFSWriteSettings"}}, "enableStaging": false}, "inputs": [{"referenceName": "ADL_Binary_Dir", "type": "DatasetReference", "parameters": {"Path": {"value": "@concat(pipeline().parameters.pADLPath,'/OUT')", "type": "Expression"}}}], "outputs": [{"referenceName": "ADL_Binary_Dir", "type": "DatasetReference", "parameters": {"Path": {"value": "@concat(pipeline().parameters.pADLPath,'/ARCHIVE/',pipeline().parameters.pDate,'/OUT')", "type": "Expression"}}}]}, {"name": "Archive IN", "type": "Copy", "dependsOn": [{"activity": "If Send File to SCD", "dependencyConditions": ["Succeeded"]}], "policy": {"timeout": "7.00:00:00", "retry": 0, "retryIntervalInSeconds": 30, "secureOutput": false, "secureInput": false}, "userProperties": [], "typeProperties": {"source": {"type": "BinarySource", "storeSettings": {"type": "AzureBlobFSReadSettings", "recursive": false, "deleteFilesAfterCompletion": true}, "formatSettings": {"type": "BinaryReadSettings"}}, "sink": {"type": "BinarySink", "storeSettings": {"type": "AzureBlobFSWriteSettings"}}, "enableStaging": false}, "inputs": [{"referenceName": "ADL_Binary_Dir", "type": "DatasetReference", "parameters": {"Path": {"value": "@concat(pipeline().parameters.pADLPath,'/IN')", "type": "Expression"}}}], "outputs": [{"referenceName": "ADL_Binary_Dir", "type": "DatasetReference", "parameters": {"Path": {"value": "@concat(pipeline().parameters.pADLPath,'/ARCHIVE/',pipeline().parameters.pDate,'/IN')", "type": "Expression"}}}]}, {"name": "Stage Data into MOW UAT", "type": "Copy", "dependsOn": [{"activity": "If Condition1", "dependencyConditions": ["Succeeded"]}], "policy": {"timeout": "7.00:00:00", "retry": 0, "retryIntervalInSeconds": 30, "secureOutput": false, "secureInput": false}, "userProperties": [], "typeProperties": {"source": {"type": "SqlMISource", "sqlReaderQuery": {"value": "@{concat('SElect \n[Fund],[Class],[Fund Name],[Broker Code],[Broker Name],[Transaction Number],[Wire Order],[Transaction Type],[Trade Date],[Settlement Date],[Account],[Shareholder Name],[Shareholder Group],[Amount],[Fee],[D/U],[Fund Currency],[Settlement Currency],[Exchange Rate],[Est Amt in Settle Curr],[Estimated Units],[User],[Process Date],[Deal Date],[Deal Time],[Order Remarks],[Network ID],[Status],[Confirm Required],[Account Alt Account Number],[Est Amt in Class Curr],[Order Type],[Payment Type],[FOF],[Reject Code],[Reject Reason],[DataDate],[DataType] From [Integration].[ifds].[IFDSPendingTradeFundsStaging] \nWHERE DataDate = ''',pipeline().parameters.pDate,'''\nAND DataType = ''',pipeline().parameters.pDataType,'''')}", "type": "Expression"}, "partitionOption": "None"}, "sink": {"type": "SqlMISink", "preCopyScript": {"value": "@{concat('DELETE [Integration].[ifds].[IFDSPendingTradeFundsStaging] \nWHERE DataDate = ''',pipeline().parameters.pDate,'''\nAND DataType = ''',pipeline().parameters.pDataType,'''')}", "type": "Expression"}, "writeBehavior": "insert", "sqlWriterUseTableLock": false}, "enableStaging": false, "translator": {"type": "TabularTranslator", "typeConversion": true, "typeConversionSettings": {"allowDataTruncation": false, "treatBooleanAsNumber": false}}}, "inputs": [{"referenceName": "SQLMI_MOW_PARAM", "type": "DatasetReference", "parameters": {"DBName": "Integration", "SchemaName": "ifds", "TableName": "IFDSPendingTradeFundsStaging"}}], "outputs": [{"referenceName": "SQLMI_MOW_UAT_PARAM", "type": "DatasetReference", "parameters": {"DBName": "Integration", "SchemaName": "ifds", "TableName": "IFDSPendingTradeFundsStaging"}}]}, {"name": "If Condition1", "type": "IfCondition", "dependsOn": [{"activity": "Create Output File", "dependencyConditions": ["Succeeded"]}], "userProperties": [], "typeProperties": {"expression": {"value": "@equals(pipeline().parameters.pDataType, 'IFDS1400')\n", "type": "Expression"}, "ifTrueActivities": [{"name": "Create Pending Trades File", "type": "Copy", "dependsOn": [], "policy": {"timeout": "0.12:00:00", "retry": 0, "retryIntervalInSeconds": 30, "secureOutput": false, "secureInput": false}, "userProperties": [], "typeProperties": {"source": {"type": "SqlMISource", "sqlReaderStoredProcedureName": "Capstock.sp_CashManagementDaily_PendingTrade", "storedProcedureParameters": {"DataDate": {"type": "String", "value": {"value": "@pipeline().parameters.pDate", "type": "Expression"}}}, "partitionOption": "None"}, "sink": {"type": "DelimitedTextSink", "storeSettings": {"type": "AzureBlobFSWriteSettings"}, "formatSettings": {"type": "DelimitedTextWriteSettings", "quoteAllText": true, "fileExtension": ".txt"}}, "enableStaging": false, "translator": {"type": "TabularTranslator", "typeConversion": true, "typeConversionSettings": {"allowDataTruncation": true, "treatBooleanAsNumber": false}}}, "inputs": [{"referenceName": "SQLMI_MOW_PARAM", "type": "DatasetReference", "parameters": {"DBName": "Integration", "SchemaName": "Capstock", "TableName": "sp_CashManagementDaily_PendingTrade"}}], "outputs": [{"referenceName": "ADL_Delimited_File", "type": "DatasetReference", "parameters": {"FilePath": "IFDS/Cash Management/OUT/", "FileName": {"value": "@concat('IFDS_Cash_Forecast_PendingTrade_',pipeline().parameters.pDate,'.csv')", "type": "Expression"}, "ColDelimiter": ",", "FirstRowHeader": "True"}}]}, {"name": "Stage Pending Trades File", "type": "Copy", "dependsOn": [{"activity": "Create Pending Trades File", "dependencyConditions": ["Succeeded"]}], "policy": {"timeout": "0.12:00:00", "retry": 0, "retryIntervalInSeconds": 30, "secureOutput": false, "secureInput": false}, "userProperties": [], "typeProperties": {"source": {"type": "SqlMISource", "additionalColumns": [{"name": "RunDate", "value": {"value": "@concat(substring(pipeline().parameters.pDate, 0, 4), '-', substring(pipeline().parameters.pDate, 4, 2), '-', substring(pipeline().parameters.pDate, 6, 2))\n", "type": "Expression"}}, {"name": "LoadedDate", "value": {"value": "@utcNow()", "type": "Expression"}}], "sqlReaderStoredProcedureName": "Capstock.sp_CashManagementDaily_PendingTrade", "storedProcedureParameters": {"DataDate": {"type": "String", "value": {"value": "@pipeline().parameters.pDate", "type": "Expression"}}}, "partitionOption": "None"}, "sink": {"type": "SqlMISink", "preCopyScript": {"value": "@concat('DELETE FROM Capstock.CashManagementDaily_PendingTrade_RESULT WHERE RunDate = ''', pipeline().parameters.pDate, '''')\n", "type": "Expression"}, "writeBehavior": "insert", "sqlWriterUseTableLock": false}, "enableStaging": false, "translator": {"type": "TabularTranslator", "typeConversion": true, "typeConversionSettings": {"allowDataTruncation": true, "treatBooleanAsNumber": false}}}, "inputs": [{"referenceName": "SQLMI_MOW_PARAM", "type": "DatasetReference", "parameters": {"DBName": "Integration", "SchemaName": "Capstock", "TableName": "sp_CashManagementDaily_PendingTrade"}}], "outputs": [{"referenceName": "SQLMI_MOW_PARAM", "type": "DatasetReference", "parameters": {"DBName": "Integration", "SchemaName": "Capstock", "TableName": "CashManagementDaily_PendingTrade_RESULT"}}]}]}}], "parameters": {"pDate": {"type": "string", "defaultValue": "20250319"}, "pADLPath": {"type": "string", "defaultValue": "Custodian Data/IFDS/IFDS Pending Trade Funds"}, "pADLContainer": {"type": "string", "defaultValue": "scdintegration"}, "pSendToSCD": {"type": "bool", "defaultValue": false}, "pSCDsftpPath": {"type": "string"}, "pDataType": {"type": "string", "defaultValue": "IFDS1200"}, "pFileName": {"type": "string", "defaultValue": "MAWTPNDTRDRPT190320251605.csv"}}, "folder": {"name": "IFDS/IFDS Pending Trade Fund Flow"}, "annotations": [], "lastPublishTime": "2025-03-27T21:51:05Z"}, "type": "Microsoft.DataFactory/factories/pipelines"}