{"name": "IN-142 - Northern Trust TXT To CSV Transform", "properties": {"folder": {"name": "Inbound"}, "type": "MappingDataFlow", "typeProperties": {"sources": [{"dataset": {"referenceName": "ADL_NorthernTrust_TEMP", "type": "DatasetReference"}, "name": "NorthernTrustPendingFXDetailByCurrencyRAW"}, {"dataset": {"referenceName": "AzureDataLakeStorage_SFPortfolioCustodian", "type": "DatasetReference"}, "name": "<PERSON><PERSON><PERSON><PERSON>"}], "sinks": [{"dataset": {"referenceName": "ADL_NorthernTrust_TEMP", "type": "DatasetReference"}, "name": "OutputColumnsToCSV"}], "transformations": [{"name": "GetCertainColumns"}, {"name": "AddDerivedColumns", "description": "Creates an explicit mapping for each drifted column"}, {"name": "GroupUniqueId"}, {"name": "SortBuySellValue"}, {"name": "SelectColumns"}, {"name": "JoinToGetPortfolioCode"}, {"name": "FilterWhereConsolidationEqualsAcctNum"}], "script": "source(allowSchemaDrift: true,\n\tvalidateSchema: false,\n\tignoreNoFilesFound: false,\n\twildcardPaths:[(concat('FX/Northern Trust/TEMP/PendingFXDetailByCurrencyTemp.csv'))]) ~> NorthernTrustPendingFXDetailByCurrencyRAW\nsource(output(\n\t\tID as string,\n\t\tSSID as string,\n\t\tCADCustodianAccount as string,\n\t\tUSDCustodianAccount as string\n\t),\n\tallowSchemaDrift: true,\n\tvalidateSchema: false,\n\tignoreNoFilesFound: false,\n\twildcardPaths:['FX/Northern Trust/TEMP/CustodianPortfolioMapping.csv']) ~> Custodian\nNorthernTrustPendingFXDetailByCurrencyRAW select(skipDuplicateMapInputs: true,\n\tskipDuplicateMapOutputs: true) ~> GetCertainColumns\nGetCertainColumns derive(UniqueId = concat(toString(byName('Consolidation')), '-', toString(byName('Account Number')), '-', toString(byName('I-TRAN'))),\n\t\tConsolidation = toString(byName('Consolidation')),\n\t\t{Account Number} = toString(byName('Account Number')),\n\t\t{From date} = toString(byName('From date')),\n\t\t{Through date} = toString(byName('Through date')),\n\t\t{D-CNTRT-ENTR} = toString(toDate(toString(byName('D-CNTRT-ENTR')), \"yyyy-MM-dd\"),\"dd-MM-yyyy\"),\n\t\t{D-CNTRT-SET-CASH} = toString(toDate(toString(byName('D-CNTRT-SET-CASH')), \"yyyy-MM-dd\"),\"dd-MM-yyyy\"),\n\t\t{T-NARR-LONG} = toString(byName('T-NARR-LONG')),\n\t\t{C-CURR} = toString(byName('C-CURR')),\n\t\t{A-FX-BUY-SELL} = toString(byName('A-FX-BUY-SELL')),\n\t\t{A-MV-BSE} = toString(byName('A-MV-BSE')),\n\t\t{A-PURCH-SALE-BSE} = toString(byName('A-PURCH-SALE-BSE')),\n\t\t{A-EXCH-GNLS-BSE} = toString(byName('A-EXCH-GNLS-BSE')),\n\t\t{C-FX-TYPE} = toString(byName('C-FX-TYPE')),\n\t\t{I-TRAN} = toString(byName('I-TRAN')),\n\t\t{C-PURCH-SALE} = toString(byName('C-PURCH-SALE')),\n\t\t{R-EXCH} = toString(byName('R-EXCH')),\n\t\t{C-VALN-TYPE-O} = toString(byName('C-VALN-TYPE-O')),\n\t\t{Error code} = toString(byName('Error code')),\n\t\t{Instrument Type} = iif(minus(toDate(byName('D-CNTRT-SET-CASH')), toDate(byName('D-CNTRT-ENTR'))) == 3, 'FX Spot', 'FX Spot'),\n\t\t{Transaction origin Trade ID} = substring(replace(uuid(), \"-\", \"\") , 1, 30)) ~> AddDerivedColumns\nSortBuySellValue aggregate(groupBy(UniqueId),\n\t{From Date} = max({From date}),\n\t\t{Sold Currency} = iif(min(toLong({A-FX-BUY-SELL})) < 0, last({C-CURR}), ''),\n\t\t{Buy Currency} = iif(max(toLong({A-FX-BUY-SELL})) > 0, first({C-CURR}), ''),\n\t\t{Sold Amount} = abs(iif(min(toDecimal({A-FX-BUY-SELL},20,2)) < 0, min(toDecimal({A-FX-BUY-SELL},20,2)), toDecimal(0))),\n\t\t{Buy Amount} = iif(max(toDecimal({A-FX-BUY-SELL},20,2)) > 0, max(toDecimal({A-FX-BUY-SELL},20,2)), toDecimal(0)),\n\t\tConsolidation = first(Consolidation),\n\t\t{Account Number} = first({Account Number}),\n\t\t{From date} = first({From date}),\n\t\t{Through date} = first({Through date}),\n\t\t{D-CNTRT-ENTR} = first({D-CNTRT-ENTR}),\n\t\t{D-CNTRT-SET-CASH} = first({D-CNTRT-SET-CASH}),\n\t\t{T-NARR-LONG} = first({T-NARR-LONG}),\n\t\t{C-CURR} = first({C-CURR}),\n\t\t{A-FX-BUY-SELL} = first({A-FX-BUY-SELL}),\n\t\t{A-MV-BSE} = first({A-MV-BSE}),\n\t\t{A-PURCH-SALE-BSE} = first({A-PURCH-SALE-BSE}),\n\t\t{A-EXCH-GNLS-BSE} = first({A-EXCH-GNLS-BSE}),\n\t\t{C-FX-TYPE} = first({C-FX-TYPE}),\n\t\t{I-TRAN} = first({I-TRAN}),\n\t\t{C-PURCH-SALE} = first({C-PURCH-SALE}),\n\t\t{R-EXCH} = first({R-EXCH}),\n\t\t{C-VALN-TYPE-O} = first({C-VALN-TYPE-O}),\n\t\t{Error code} = first({Error code}),\n\t\t{Instrument Type} = first({Instrument Type}),\n\t\t{Transaction origin Trade ID} = first({Transaction origin Trade ID})) ~> GroupUniqueId\nAddDerivedColumns sort(desc({A-FX-BUY-SELL}, true)) ~> SortBuySellValue\nJoinToGetPortfolioCode select(mapColumn(\n\t\tUniqueId,\n\t\t{From Date} = GroupUniqueId@{From Date},\n\t\t{Sold Currency},\n\t\t{Buy Currency},\n\t\t{Sold Amount},\n\t\t{Buy Amount},\n\t\tConsolidation,\n\t\t{Account Number} = SSID,\n\t\t{From date} = GroupUniqueId@{From date},\n\t\t{Through date},\n\t\t{D-CNTRT-ENTR},\n\t\t{D-CNTRT-SET-CASH},\n\t\t{T-NARR-LONG},\n\t\t{C-CURR},\n\t\t{A-FX-BUY-SELL},\n\t\t{A-MV-BSE},\n\t\t{A-PURCH-SALE-BSE},\n\t\t{A-EXCH-GNLS-BSE},\n\t\t{C-FX-TYPE},\n\t\t{I-TRAN},\n\t\t{C-PURCH-SALE},\n\t\t{R-EXCH},\n\t\t{C-VALN-TYPE-O},\n\t\t{Error code},\n\t\t{Instrument Type},\n\t\t{Transaction origin Trade ID}\n\t),\n\tskipDuplicateMapInputs: true,\n\tskipDuplicateMapOutputs: true) ~> SelectColumns\nFilterWhereConsolidationEqualsAcctNum, Custodian join({Account Number} == CADCustodianAccount,\n\tjoinType:'inner',\n\tmatchType:'exact',\n\tignoreSpaces: false,\n\tbroadcast: 'auto')~> JoinToGetPortfolioCode\nGroupUniqueId filter(Consolidation == {Account Number}) ~> FilterWhereConsolidationEqualsAcctNum\nSelectColumns sink(allowSchemaDrift: true,\n\tvalidateSchema: false,\n\tpartitionFileNames:['Pending_FX_Detail_by_Currency.csv'],\n\tumask: 0022,\n\tpreCommands: [],\n\tpostCommands: [],\n\tskipDuplicateMapInputs: true,\n\tskipDuplicateMapOutputs: true,\n\tpartitionBy('hash', 1)) ~> OutputColumnsToCSV"}}}