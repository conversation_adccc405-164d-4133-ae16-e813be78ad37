{"name": "DECOM- IN-180 Rimes Revision Outer Loop Daily - CHILD_V2", "properties": {"activities": [{"name": "Find latest revisions inner loop", "type": "ForEach", "dependsOn": [], "userProperties": [], "typeProperties": {"items": {"value": "@pipeline().parameters.file_names", "type": "Expression"}, "isSequential": true, "activities": [{"name": "If starts with name and greater than latest", "type": "IfCondition", "dependsOn": [], "userProperties": [], "typeProperties": {"expression": {"value": "@and(\n    startswith(item().name, pipeline().parameters.file_name),\n    greater(item().name, variables('latest_file_name'))\n)", "type": "Expression"}, "ifTrueActivities": [{"name": "Updatest latest file name", "type": "SetVariable", "dependsOn": [], "policy": {"secureOutput": false, "secureInput": false}, "userProperties": [], "typeProperties": {"variableName": "latest_file_name", "value": {"value": "@item().name", "type": "Expression"}}}, {"name": "Set file date", "type": "SetVariable", "dependsOn": [], "policy": {"secureOutput": false, "secureInput": false}, "userProperties": [], "typeProperties": {"variableName": "file_date", "value": {"value": "@last(split(substring(item().name, 0, lastindexof(item().name, '_')), '_'))", "type": "Expression"}}}]}}]}}, {"name": "Send to SCD", "type": "IfCondition", "dependsOn": [{"activity": "Copy from IN to OUT", "dependencyConditions": ["Succeeded"]}], "userProperties": [], "typeProperties": {"expression": {"value": "@pipeline().parameters.send_to_scd", "type": "Expression"}, "ifTrueActivities": [{"name": "Send to SCD Rimes", "type": "Copy", "dependsOn": [], "policy": {"timeout": "7.00:00:00", "retry": 0, "retryIntervalInSeconds": 30, "secureOutput": false, "secureInput": false}, "userProperties": [], "typeProperties": {"source": {"type": "DelimitedTextSource", "storeSettings": {"type": "AzureBlobFSReadSettings", "recursive": true, "enablePartitionDiscovery": false}, "formatSettings": {"type": "DelimitedTextReadSettings"}}, "sink": {"type": "DelimitedTextSink", "storeSettings": {"type": "SftpWriteSettings", "operationTimeout": "01:00:00", "useTempFileRename": true}, "formatSettings": {"type": "DelimitedTextWriteSettings", "quoteAllText": true, "fileExtension": ""}}, "enableStaging": false, "translator": {"type": "TabularTranslator", "typeConversion": true, "typeConversionSettings": {"allowDataTruncation": true, "treatBooleanAsNumber": false}}}, "inputs": [{"referenceName": "AzureDataLakeStorage_Rimes_File", "type": "DatasetReference", "parameters": {"dir_name": "OUT/", "file_name": "@variables('latest_file_name')"}}], "outputs": [{"referenceName": "SFTP_SCD_RIMES_Parameterized_Directory", "type": "DatasetReference", "parameters": {"FilePath": {"value": "@concat(pipeline().globalParameters.SCD_ENV, '/ActiveData/Interfaces/In/SFTP/Rimes/Working')", "type": "Expression"}, "FileName": "@variables('latest_file_name')", "col_delim": ";"}}]}, {"name": "Send to SCD StaticData", "type": "Copy", "dependsOn": [], "policy": {"timeout": "7.00:00:00", "retry": 0, "retryIntervalInSeconds": 30, "secureOutput": false, "secureInput": false}, "userProperties": [], "typeProperties": {"source": {"type": "DelimitedTextSource", "storeSettings": {"type": "AzureBlobFSReadSettings", "recursive": true, "enablePartitionDiscovery": false}, "formatSettings": {"type": "DelimitedTextReadSettings"}}, "sink": {"type": "DelimitedTextSink", "storeSettings": {"type": "SftpWriteSettings", "operationTimeout": "01:00:00", "useTempFileRename": true}, "formatSettings": {"type": "DelimitedTextWriteSettings", "quoteAllText": true, "fileExtension": ""}}, "enableStaging": false, "translator": {"type": "TabularTranslator", "typeConversion": true, "typeConversionSettings": {"allowDataTruncation": true, "treatBooleanAsNumber": false}}}, "inputs": [{"referenceName": "AzureDataLakeStorage_Rimes_File", "type": "DatasetReference", "parameters": {"dir_name": "OUT/", "file_name": "@variables('latest_file_name')"}}], "outputs": [{"referenceName": "SFTP_SCD_RIMES_Parameterized_Directory", "type": "DatasetReference", "parameters": {"FilePath": "@concat(pipeline().globalParameters.SCD_ENV, '/ActiveData/Interfaces/In/SFTP/STATIC_DATA/Working')", "FileName": "@variables('latest_file_name')", "col_delim": ";"}}]}]}}, {"name": "Copy latest file to IN", "type": "Copy", "dependsOn": [{"activity": "Find latest revisions inner loop", "dependencyConditions": ["Succeeded"]}], "policy": {"timeout": "7.00:00:00", "retry": 0, "retryIntervalInSeconds": 30, "secureOutput": false, "secureInput": false}, "userProperties": [], "typeProperties": {"source": {"type": "DelimitedTextSource", "storeSettings": {"type": "SftpReadSettings", "recursive": false, "wildcardFileName": {"value": "@variables('latest_file_name')", "type": "Expression"}, "enablePartitionDiscovery": false}, "formatSettings": {"type": "DelimitedTextReadSettings"}}, "sink": {"type": "DelimitedTextSink", "storeSettings": {"type": "AzureBlobFSWriteSettings"}, "formatSettings": {"type": "DelimitedTextWriteSettings", "quoteAllText": true, "fileExtension": ".txt"}}, "enableStaging": false, "translator": {"type": "TabularTranslator", "typeConversion": true, "typeConversionSettings": {"allowDataTruncation": true, "treatBooleanAsNumber": false}}}, "inputs": [{"referenceName": "SFTP_Rimes", "type": "DatasetReference"}], "outputs": [{"referenceName": "AzureDataLakeStorage_Rimes_File", "type": "DatasetReference", "parameters": {"dir_name": "IN/", "file_name": {"value": "@variables('latest_file_name')", "type": "Expression"}}}]}, {"name": "Copy from OUT to ADX", "type": "Copy", "dependsOn": [{"activity": "Send to SCD", "dependencyConditions": ["Succeeded"]}], "policy": {"timeout": "7.00:00:00", "retry": 0, "retryIntervalInSeconds": 30, "secureOutput": false, "secureInput": false}, "userProperties": [], "typeProperties": {"source": {"type": "DelimitedTextSource", "additionalColumns": [{"name": "FileName", "value": {"value": "@variables('latest_file_name')", "type": "Expression"}}, {"name": "ADFRunTimeUTC", "value": {"value": "@utcnow()", "type": "Expression"}}], "storeSettings": {"type": "AzureBlobFSReadSettings", "recursive": true, "enablePartitionDiscovery": false}, "formatSettings": {"type": "DelimitedTextReadSettings", "skipLineCount": 1}}, "sink": {"type": "AzureDataExplorerSink", "ingestionMappingName": ""}, "enableStaging": false, "translator": {"type": "TabularTranslator", "typeConversion": true, "typeConversionSettings": {"allowDataTruncation": true, "treatBooleanAsNumber": false}}}, "inputs": [{"referenceName": "AzureDataLakeStorage_Rimes_File", "type": "DatasetReference", "parameters": {"dir_name": "OUT/", "file_name": {"value": "@variables('latest_file_name')", "type": "Expression"}}}], "outputs": [{"referenceName": "ADX_RIMES_Index_Data", "type": "DatasetReference", "parameters": {"table_name": {"value": "@if(\n    contains(\n        variables('latest_file_name'),\n        '_IDX_'\n    ),\n    'RimesIdx',\n    if(\n        startswith(\n            variables('latest_file_name'),\n            'FTX'\n        ),\n        if(\n            contains(\n                variables('latest_file_name'),\n                '_SOD_'\n            ),\n            'RimesConsFISOD',\n            'RimesConsFINDO'\n        ),\n        if(\n            contains(\n                variables('latest_file_name'),\n                '_SOD_'\n            ),\n            'RimesConsSOD',\n            'RimesConsNDO'\n        )\n    )\n)", "type": "Expression"}}}]}, {"name": "Log in ADX", "type": "AzureDataExplorerCommand", "dependsOn": [{"activity": "Copy from OUT to ADX", "dependencyConditions": ["Succeeded"]}], "policy": {"timeout": "7.00:00:00", "retry": 0, "retryIntervalInSeconds": 30, "secureOutput": false, "secureInput": false}, "userProperties": [], "typeProperties": {"command": {"value": ".ingest inline into table RimesADFLog <|\n@{utcnow()},@{pipeline().parameters.origin_run_id},true,@{variables('latest_file_name')},@{first(split(variables('latest_file_name'), '_'))},@{first(skip(split(variables('latest_file_name'), '_'), 1))},@{if(contains(variables('latest_file_name'), '_CONS_'), 'CONS', 'IDX')}", "type": "Expression"}, "commandTimeout": "00:20:00"}, "linkedServiceName": {"referenceName": "AzureDataExplorer", "type": "LinkedServiceReference", "parameters": {"DatabaseName": "RIMES Index Data"}}}, {"name": "Copy from IN to ARCHIVE IN", "type": "Copy", "dependsOn": [{"activity": "Copy from OUT to ADX", "dependencyConditions": ["Succeeded"]}], "policy": {"timeout": "7.00:00:00", "retry": 8, "retryIntervalInSeconds": 30, "secureOutput": false, "secureInput": false}, "userProperties": [], "typeProperties": {"source": {"type": "BinarySource", "storeSettings": {"type": "AzureBlobFSReadSettings", "recursive": true, "deleteFilesAfterCompletion": true}, "formatSettings": {"type": "BinaryReadSettings"}}, "sink": {"type": "BinarySink", "storeSettings": {"type": "AzureBlobFSWriteSettings"}}, "enableStaging": false}, "inputs": [{"referenceName": "ADL_Binary_File", "type": "DatasetReference", "parameters": {"FileName": "@variables('latest_file_name')", "Path": "RIMES/IN/", "Container": "scdintegration"}}], "outputs": [{"referenceName": "ADL_Binary_File", "type": "DatasetReference", "parameters": {"FileName": "@variables('latest_file_name')", "Path": "RIMES/ARCHIVE/IN/@{variables('file_date')}", "Container": "scdintegration"}}]}, {"name": "Copy from OUT to ARCHIVE OUT", "type": "Copy", "dependsOn": [{"activity": "Copy from OUT to ADX", "dependencyConditions": ["Succeeded"]}], "policy": {"timeout": "7.00:00:00", "retry": 8, "retryIntervalInSeconds": 30, "secureOutput": false, "secureInput": false}, "userProperties": [], "typeProperties": {"source": {"type": "BinarySource", "storeSettings": {"type": "AzureBlobFSReadSettings", "recursive": true, "deleteFilesAfterCompletion": true}, "formatSettings": {"type": "BinaryReadSettings"}}, "sink": {"type": "BinarySink", "storeSettings": {"type": "AzureBlobFSWriteSettings"}}, "enableStaging": false}, "inputs": [{"referenceName": "ADL_Binary_File", "type": "DatasetReference", "parameters": {"FileName": "@variables('latest_file_name')", "Path": "RIMES/OUT/", "Container": "scdintegration"}}], "outputs": [{"referenceName": "ADL_Binary_File", "type": "DatasetReference", "parameters": {"FileName": "@variables('latest_file_name')", "Path": "RIMES/ARCHIVE/OUT/@{variables('file_date')}", "Container": "scdintegration"}}]}, {"name": "Copy from IN to OUT", "type": "Copy", "dependsOn": [{"activity": "Copy latest file to IN", "dependencyConditions": ["Succeeded"]}], "policy": {"timeout": "7.00:00:00", "retry": 8, "retryIntervalInSeconds": 30, "secureOutput": false, "secureInput": false}, "userProperties": [], "typeProperties": {"source": {"type": "BinarySource", "storeSettings": {"type": "AzureBlobFSReadSettings", "recursive": true, "deleteFilesAfterCompletion": false}, "formatSettings": {"type": "BinaryReadSettings"}}, "sink": {"type": "BinarySink", "storeSettings": {"type": "AzureBlobFSWriteSettings"}}, "enableStaging": false}, "inputs": [{"referenceName": "ADL_Binary_File", "type": "DatasetReference", "parameters": {"FileName": "@variables('latest_file_name')", "Path": "RIMES/IN/", "Container": "scdintegration"}}], "outputs": [{"referenceName": "ADL_Binary_File", "type": "DatasetReference", "parameters": {"FileName": "@variables('latest_file_name')", "Path": "RIMES/OUT/", "Container": "scdintegration"}}]}], "parameters": {"file_name": {"type": "string"}, "file_names": {"type": "array"}, "origin_run_id": {"type": "string"}, "send_to_scd": {"type": "bool"}}, "variables": {"latest_file_name": {"type": "String", "defaultValue": "@pipeline().parameters.file_name"}, "file_date": {"type": "String", "defaultValue": "@last(split(substring(pipeline().parameters.file_name, 0, lastindexof(pipeline().parameters.file_name, '_')), '_'))"}}, "folder": {"name": "Internal/RIMES to SCD"}, "annotations": [], "lastPublishTime": "2023-12-01T04:12:15Z"}, "type": "Microsoft.DataFactory/factories/pipelines"}