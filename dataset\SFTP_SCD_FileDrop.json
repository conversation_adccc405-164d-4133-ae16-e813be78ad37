{"name": "SFTP_SCD_FileDrop", "properties": {"linkedServiceName": {"referenceName": "SCD_SFTP", "type": "LinkedServiceReference"}, "parameters": {"TargetFilePath": {"type": "string"}, "TargetFileName": {"type": "string"}}, "folder": {"name": "Inbound/FileDrop"}, "annotations": [], "type": "Binary", "typeProperties": {"location": {"type": "SftpLocation", "fileName": {"value": "@dataset().TargetFileName", "type": "Expression"}, "folderPath": {"value": "@dataset().TargetFilePath", "type": "Expression"}}}}, "type": "Microsoft.DataFactory/factories/datasets"}