{"name": "DECOM- IN-415 Mawer Orderhistory Flow", "properties": {"activities": [{"name": "Generate Mawer order history source file", "type": "Copy", "dependsOn": [{"activity": "Set variable1", "dependencyConditions": ["Succeeded"]}], "policy": {"timeout": "7.00:00:00", "retry": 0, "retryIntervalInSeconds": 30, "secureOutput": false, "secureInput": false}, "userProperties": [], "typeProperties": {"source": {"type": "SqlServerSource", "sqlReaderQuery": "Declare @startdate Datetime;\nset @Startdate=CAST(CURRENT_TIMESTAMP -1 AS DATE);\nSELECT        o.ORDER_ID AS orderid, oh.ACT_TIMESTAMP AS actiondate, oh.PARENT_ORDER_ID AS parentorderid, oh.ACTION_TYPE AS actiontype, oh.USER_ID AS actionuserid, REPLACE(REPLACE(oh.MSG_TEXT, CHAR(10),\n                          ' '), CHAR(13), '') AS messagetext, oh.SHARES_OPEN AS sharesopen, oh.SHARES_ACTION AS sharesaction, oh.FUNC_FROM AS functionid, oh.SEQ AS seqid, oh.SCNDRY_ID AS secondaryid,\n                         oh.SCNDRY_ID_TYPE AS seconaryidtype, o.TRADE_DATE AS tradedate\nFROM            [MWR-SRV-crSQL1A].[CRDMAINMAWIM].[dbo].TS_ORDER AS o INNER JOIN\n                         [MWR-SRV-crSQL1A].[CRDMAINMAWIM].[dbo].TS_ORDER_HISTORY AS oh ON o.ORDER_ID = oh.ORDER_ID\nWHERE        (o.TRADE_DATE >=@startdate) AND (o.TRADE_DATE <=@startdate)", "queryTimeout": "02:00:00", "partitionOption": "None"}, "sink": {"type": "DelimitedTextSink", "storeSettings": {"type": "AzureBlobFSWriteSettings"}, "formatSettings": {"type": "DelimitedTextWriteSettings", "quoteAllText": true, "fileExtension": ".txt"}}, "enableStaging": false, "translator": {"type": "TabularTranslator", "typeConversion": true, "typeConversionSettings": {"allowDataTruncation": true, "treatBooleanAsNumber": false}}}, "inputs": [{"referenceName": "ODSExternalData_MawerIHSMarkit_sql", "type": "DatasetReference"}], "outputs": [{"referenceName": "AzureDataLakeStorage_MawerIHSmarkitsink_csv", "type": "DatasetReference", "parameters": {"FileName": {"value": "@variables('FileName')", "type": "Expression"}}}]}, {"name": "Set variable1", "type": "SetVariable", "dependsOn": [], "policy": {"secureOutput": false, "secureInput": false}, "userProperties": [], "typeProperties": {"variableName": "FileName", "value": {"value": "@concat('Mawer OrderHistory_',formatDateTime(convertFromUtc(adddays(utcnow(),-1),'Mountain Standard Time'),'MMddyy'),'.txt')", "type": "Expression"}}}, {"name": "Archive", "type": "Copy", "dependsOn": [{"activity": "Generate Mawer order history source file", "dependencyConditions": ["Succeeded"]}], "policy": {"timeout": "7.00:00:00", "retry": 0, "retryIntervalInSeconds": 30, "secureOutput": false, "secureInput": false}, "userProperties": [], "typeProperties": {"source": {"type": "BinarySource", "storeSettings": {"type": "AzureBlobFSReadSettings", "recursive": false, "wildcardFolderPath": "DDI Migration/Mawer IHSMarkit - workflow/Outputfiles", "wildcardFileName": {"value": "@variables('FileName')", "type": "Expression"}, "deleteFilesAfterCompletion": true}, "formatSettings": {"type": "BinaryReadSettings"}}, "sink": {"type": "BinarySink", "storeSettings": {"type": "AzureBlobFSWriteSettings"}}, "enableStaging": false}, "inputs": [{"referenceName": "AzureDataLakeStorageMawerIHSMarkit_Binary_Source", "type": "DatasetReference"}], "outputs": [{"referenceName": "AzureDataLakeStorage_MawerIHSMarkit_Binary_Sink", "type": "DatasetReference"}]}], "variables": {"FileName": {"type": "String"}}, "folder": {"name": "Internal/DDI/IN-329 DDI Migration/IN-384 Mawer IHSMarkit - TransactionCostAnalysis - Workflow"}, "annotations": [], "lastPublishTime": "2023-12-01T04:11:28Z"}, "type": "Microsoft.DataFactory/factories/pipelines"}