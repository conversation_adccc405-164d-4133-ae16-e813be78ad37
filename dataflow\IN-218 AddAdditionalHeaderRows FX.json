{"name": "IN-218 AddAdditionalHeaderRows FX", "properties": {"folder": {"name": "Inbound"}, "type": "MappingDataFlow", "typeProperties": {"sources": [{"dataset": {"referenceName": "AzureDataLakeStorage_FX_01", "type": "DatasetReference"}, "name": "HeaderFiles"}, {"dataset": {"referenceName": "AzureDataLakeStorage_FX_01", "type": "DatasetReference"}, "name": "FXData"}], "sinks": [{"dataset": {"referenceName": "AzureDataLakeStorage_FX_01", "type": "DatasetReference"}, "name": "OutputToCSV"}], "transformations": [{"name": "UnionDummyHeaderFileAndFxDataFile"}, {"name": "SelectColumns"}], "script": "parameters{\n\toutputFileName as string,\n\toutputDirectory as string\n}\nsource(output(\n\t\t{1} as string,\n\t\t{2} as string,\n\t\t{3} as string,\n\t\t{4} as string,\n\t\t{5} as string,\n\t\t{6} as string,\n\t\t{7} as string,\n\t\t{8} as string,\n\t\t{9} as string,\n\t\t{10} as string,\n\t\t{11} as string,\n\t\t{12} as string,\n\t\t{13} as string,\n\t\t{14} as string,\n\t\t{15} as string,\n\t\t{16} as string,\n\t\t{17} as string,\n\t\t{18} as string,\n\t\t{19} as string,\n\t\t{20} as string,\n\t\t{21} as string,\n\t\t{22} as string,\n\t\t{23} as string,\n\t\t{24} as string,\n\t\t{25} as string,\n\t\t{26} as string,\n\t\t{27} as string,\n\t\t{28} as string,\n\t\t{29} as string,\n\t\t{30} as string,\n\t\t{31} as string,\n\t\t{32} as string,\n\t\t{33} as string,\n\t\t{34} as string,\n\t\t{35} as string,\n\t\t{36} as string,\n\t\t{37} as string,\n\t\t{38} as string,\n\t\t{39} as string,\n\t\t{40} as string,\n\t\t{41} as string,\n\t\t{42} as string,\n\t\t{43} as string,\n\t\t{44} as string,\n\t\t{45} as string,\n\t\t{46} as string,\n\t\t{47} as string,\n\t\t{48} as string,\n\t\t{49} as string,\n\t\t{50} as string,\n\t\t{51} as string,\n\t\t{52} as string,\n\t\t{53} as string,\n\t\t{54} as string,\n\t\t{55} as string,\n\t\t{56} as string,\n\t\t{57} as string,\n\t\t{58} as string,\n\t\t{59} as string,\n\t\t{60} as string,\n\t\t{61} as string,\n\t\t{62} as string,\n\t\t{63} as string,\n\t\t{64} as string,\n\t\t{65} as string,\n\t\t{66} as string,\n\t\t{67} as string,\n\t\t{68} as string,\n\t\t{69} as string,\n\t\t{70} as string,\n\t\t{71} as string,\n\t\t{72} as string,\n\t\t{73} as string,\n\t\t{74} as string,\n\t\t{75} as string,\n\t\t{76} as string,\n\t\t{77} as string,\n\t\t{78} as string,\n\t\t{79} as string,\n\t\t{80} as string,\n\t\t{81} as string,\n\t\t{82} as string,\n\t\t{83} as string,\n\t\t{84} as string,\n\t\t{85} as string,\n\t\t{86} as string,\n\t\t{87} as string,\n\t\t{88} as string,\n\t\t{89} as string,\n\t\t{90} as string,\n\t\t{91} as string,\n\t\t{92} as string,\n\t\t{93} as string,\n\t\t{94} as string,\n\t\t{95} as string,\n\t\t{96} as string,\n\t\t{97} as string,\n\t\t{98} as string,\n\t\t{99} as string,\n\t\t{100} as string,\n\t\t{101} as string,\n\t\t{102} as string,\n\t\t{103} as string,\n\t\t{104} as string,\n\t\t{105} as string,\n\t\t{106} as string,\n\t\t{107} as string,\n\t\t{108} as string,\n\t\t{109} as string,\n\t\t{110} as string,\n\t\t{111} as string,\n\t\t{112} as string,\n\t\t{113} as string,\n\t\t{114} as string,\n\t\t{115} as string,\n\t\t{116} as string,\n\t\t{117} as string,\n\t\t{118} as string,\n\t\t{119} as string,\n\t\t{120} as string,\n\t\t{121} as string,\n\t\t{122} as string,\n\t\t{123} as string,\n\t\t{124} as string,\n\t\t{125} as string,\n\t\t{126} as string,\n\t\t{127} as string,\n\t\t{128} as string,\n\t\t{129} as string,\n\t\t{130} as string,\n\t\t{131} as string,\n\t\t{132} as string,\n\t\t{133} as string,\n\t\t{134} as string,\n\t\t{135} as string,\n\t\t{136} as string,\n\t\t{137} as string,\n\t\t{138} as string,\n\t\t{139} as string,\n\t\t{140} as string,\n\t\t{141} as string,\n\t\t{142} as string,\n\t\t{143} as string,\n\t\t{144} as string,\n\t\t{145} as string,\n\t\t{146} as string,\n\t\t{147} as string,\n\t\t{148} as string,\n\t\t{149} as string,\n\t\t{150} as string,\n\t\t{151} as string,\n\t\t{152} as string,\n\t\t{153} as string,\n\t\t{154} as string,\n\t\t{155} as string,\n\t\t{156} as string,\n\t\t{157} as string,\n\t\t{158} as string,\n\t\t{159} as string,\n\t\t{160} as string,\n\t\t{161} as string,\n\t\t{162} as string,\n\t\t{163} as string,\n\t\t{164} as string,\n\t\t{165} as string,\n\t\t{166} as string,\n\t\t{167} as string,\n\t\t{168} as string,\n\t\t{169} as string,\n\t\t{170} as string,\n\t\t{171} as string,\n\t\t{172} as string,\n\t\t{173} as string,\n\t\t{174} as string,\n\t\t{175} as string,\n\t\t{176} as string,\n\t\t{177} as string,\n\t\t{178} as string,\n\t\t{179} as string,\n\t\t{180} as string,\n\t\t{181} as string,\n\t\t{182} as string,\n\t\t{183} as string,\n\t\t{184} as string,\n\t\t{185} as string,\n\t\t{186} as string,\n\t\t{187} as string,\n\t\t{188} as string,\n\t\t{189} as string,\n\t\t{190} as string,\n\t\t{191} as string,\n\t\t{192} as string,\n\t\t{193} as string,\n\t\t{194} as string,\n\t\t{195} as string,\n\t\t{196} as string,\n\t\t{197} as string,\n\t\t{198} as string,\n\t\t{199} as string,\n\t\t{200} as string,\n\t\t{201} as string,\n\t\t{202} as string,\n\t\t{203} as string,\n\t\t{204} as string,\n\t\t{205} as string,\n\t\t{206} as string,\n\t\t{207} as string,\n\t\t{208} as string,\n\t\t{209} as string,\n\t\t{210} as string,\n\t\t{211} as string,\n\t\t{212} as string,\n\t\t{213} as string,\n\t\t{214} as string,\n\t\t{215} as string,\n\t\t{216} as string,\n\t\t{217} as string,\n\t\t{218} as string,\n\t\t{219} as string,\n\t\t{220} as string,\n\t\t{221} as string,\n\t\t{222} as string,\n\t\t{223} as string,\n\t\t{224} as string,\n\t\t{225} as string,\n\t\t{226} as string,\n\t\t{227} as string,\n\t\t{228} as string,\n\t\t{229} as string,\n\t\t{230} as string,\n\t\t{231} as string,\n\t\t{232} as string,\n\t\t{233} as string,\n\t\t{234} as string,\n\t\t{235} as string,\n\t\t{236} as string,\n\t\t{237} as string,\n\t\t{238} as string,\n\t\t{239} as string,\n\t\t{240} as string,\n\t\t{241} as string,\n\t\t{242} as string,\n\t\t{243} as string,\n\t\t{244} as string,\n\t\t{245} as string,\n\t\t{246} as string,\n\t\t{247} as string,\n\t\t{248} as string,\n\t\t{249} as string,\n\t\t{250} as string,\n\t\t{251} as string,\n\t\t{252} as string,\n\t\t{253} as string,\n\t\t{254} as string,\n\t\t{255} as string,\n\t\t{256} as string,\n\t\t{257} as string,\n\t\t{258} as string,\n\t\t{259} as string,\n\t\t{260} as string,\n\t\t{261} as string,\n\t\t{262} as string,\n\t\t{263} as string,\n\t\t{264} as string,\n\t\t{265} as string,\n\t\t{266} as string,\n\t\t{267} as string,\n\t\t{268} as string,\n\t\t{269} as string,\n\t\t{270} as string,\n\t\t{271} as string,\n\t\t{272} as string,\n\t\t{273} as string,\n\t\t{274} as string,\n\t\t{275} as string,\n\t\t{276} as string,\n\t\t{277} as string,\n\t\t{278} as string\n\t),\n\tallowSchemaDrift: true,\n\tvalidateSchema: false,\n\tignoreNoFilesFound: false,\n\twildcardPaths:['FX/FX_Additional_Headers.csv']) ~> HeaderFiles\nsource(output(\n\t\t{1} as string,\n\t\t{2} as string,\n\t\t{3} as string,\n\t\t{4} as string,\n\t\t{5} as string,\n\t\t{6} as string,\n\t\t{7} as string,\n\t\t{8} as string,\n\t\t{9} as string,\n\t\t{10} as string,\n\t\t{11} as string,\n\t\t{12} as string,\n\t\t{13} as string,\n\t\t{14} as string,\n\t\t{15} as string,\n\t\t{16} as string,\n\t\t{17} as string,\n\t\t{18} as string,\n\t\t{19} as string,\n\t\t{20} as string,\n\t\t{21} as string,\n\t\t{22} as string,\n\t\t{23} as string,\n\t\t{24} as string,\n\t\t{25} as string,\n\t\t{26} as string,\n\t\t{27} as string,\n\t\t{28} as string,\n\t\t{29} as string,\n\t\t{30} as string,\n\t\t{31} as string,\n\t\t{32} as string,\n\t\t{33} as string,\n\t\t{34} as string,\n\t\t{35} as string,\n\t\t{36} as string,\n\t\t{37} as string,\n\t\t{38} as string,\n\t\t{39} as string,\n\t\t{40} as string,\n\t\t{41} as string,\n\t\t{42} as string,\n\t\t{43} as string,\n\t\t{44} as string,\n\t\t{45} as string,\n\t\t{46} as string,\n\t\t{47} as string,\n\t\t{48} as string,\n\t\t{49} as string,\n\t\t{50} as string,\n\t\t{51} as string,\n\t\t{52} as string,\n\t\t{53} as string,\n\t\t{54} as string,\n\t\t{55} as string,\n\t\t{56} as string,\n\t\t{57} as string,\n\t\t{58} as string,\n\t\t{59} as string,\n\t\t{60} as string,\n\t\t{61} as string,\n\t\t{62} as string,\n\t\t{63} as string,\n\t\t{64} as string,\n\t\t{65} as string,\n\t\t{66} as string,\n\t\t{67} as string,\n\t\t{68} as string,\n\t\t{69} as string,\n\t\t{70} as string,\n\t\t{71} as string,\n\t\t{72} as string,\n\t\t{73} as string,\n\t\t{74} as string,\n\t\t{75} as string,\n\t\t{76} as string,\n\t\t{77} as string,\n\t\t{78} as string,\n\t\t{79} as string,\n\t\t{80} as string,\n\t\t{81} as string,\n\t\t{82} as string,\n\t\t{83} as string,\n\t\t{84} as string,\n\t\t{85} as string,\n\t\t{86} as string,\n\t\t{87} as string,\n\t\t{88} as string,\n\t\t{89} as string,\n\t\t{90} as string,\n\t\t{91} as string,\n\t\t{92} as string,\n\t\t{93} as string,\n\t\t{94} as string,\n\t\t{95} as string,\n\t\t{96} as string,\n\t\t{97} as string,\n\t\t{98} as string,\n\t\t{99} as string,\n\t\t{100} as string,\n\t\t{101} as string,\n\t\t{102} as string,\n\t\t{103} as string,\n\t\t{104} as string,\n\t\t{105} as string,\n\t\t{106} as string,\n\t\t{107} as string,\n\t\t{108} as string,\n\t\t{109} as string,\n\t\t{110} as string,\n\t\t{111} as string,\n\t\t{112} as string,\n\t\t{113} as string,\n\t\t{114} as string,\n\t\t{115} as string,\n\t\t{116} as string,\n\t\t{117} as string,\n\t\t{118} as string,\n\t\t{119} as string,\n\t\t{120} as string,\n\t\t{121} as string,\n\t\t{122} as string,\n\t\t{123} as string,\n\t\t{124} as string,\n\t\t{125} as string,\n\t\t{126} as string,\n\t\t{127} as string,\n\t\t{128} as string,\n\t\t{129} as string,\n\t\t{130} as string,\n\t\t{131} as string,\n\t\t{132} as string,\n\t\t{133} as string,\n\t\t{134} as string,\n\t\t{135} as string,\n\t\t{136} as string,\n\t\t{137} as string,\n\t\t{138} as string,\n\t\t{139} as string,\n\t\t{140} as string,\n\t\t{141} as string,\n\t\t{142} as string,\n\t\t{143} as string,\n\t\t{144} as string,\n\t\t{145} as string,\n\t\t{146} as string,\n\t\t{147} as string,\n\t\t{148} as string,\n\t\t{149} as string,\n\t\t{150} as string,\n\t\t{151} as string,\n\t\t{152} as string,\n\t\t{153} as string,\n\t\t{154} as string,\n\t\t{155} as string,\n\t\t{156} as string,\n\t\t{157} as string,\n\t\t{158} as string,\n\t\t{159} as string,\n\t\t{160} as string,\n\t\t{161} as string,\n\t\t{162} as string,\n\t\t{163} as string,\n\t\t{164} as string,\n\t\t{165} as string,\n\t\t{166} as string,\n\t\t{167} as string,\n\t\t{168} as string,\n\t\t{169} as string,\n\t\t{170} as string,\n\t\t{171} as string,\n\t\t{172} as string,\n\t\t{173} as string,\n\t\t{174} as string,\n\t\t{175} as string,\n\t\t{176} as string,\n\t\t{177} as string,\n\t\t{178} as string,\n\t\t{179} as string,\n\t\t{180} as string,\n\t\t{181} as string,\n\t\t{182} as string,\n\t\t{183} as string,\n\t\t{184} as string,\n\t\t{185} as string,\n\t\t{186} as string,\n\t\t{187} as string,\n\t\t{188} as string,\n\t\t{189} as string,\n\t\t{190} as string,\n\t\t{191} as string,\n\t\t{192} as string,\n\t\t{193} as string,\n\t\t{194} as string,\n\t\t{195} as string,\n\t\t{196} as string,\n\t\t{197} as string,\n\t\t{198} as string,\n\t\t{199} as string,\n\t\t{200} as string,\n\t\t{201} as string,\n\t\t{202} as string,\n\t\t{203} as string,\n\t\t{204} as string,\n\t\t{205} as string,\n\t\t{206} as string,\n\t\t{207} as string,\n\t\t{208} as string,\n\t\t{209} as string,\n\t\t{210} as string,\n\t\t{211} as string,\n\t\t{212} as string,\n\t\t{213} as string,\n\t\t{214} as string,\n\t\t{215} as string,\n\t\t{216} as string,\n\t\t{217} as string,\n\t\t{218} as string,\n\t\t{219} as string,\n\t\t{220} as string,\n\t\t{221} as string,\n\t\t{222} as string,\n\t\t{223} as string,\n\t\t{224} as string,\n\t\t{225} as string,\n\t\t{226} as string,\n\t\t{227} as string,\n\t\t{228} as string,\n\t\t{229} as string,\n\t\t{230} as string,\n\t\t{231} as string,\n\t\t{232} as string,\n\t\t{233} as string,\n\t\t{234} as string,\n\t\t{235} as string,\n\t\t{236} as string,\n\t\t{237} as string,\n\t\t{238} as string,\n\t\t{239} as string,\n\t\t{240} as string,\n\t\t{241} as string,\n\t\t{242} as string,\n\t\t{243} as string,\n\t\t{244} as string,\n\t\t{245} as string,\n\t\t{246} as string,\n\t\t{247} as string,\n\t\t{248} as string,\n\t\t{249} as string,\n\t\t{250} as string,\n\t\t{251} as string,\n\t\t{252} as string,\n\t\t{253} as string,\n\t\t{254} as string,\n\t\t{255} as string,\n\t\t{256} as string,\n\t\t{257} as string,\n\t\t{258} as string,\n\t\t{259} as string,\n\t\t{260} as string,\n\t\t{261} as string,\n\t\t{262} as string,\n\t\t{263} as string,\n\t\t{264} as string,\n\t\t{265} as string,\n\t\t{266} as string,\n\t\t{267} as string,\n\t\t{268} as string,\n\t\t{269} as string,\n\t\t{270} as string,\n\t\t{271} as string,\n\t\t{272} as string,\n\t\t{273} as string,\n\t\t{274} as string,\n\t\t{275} as string,\n\t\t{276} as string,\n\t\t{277} as string,\n\t\t{278} as string\n\t),\n\tallowSchemaDrift: true,\n\tvalidateSchema: false,\n\tignoreNoFilesFound: false,\n\twildcardPaths:[(concat($outputDirectory,$outputFileName))]) ~> FXData\nHeaderFiles, FXData union(byName: true,\n\tpartitionBy('hash', 1))~> UnionDummyHeaderFileAndFxDataFile\nUnionDummyHeaderFileAndFxDataFile select(mapColumn(\n\t\teach(match(true()))\n\t),\n\tskipDuplicateMapInputs: true,\n\tskipDuplicateMapOutputs: true) ~> SelectColumns\nSelectColumns sink(allowSchemaDrift: true,\n\tvalidateSchema: false,\n\tinput(\n\t\t{1} as string,\n\t\t{2} as string,\n\t\t{3} as string,\n\t\t{4} as string,\n\t\t{5} as string,\n\t\t{6} as string,\n\t\t{7} as string,\n\t\t{8} as string,\n\t\t{9} as string,\n\t\t{10} as string,\n\t\t{11} as string,\n\t\t{12} as string,\n\t\t{13} as string,\n\t\t{14} as string,\n\t\t{15} as string,\n\t\t{16} as string,\n\t\t{17} as string,\n\t\t{18} as string,\n\t\t{19} as string,\n\t\t{20} as string,\n\t\t{21} as string,\n\t\t{22} as string,\n\t\t{23} as string,\n\t\t{24} as string,\n\t\t{25} as string,\n\t\t{26} as string,\n\t\t{27} as string,\n\t\t{28} as string,\n\t\t{29} as string,\n\t\t{30} as string,\n\t\t{31} as string,\n\t\t{32} as string,\n\t\t{33} as string,\n\t\t{34} as string,\n\t\t{35} as string,\n\t\t{36} as string,\n\t\t{37} as string,\n\t\t{38} as string,\n\t\t{39} as string,\n\t\t{40} as string,\n\t\t{41} as string,\n\t\t{42} as string,\n\t\t{43} as string,\n\t\t{44} as string,\n\t\t{45} as string,\n\t\t{46} as string,\n\t\t{47} as string,\n\t\t{48} as string,\n\t\t{49} as string,\n\t\t{50} as string,\n\t\t{51} as string,\n\t\t{52} as string,\n\t\t{53} as string,\n\t\t{54} as string,\n\t\t{55} as string,\n\t\t{56} as string,\n\t\t{57} as string,\n\t\t{58} as string,\n\t\t{59} as string,\n\t\t{60} as string,\n\t\t{61} as string,\n\t\t{62} as string,\n\t\t{63} as string,\n\t\t{64} as string,\n\t\t{65} as string,\n\t\t{66} as string,\n\t\t{67} as string,\n\t\t{68} as string,\n\t\t{69} as string,\n\t\t{70} as string,\n\t\t{71} as string,\n\t\t{72} as string,\n\t\t{73} as string,\n\t\t{74} as string,\n\t\t{75} as string,\n\t\t{76} as string,\n\t\t{77} as string,\n\t\t{78} as string,\n\t\t{79} as string,\n\t\t{80} as string,\n\t\t{81} as string,\n\t\t{82} as string,\n\t\t{83} as string,\n\t\t{84} as string,\n\t\t{85} as string,\n\t\t{86} as string,\n\t\t{87} as string,\n\t\t{88} as string,\n\t\t{89} as string,\n\t\t{90} as string,\n\t\t{91} as string,\n\t\t{92} as string,\n\t\t{93} as string,\n\t\t{94} as string,\n\t\t{95} as string,\n\t\t{96} as string,\n\t\t{97} as string,\n\t\t{98} as string,\n\t\t{99} as string,\n\t\t{100} as string,\n\t\t{101} as string,\n\t\t{102} as string,\n\t\t{103} as string,\n\t\t{104} as string,\n\t\t{105} as string,\n\t\t{106} as string,\n\t\t{107} as string,\n\t\t{108} as string,\n\t\t{109} as string,\n\t\t{110} as string,\n\t\t{111} as string,\n\t\t{112} as string,\n\t\t{113} as string,\n\t\t{114} as string,\n\t\t{115} as string,\n\t\t{116} as string,\n\t\t{117} as string,\n\t\t{118} as string,\n\t\t{119} as string,\n\t\t{120} as string,\n\t\t{121} as string,\n\t\t{122} as string,\n\t\t{123} as string,\n\t\t{124} as string,\n\t\t{125} as string,\n\t\t{126} as string,\n\t\t{127} as string,\n\t\t{128} as string,\n\t\t{129} as string,\n\t\t{130} as string,\n\t\t{131} as string,\n\t\t{132} as string,\n\t\t{133} as string,\n\t\t{134} as string,\n\t\t{135} as string,\n\t\t{136} as string,\n\t\t{137} as string,\n\t\t{138} as string,\n\t\t{139} as string,\n\t\t{140} as string,\n\t\t{141} as string,\n\t\t{142} as string,\n\t\t{143} as string,\n\t\t{144} as string,\n\t\t{145} as string,\n\t\t{146} as string,\n\t\t{147} as string,\n\t\t{148} as string,\n\t\t{149} as string,\n\t\t{150} as string,\n\t\t{151} as string,\n\t\t{152} as string,\n\t\t{153} as string,\n\t\t{154} as string,\n\t\t{155} as string,\n\t\t{156} as string,\n\t\t{157} as string,\n\t\t{158} as string,\n\t\t{159} as string,\n\t\t{160} as string,\n\t\t{161} as string,\n\t\t{162} as string,\n\t\t{163} as string,\n\t\t{164} as string,\n\t\t{165} as string,\n\t\t{166} as string,\n\t\t{167} as string,\n\t\t{168} as string,\n\t\t{169} as string,\n\t\t{170} as string,\n\t\t{171} as string,\n\t\t{172} as string,\n\t\t{173} as string,\n\t\t{174} as string,\n\t\t{175} as string,\n\t\t{176} as string,\n\t\t{177} as string,\n\t\t{178} as string,\n\t\t{179} as string,\n\t\t{180} as string,\n\t\t{181} as string,\n\t\t{182} as string,\n\t\t{183} as string,\n\t\t{184} as string,\n\t\t{185} as string,\n\t\t{186} as string,\n\t\t{187} as string,\n\t\t{188} as string,\n\t\t{189} as string,\n\t\t{190} as string,\n\t\t{191} as string,\n\t\t{192} as string,\n\t\t{193} as string,\n\t\t{194} as string,\n\t\t{195} as string,\n\t\t{196} as string,\n\t\t{197} as string,\n\t\t{198} as string,\n\t\t{199} as string,\n\t\t{200} as string,\n\t\t{201} as string,\n\t\t{202} as string,\n\t\t{203} as string,\n\t\t{204} as string,\n\t\t{205} as string,\n\t\t{206} as string,\n\t\t{207} as string,\n\t\t{208} as string,\n\t\t{209} as string,\n\t\t{210} as string,\n\t\t{211} as string,\n\t\t{212} as string,\n\t\t{213} as string,\n\t\t{214} as string,\n\t\t{215} as string,\n\t\t{216} as string,\n\t\t{217} as string,\n\t\t{218} as string,\n\t\t{219} as string,\n\t\t{220} as string,\n\t\t{221} as string,\n\t\t{222} as string,\n\t\t{223} as string,\n\t\t{224} as string,\n\t\t{225} as string,\n\t\t{226} as string,\n\t\t{227} as string,\n\t\t{228} as string,\n\t\t{229} as string,\n\t\t{230} as string,\n\t\t{231} as string,\n\t\t{232} as string,\n\t\t{233} as string,\n\t\t{234} as string,\n\t\t{235} as string,\n\t\t{236} as string,\n\t\t{237} as string,\n\t\t{238} as string,\n\t\t{239} as string,\n\t\t{240} as string,\n\t\t{241} as string,\n\t\t{242} as string,\n\t\t{243} as string,\n\t\t{244} as string,\n\t\t{245} as string,\n\t\t{246} as string,\n\t\t{247} as string,\n\t\t{248} as string,\n\t\t{249} as string,\n\t\t{250} as string,\n\t\t{251} as string,\n\t\t{252} as string,\n\t\t{253} as string,\n\t\t{254} as string,\n\t\t{255} as string,\n\t\t{256} as string,\n\t\t{257} as string,\n\t\t{258} as string,\n\t\t{259} as string,\n\t\t{260} as string,\n\t\t{261} as string,\n\t\t{262} as string,\n\t\t{263} as string,\n\t\t{264} as string,\n\t\t{265} as string,\n\t\t{266} as string,\n\t\t{267} as string,\n\t\t{268} as string,\n\t\t{269} as string,\n\t\t{270} as string,\n\t\t{271} as string,\n\t\t{272} as string,\n\t\t{273} as string,\n\t\t{274} as string,\n\t\t{275} as string,\n\t\t{276} as string,\n\t\t{277} as string,\n\t\t{278} as string\n\t),\n\tpartitionFileNames:[(concat($outputDirectory, $outputFileName))],\n\tumask: 0022,\n\tpreCommands: [],\n\tpostCommands: [],\n\tskipDuplicateMapInputs: true,\n\tskipDuplicateMapOutputs: true,\n\tpartitionBy('hash', 1)) ~> OutputToCSV"}}}