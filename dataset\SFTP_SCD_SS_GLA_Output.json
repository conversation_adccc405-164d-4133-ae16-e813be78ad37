{"name": "SFTP_SCD_SS_GLA_Output", "properties": {"linkedServiceName": {"referenceName": "SCD_SFTP", "type": "LinkedServiceReference"}, "parameters": {"FilePath": {"type": "string"}}, "folder": {"name": "Inbound/Billing and QFM"}, "annotations": [], "type": "DelimitedText", "typeProperties": {"location": {"type": "SftpLocation", "folderPath": {"value": "@dataset().FilePath", "type": "Expression"}}, "columnDelimiter": ",", "escapeChar": "\\", "firstRowAsHeader": true, "quoteChar": ""}, "schema": []}, "type": "Microsoft.DataFactory/factories/datasets"}