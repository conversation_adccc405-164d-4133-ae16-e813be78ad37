{"name": "SQLMI_SFDB_PROD", "properties": {"linkedServiceName": {"referenceName": "AzureSQLManagedInstance_Prod", "type": "LinkedServiceReference", "parameters": {"dbName": "SalesforceData"}}, "folder": {"name": "Inbound/Salesforce/IN-193 DW Inbound Interface/IN-196 SF To IMW"}, "annotations": [], "type": "AzureSqlMITable", "schema": [], "typeProperties": {"schema": "dbo", "table": "Account"}}, "type": "Microsoft.DataFactory/factories/datasets"}