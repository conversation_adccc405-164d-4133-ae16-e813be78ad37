{"name": "SFTP_SCD_Parameterized_Directory_CSV", "properties": {"linkedServiceName": {"referenceName": "SCD_SFTP", "type": "LinkedServiceReference"}, "parameters": {"FilePath": {"type": "string"}, "FileName": {"type": "string"}}, "folder": {"name": "SCD"}, "annotations": [], "type": "DelimitedText", "typeProperties": {"location": {"type": "SftpLocation", "fileName": {"value": "@dataset().FileName", "type": "Expression"}, "folderPath": {"value": "@dataset().FilePath", "type": "Expression"}}, "columnDelimiter": ",", "escapeChar": "\\", "firstRowAsHeader": true, "quoteChar": ""}, "schema": []}, "type": "Microsoft.DataFactory/factories/datasets"}