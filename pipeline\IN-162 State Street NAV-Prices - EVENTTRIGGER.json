{"name": "IN-162 State Street NAV-Prices - EVENTTRIGGER", "properties": {"description": "1) Set Archive_IN_Folder variable and invoke IN-162 State Street NAV-Prices - PARENT", "activities": [{"name": "Set Date variable", "type": "SetVariable", "dependsOn": [], "policy": {"secureOutput": false, "secureInput": false}, "userProperties": [], "typeProperties": {"variableName": "Date", "value": {"value": "@concat(formatDateTime(convertFromUtc(utcnow(), 'Mountain Standard Time'),'yyyyMMdd'))", "type": "Expression"}}}, {"name": "If Sending File to SCD SFTP", "type": "IfCondition", "dependsOn": [{"activity": "If Condition1", "dependencyConditions": ["Succeeded"]}], "userProperties": [], "typeProperties": {"expression": {"value": "@and(pipeline().globalParameters.SendToSCD_NAV, and(variables('ProcessPipeline'), not(variables('OutPutFileNameExists'))))", "type": "Expression"}, "ifTrueActivities": [{"name": "Output result to SCD SFTP", "type": "Copy", "dependsOn": [], "policy": {"timeout": "7.00:00:00", "retry": 0, "retryIntervalInSeconds": 30, "secureOutput": false, "secureInput": false}, "userProperties": [], "typeProperties": {"source": {"type": "BinarySource", "storeSettings": {"type": "AzureBlobFSReadSettings", "recursive": true, "modifiedDatetimeStart": "2022-08-18T00:00:00.752Z", "wildcardFolderPath": "NAV/State_Street_Nav_Price/OUT", "wildcardFileName": "*.xml", "deleteFilesAfterCompletion": false}, "formatSettings": {"type": "BinaryReadSettings"}}, "sink": {"type": "BinarySink", "storeSettings": {"type": "SftpWriteSettings", "operationTimeout": "01:00:00", "useTempFileRename": true}}, "enableStaging": false}, "inputs": [{"referenceName": "ADL_Binary_Dir", "type": "DatasetReference", "parameters": {"Path": "NAV/State_Street_Nav_Price/OUT"}}], "outputs": [{"referenceName": "SCD_SFTP_Binary_Dir", "type": "DatasetReference", "parameters": {"Path": {"value": "@concat(pipeline().globalParameters.SCD_ENV,\r\npipeline().globalParameters.SCD_SFTP_NAV)", "type": "Expression"}}}]}, {"name": "Convert to CSV_XML", "type": "Copy", "dependsOn": [{"activity": "CSV To XML Orchestration_copy1", "dependencyConditions": ["Succeeded"]}], "policy": {"timeout": "0.12:00:00", "retry": 0, "retryIntervalInSeconds": 30, "secureOutput": false, "secureInput": false}, "userProperties": [], "typeProperties": {"source": {"type": "DelimitedTextSource", "storeSettings": {"type": "AzureBlobFSReadSettings", "recursive": true, "wildcardFolderPath": "NAV/State_Street_Nav_Price/OUT", "wildcardFileName": "*.xml", "enablePartitionDiscovery": false}, "formatSettings": {"type": "DelimitedTextReadSettings"}}, "sink": {"type": "DelimitedTextSink", "storeSettings": {"type": "AzureBlobFSWriteSettings"}, "formatSettings": {"type": "DelimitedTextWriteSettings", "quoteAllText": true, "fileExtension": ".txt"}}, "enableStaging": false, "translator": {"type": "TabularTranslator", "typeConversion": true, "typeConversionSettings": {"allowDataTruncation": true, "treatBooleanAsNumber": false}}}, "inputs": [{"referenceName": "ADL_Delimited_File", "type": "DatasetReference", "parameters": {"FilePath": "NAV/State_Street_Nav_Price/OUT", "FileName": {"value": "@variables('OutPutFileNameSummary')", "type": "Expression"}, "ColDelimiter": ",", "FirstRowHeader": true}}], "outputs": [{"referenceName": "ADL_Delimited_File", "type": "DatasetReference", "parameters": {"FilePath": "NAV/State_Street_Nav_Price/OUT", "FileName": {"value": "@concat(replace(variables('OutPutFileNameSummary'),'.csv','.xml'),'.csv')", "type": "Expression"}, "ColDelimiter": ",", "FirstRowHeader": true}}]}, {"name": "Output result to FundServ Working", "type": "Copy", "dependsOn": [{"activity": "Convert to CSV_XML", "dependencyConditions": ["Succeeded"]}], "policy": {"timeout": "7.00:00:00", "retry": 0, "retryIntervalInSeconds": 30, "secureOutput": false, "secureInput": false}, "userProperties": [], "typeProperties": {"source": {"type": "BinarySource", "storeSettings": {"type": "AzureBlobFSReadSettings", "recursive": true, "modifiedDatetimeStart": "2022-08-18T00:00:00.752Z", "wildcardFolderPath": "NAV/State_Street_Nav_Price/OUT", "wildcardFileName": "*.xml.csv", "deleteFilesAfterCompletion": false}, "formatSettings": {"type": "BinaryReadSettings"}}, "sink": {"type": "BinarySink", "storeSettings": {"type": "SftpWriteSettings", "operationTimeout": "01:00:00", "useTempFileRename": true}}, "enableStaging": false}, "inputs": [{"referenceName": "ADL_Binary_Dir", "type": "DatasetReference", "parameters": {"Path": "NAV/State_Street_Nav_Price/OUT"}}], "outputs": [{"referenceName": "SCD_SFTP_Binary_Dir", "type": "DatasetReference", "parameters": {"Path": {"value": "@concat(pipeline().globalParameters.SCD_ENV,\npipeline().globalParameters.SCD_SFTP_FUNDSERV)", "type": "Expression"}}}]}, {"name": "OutPutFileName_summary", "type": "SetVariable", "dependsOn": [{"activity": "Output result to SCD SFTP", "dependencyConditions": ["Succeeded"]}], "policy": {"secureOutput": false, "secureInput": false}, "userProperties": [], "typeProperties": {"variableName": "OutPutFileNameSummary", "value": {"value": "@concat('NAV_StateStreet_Summary_',variables('Date'),'_',formatDateTime(convertFromUtc(utcnow(),'Mountain Standard Time'),'hhmmss'),'.csv')", "type": "Expression"}}}, {"name": "CSV To XML Orchestration_copy1", "type": "WebActivity", "dependsOn": [{"activity": "Copy NAV data_summary", "dependencyConditions": ["Succeeded"]}], "policy": {"timeout": "7.00:00:00", "retry": 0, "retryIntervalInSeconds": 30, "secureOutput": false, "secureInput": false}, "userProperties": [], "typeProperties": {"method": "POST", "url": {"value": "@pipeline().globalParameters.CsvToXmlOrchestration", "type": "Expression"}, "body": {"value": "@concat('{\"InputFileName\":\"',variables('OutPutFileNameSummary'),'\",\"DirectoryFullName\":\"',concat('NAV/State_Street_Nav_Price/OUT'),'\",\"Delimiter\":\",\",\"RowTagName\":\"Record\",\"RowParentTagName\":\"Data\",\"RowParentCountAttributeName\":\"RecordCount\",\"RootElement\":\"\",\"AdditionalTags\":[]}')", "type": "Expression"}}}, {"name": "Copy NAV data_summary", "type": "Copy", "dependsOn": [{"activity": "OutPutFileName_summary", "dependencyConditions": ["Succeeded"]}], "policy": {"timeout": "0.12:00:00", "retry": 0, "retryIntervalInSeconds": 30, "secureOutput": false, "secureInput": false}, "userProperties": [], "typeProperties": {"source": {"type": "SqlMISource", "sqlReaderStoredProcedureName": "[StateStreet].[sp_ExtractStateStreetDailyNav]", "storedProcedureParameters": {"ReportDate": {"type": "DateTime", "value": {"value": "@concat(substring(variables('Date'), 0, 4), '-',\nsubstring(variables('Date'), 4, 2), '-',\nsubstring(variables('Date'), 6, 2))", "type": "Expression"}}, "SummarizeNAV": {"type": "Boolean", "value": {"value": "@bool(1)", "type": "Expression"}}}, "partitionOption": "None"}, "sink": {"type": "DelimitedTextSink", "storeSettings": {"type": "AzureBlobFSWriteSettings"}, "formatSettings": {"type": "DelimitedTextWriteSettings", "quoteAllText": true, "fileExtension": ".txt"}}, "enableStaging": false, "translator": {"type": "TabularTranslator", "typeConversion": true, "typeConversionSettings": {"allowDataTruncation": true, "treatBooleanAsNumber": false}}}, "inputs": [{"referenceName": "SQLMI_RESEARCH_DEV_PARAM", "type": "DatasetReference", "parameters": {"DBName": "ExternalData", "SchemaName": "StateStreet", "TableName": "STATESTREET_DAILY_NAV_SFTP"}}], "outputs": [{"referenceName": "ADL_CSV_File_Param", "type": "DatasetReference", "parameters": {"FilePath": "NAV/State_Street_Nav_Price/OUT", "FileName": {"value": "@variables('OutPutFileNameSummary')", "type": "Expression"}}}]}]}}, {"name": "Archive IN", "type": "Copy", "dependsOn": [{"activity": "If Sending File to SCD SFTP", "dependencyConditions": ["Succeeded"]}, {"activity": "If Sending SS_PRICING File to SCD SFTP", "dependencyConditions": ["Succeeded"]}], "policy": {"timeout": "7.00:00:00", "retry": 0, "retryIntervalInSeconds": 30, "secureOutput": false, "secureInput": false}, "userProperties": [], "typeProperties": {"source": {"type": "BinarySource", "storeSettings": {"type": "AzureBlobFSReadSettings", "recursive": true, "deleteFilesAfterCompletion": true}, "formatSettings": {"type": "BinaryReadSettings"}}, "sink": {"type": "BinarySink", "storeSettings": {"type": "AzureBlobFSWriteSettings"}}, "enableStaging": false}, "inputs": [{"referenceName": "ADL_Binary_Dir", "type": "DatasetReference", "parameters": {"Path": "NAV/State_Street_Nav_Price/IN"}}], "outputs": [{"referenceName": "ADL_Binary_Dir", "type": "DatasetReference", "parameters": {"Path": {"value": "@concat('NAV/State_Street_Nav_Price/ARCHIVE/',variables('Date'),'/IN')", "type": "Expression"}}}]}, {"name": "Archive OUT", "type": "Copy", "dependsOn": [{"activity": "If Sending File to SCD SFTP", "dependencyConditions": ["Succeeded"]}], "policy": {"timeout": "7.00:00:00", "retry": 0, "retryIntervalInSeconds": 30, "secureOutput": false, "secureInput": false}, "userProperties": [], "typeProperties": {"source": {"type": "BinarySource", "storeSettings": {"type": "AzureBlobFSReadSettings", "recursive": false, "deleteFilesAfterCompletion": true}, "formatSettings": {"type": "BinaryReadSettings"}}, "sink": {"type": "BinarySink", "storeSettings": {"type": "AzureBlobFSWriteSettings"}}, "enableStaging": false}, "inputs": [{"referenceName": "ADL_Binary_Dir", "type": "DatasetReference", "parameters": {"Path": "NAV/State_Street_Nav_Price/OUT"}}], "outputs": [{"referenceName": "ADL_Binary_Dir", "type": "DatasetReference", "parameters": {"Path": {"value": "@concat('NAV/State_Street_Nav_Price/ARCHIVE/',variables('Date'),'/OUT')", "type": "Expression"}}}]}, {"name": "Archive TEMP", "type": "Copy", "dependsOn": [{"activity": "If Sending File to SCD SFTP", "dependencyConditions": ["Succeeded"]}], "policy": {"timeout": "7.00:00:00", "retry": 0, "retryIntervalInSeconds": 30, "secureOutput": false, "secureInput": false}, "userProperties": [], "typeProperties": {"source": {"type": "BinarySource", "storeSettings": {"type": "AzureBlobFSReadSettings", "recursive": true, "deleteFilesAfterCompletion": true}, "formatSettings": {"type": "BinaryReadSettings"}}, "sink": {"type": "BinarySink", "storeSettings": {"type": "AzureBlobFSWriteSettings"}}, "enableStaging": false}, "inputs": [{"referenceName": "ADL_Binary_Dir", "type": "DatasetReference", "parameters": {"Path": "NAV/State_Street_Nav_Price/TEMP"}}], "outputs": [{"referenceName": "ADL_Binary_Dir", "type": "DatasetReference", "parameters": {"Path": {"value": "@concat('NAV/State_Street_Nav_Price/ARCHIVE/',variables('Date'),'/TEMP')", "type": "Expression"}}}]}, {"name": "OutPutFileName", "type": "SetVariable", "dependsOn": [{"activity": "Set Date variable", "dependencyConditions": ["Succeeded"]}], "policy": {"secureOutput": false, "secureInput": false}, "userProperties": [], "typeProperties": {"variableName": "OutPutFileName", "value": {"value": "@concat('NAV_StateStreet_',variables('Date'),'_',formatDateTime(convertFromUtc(utcnow(),'Mountain Standard Time'),'hhmmss'),'.csv')", "type": "Expression"}}}, {"name": "If Condition1", "type": "IfCondition", "dependsOn": [{"activity": "Set ProcessFlag", "dependencyConditions": ["Succeeded"]}, {"activity": "Set OutputFileExists", "dependencyConditions": ["Succeeded"]}], "userProperties": [], "typeProperties": {"expression": {"value": "@and(variables('ProcessPipeline'), not(variables('OutPutFileNameExists')))", "type": "Expression"}, "ifTrueActivities": [{"name": "CSV To XML Orchestration", "type": "WebActivity", "dependsOn": [{"activity": "Copy NAV data", "dependencyConditions": ["Succeeded"]}], "policy": {"timeout": "7.00:00:00", "retry": 0, "retryIntervalInSeconds": 30, "secureOutput": false, "secureInput": false}, "userProperties": [], "typeProperties": {"method": "POST", "url": {"value": "@pipeline().globalParameters.CsvToXmlOrchestration", "type": "Expression"}, "body": {"value": "@concat('{\"InputFileName\":\"',variables('OutPutFileName'),'\",\"DirectoryFullName\":\"',concat('NAV/State_Street_Nav_Price/OUT'),'\",\"Delimiter\":\",\",\"RowTagName\":\"Record\",\"RowParentTagName\":\"Data\",\"RowParentCountAttributeName\":\"RecordCount\",\"RootElement\":\"\",\"AdditionalTags\":[]}')", "type": "Expression"}}}, {"name": "Copy NAV data", "type": "Copy", "dependsOn": [], "policy": {"timeout": "0.12:00:00", "retry": 0, "retryIntervalInSeconds": 30, "secureOutput": false, "secureInput": false}, "userProperties": [], "typeProperties": {"source": {"type": "SqlMISource", "sqlReaderStoredProcedureName": "[StateStreet].[sp_ExtractStateStreetDailyNav]", "storedProcedureParameters": {"ReportDate": {"type": "DateTime", "value": {"value": "@concat(substring(variables('Date'), 0, 4), '-',\nsubstring(variables('Date'), 4, 2), '-',\nsubstring(variables('Date'), 6, 2))", "type": "Expression"}}, "SummarizeNAV": {"type": "Boolean", "value": {"value": "@bool(0)", "type": "Expression"}}}, "partitionOption": "None"}, "sink": {"type": "DelimitedTextSink", "storeSettings": {"type": "AzureBlobFSWriteSettings"}, "formatSettings": {"type": "DelimitedTextWriteSettings", "quoteAllText": true, "fileExtension": ".txt"}}, "enableStaging": false, "translator": {"type": "TabularTranslator", "typeConversion": true, "typeConversionSettings": {"allowDataTruncation": true, "treatBooleanAsNumber": false}}}, "inputs": [{"referenceName": "SQLMI_RESEARCH_DEV_PARAM", "type": "DatasetReference", "parameters": {"DBName": "ExternalData", "SchemaName": "StateStreet", "TableName": "STATESTREET_DAILY_NAV_SFTP"}}], "outputs": [{"referenceName": "ADL_CSV_File_Param", "type": "DatasetReference", "parameters": {"FilePath": "NAV/State_Street_Nav_Price/OUT", "FileName": {"value": "@variables('OutPutFileName')", "type": "Expression"}}}]}, {"name": "Copy CSV to SCD SFTP", "type": "Copy", "dependsOn": [{"activity": "Copy NAV data", "dependencyConditions": ["Succeeded"]}], "policy": {"timeout": "0.12:00:00", "retry": 0, "retryIntervalInSeconds": 30, "secureOutput": false, "secureInput": false}, "userProperties": [], "typeProperties": {"source": {"type": "BinarySource", "storeSettings": {"type": "AzureBlobFSReadSettings", "recursive": true, "deleteFilesAfterCompletion": false}, "formatSettings": {"type": "BinaryReadSettings"}}, "sink": {"type": "BinarySink", "storeSettings": {"type": "SftpWriteSettings", "operationTimeout": "01:00:00", "useTempFileRename": true}}, "enableStaging": false}, "inputs": [{"referenceName": "ADL_Binary_File", "type": "DatasetReference", "parameters": {"FileName": {"value": "@variables('OutPutFileName')", "type": "Expression"}, "Path": "NAV/State_Street_Nav_Price/OUT", "Container": "scdintegration"}}], "outputs": [{"referenceName": "SCD_SFTP_Binary_Dir", "type": "DatasetReference", "parameters": {"Path": {"value": "@concat(pipeline().globalParameters.SCD_ENV,'/ActiveData/Interfaces/In/SFTP/Compliance/Working')", "type": "Expression"}}}]}]}}, {"name": "Get Pipeline Porcess Flag", "type": "Lookup", "dependsOn": [{"activity": "OutPutFileName", "dependencyConditions": ["Succeeded"]}], "policy": {"timeout": "0.12:00:00", "retry": 0, "retryIntervalInSeconds": 30, "secureOutput": false, "secureInput": false}, "userProperties": [], "typeProperties": {"source": {"type": "SqlMISource", "sqlReaderStoredProcedureName": "[StateStreet].[sp_GetStateStreetProcessFlag]", "storedProcedureParameters": {"ReportDate": {"type": "DateTime", "value": {"value": "@concat(substring(variables('Date'), 0, 4), '-',\nsubstring(variables('Date'), 4, 2), '-',\nsubstring(variables('Date'), 6, 2))", "type": "Expression"}}}, "partitionOption": "None"}, "dataset": {"referenceName": "SQLMI_RESEARCH_DEV_PARAM", "type": "DatasetReference", "parameters": {"DBName": "ExternalData", "SchemaName": "StateStreet", "TableName": "STATESTREET_DAILY_NAV_SFTP"}}}}, {"name": "Set ProcessFlag", "type": "SetVariable", "dependsOn": [{"activity": "Get Pipeline Porcess Flag", "dependencyConditions": ["Succeeded"]}], "policy": {"secureOutput": false, "secureInput": false}, "userProperties": [], "typeProperties": {"variableName": "ProcessPipeline", "value": {"value": "@activity('Get Pipeline Porcess Flag').output.firstRow.ProcessPipeLineInd", "type": "Expression"}}}, {"name": "Check if output file exists", "description": "Check if output file exists in archive", "type": "GetMetadata", "dependsOn": [{"activity": "Create Dummy File", "dependencyConditions": ["Succeeded"]}], "policy": {"timeout": "0.12:00:00", "retry": 0, "retryIntervalInSeconds": 30, "secureOutput": false, "secureInput": false}, "userProperties": [], "typeProperties": {"dataset": {"referenceName": "ADL_CSV_File_Param", "type": "DatasetReference", "parameters": {"FilePath": {"value": "@concat('NAV/State_Street_Nav_Price/ARCHIVE/',variables('Date'),'/OUT')", "type": "Expression"}, "FileName": {"value": "@concat('NAV_StateStreet_',variables('Date'),'_','*.xml')", "type": "Expression"}}}, "fieldList": ["childItems"], "storeSettings": {"type": "AzureBlobFSReadSettings", "recursive": true, "enablePartitionDiscovery": false}, "formatSettings": {"type": "DelimitedTextReadSettings"}}}, {"name": "Set OutputFileExists", "type": "SetVariable", "dependsOn": [{"activity": "Filter XML File", "dependencyConditions": ["Succeeded"]}], "policy": {"secureOutput": false, "secureInput": false}, "userProperties": [], "typeProperties": {"variableName": "OutPutFileNameExists", "value": {"value": "@if(equals(activity('Filter XML File').output.FilteredItemsCount, 0), bool(0), bool(1))", "type": "Expression"}}}, {"name": "Create Dummy File", "type": "Copy", "dependsOn": [{"activity": "OutPutFileName", "dependencyConditions": ["Succeeded"]}], "policy": {"timeout": "0.12:00:00", "retry": 0, "retryIntervalInSeconds": 30, "secureOutput": false, "secureInput": false}, "userProperties": [], "typeProperties": {"source": {"type": "SqlMISource", "sqlReaderStoredProcedureName": "[StateStreet].[sp_GetStateStreetProcessFlag]", "storedProcedureParameters": {"ReportDate": {"type": "DateTime", "value": {"value": "@concat(substring(variables('Date'), 0, 4), '-',\nsubstring(variables('Date'), 4, 2), '-',\nsubstring(variables('Date'), 6, 2))", "type": "Expression"}}}, "partitionOption": "None"}, "sink": {"type": "DelimitedTextSink", "storeSettings": {"type": "AzureBlobFSWriteSettings"}, "formatSettings": {"type": "DelimitedTextWriteSettings", "quoteAllText": true, "fileExtension": ".txt"}}, "enableStaging": false, "translator": {"type": "TabularTranslator", "typeConversion": true, "typeConversionSettings": {"allowDataTruncation": true, "treatBooleanAsNumber": false}}}, "inputs": [{"referenceName": "SQLMI_RESEARCH_DEV_PARAM", "type": "DatasetReference", "parameters": {"DBName": "ExternalData", "SchemaName": "StateStreet", "TableName": "STATESTREET_DAILY_NAV_SFTP"}}], "outputs": [{"referenceName": "ADL_CSV_File_Param", "type": "DatasetReference", "parameters": {"FilePath": {"value": "@concat('NAV/State_Street_Nav_Price/ARCHIVE/',variables('Date'),'/OUT')", "type": "Expression"}, "FileName": "Process.txt"}}]}, {"name": "Filter XML File", "type": "Filter", "dependsOn": [{"activity": "Check if output file exists", "dependencyConditions": ["Succeeded"]}], "userProperties": [], "typeProperties": {"items": {"value": "@activity('Check if output file exists').output.childItems", "type": "Expression"}, "condition": {"value": "@and(contains(item().name,concat('NAV_StateStreet_',variables('Date'))), endswith(item().name, '.xml'))", "type": "Expression"}}}, {"name": "If Sending SS_PRICING File to SCD SFTP", "type": "IfCondition", "dependsOn": [{"activity": "Set Date variable", "dependencyConditions": ["Succeeded"]}], "userProperties": [], "typeProperties": {"expression": {"value": "@pipeline().globalParameters.SendToSCD_NAV", "type": "Expression"}, "ifTrueActivities": [{"name": "Copy SS_MFPrice File", "type": "Copy", "dependsOn": [], "policy": {"timeout": "7.00:00:00", "retry": 0, "retryIntervalInSeconds": 30, "secureOutput": false, "secureInput": false}, "userProperties": [], "typeProperties": {"source": {"type": "BinarySource", "storeSettings": {"type": "AzureFileStorageReadSettings", "recursive": true, "wildcardFileName": {"value": "@concat('SS_MFPrice_SFTP_',variables('Date'),'*.*')", "type": "Expression"}, "deleteFilesAfterCompletion": false}, "formatSettings": {"type": "BinaryReadSettings"}}, "sink": {"type": "BinarySink", "storeSettings": {"type": "AzureBlobFSWriteSettings"}}, "enableStaging": false}, "inputs": [{"referenceName": "SFTP_Mawer_ROOT_BINARY", "type": "DatasetReference"}], "outputs": [{"referenceName": "ADL_Binary_Dir", "type": "DatasetReference", "parameters": {"Path": "NAV/State_Street_Nav_Price/IN"}}]}, {"name": "Copy SS_MFPrice File to SCD SFTP", "type": "Copy", "dependsOn": [{"activity": "Copy SS_MFPrice File", "dependencyConditions": ["Succeeded"]}], "policy": {"timeout": "7.00:00:00", "retry": 0, "retryIntervalInSeconds": 30, "secureOutput": false, "secureInput": false}, "userProperties": [], "typeProperties": {"source": {"type": "BinarySource", "storeSettings": {"type": "AzureBlobFSReadSettings", "recursive": true, "wildcardFolderPath": "NAV/State_Street_Nav_Price/IN", "wildcardFileName": {"value": "@concat('SS_MFPrice_SFTP_',variables('Date'),'*.*')", "type": "Expression"}, "deleteFilesAfterCompletion": false}, "formatSettings": {"type": "BinaryReadSettings"}}, "sink": {"type": "BinarySink", "storeSettings": {"type": "SftpWriteSettings", "operationTimeout": "01:00:00", "useTempFileRename": true}}, "enableStaging": false}, "inputs": [{"referenceName": "ADL_Binary_File", "type": "DatasetReference", "parameters": {"FileName": {"value": "@concat('SS_MFPrice_SFTP_',variables('Date'),'*.*')", "type": "Expression"}, "Path": "NAV/State_Street_Nav_Price/IN", "Container": "scdintegration"}}], "outputs": [{"referenceName": "SCD_SFTP_Binary_File", "type": "DatasetReference", "parameters": {"Path": {"value": "@concat(pipeline().globalParameters.SCD_ENV,\npipeline().globalParameters.SCD_SFTP_NAV_MFPrice)", "type": "Expression"}, "FileName": {"value": "@concat(variables('Date'),'_SS_MFPrice.csv')\n", "type": "Expression"}}}]}]}}], "variables": {"LastBizDay": {"type": "String"}, "Date": {"type": "String"}, "OutPutFileName": {"type": "String"}, "OutPutFileNameSummary": {"type": "String"}, "ProcessPipeline": {"type": "Boolean", "defaultValue": false}, "OutPutFileNameExists": {"type": "Boolean"}}, "folder": {"name": "State Street/SS NAV Prices"}, "annotations": [], "lastPublishTime": "2024-07-31T20:05:28Z"}, "type": "Microsoft.DataFactory/factories/pipelines"}