{"name": "IN85_IFDS_ZPACSWP5_DF", "properties": {"description": "Report5 file data flow", "folder": {"name": "Inbound"}, "type": "MappingDataFlow", "typeProperties": {"sources": [{"dataset": {"referenceName": "AzureDataLakeStorage_IFDS_RawFiles_IN_04", "type": "DatasetReference"}, "name": "ProductionReportREPORT5File"}], "sinks": [{"dataset": {"referenceName": "ADL_IFDS_ProductionReport_OUT", "type": "DatasetReference"}, "name": "ZPACCSWP5FileSink"}], "transformations": [], "script": "parameters{\n\tADLFileDirectory as string\n}\nsource(allowSchemaDrift: true,\n\tvalidateSchema: false,\n\tignoreNoFilesFound: false,\n\tskipLines: 1,\n\twildcardPaths:[(concat($ADLFileDirectory,'/','ZPACSWP5.D'))]) ~> ProductionReportREPORT5File\nProductionReportREPORT5File sink(allowSchemaDrift: true,\n\tvalidateSchema: false,\n\temptyLinesAsHeader: 1,\n\tpartitionFileNames:['ZPACSWP5.txt'],\n\tumask: 0022,\n\tpreCommands: [],\n\tpostCommands: [],\n\tskipDuplicateMapInputs: true,\n\tskipDuplicateMapOutputs: true,\n\tpartitionBy('hash', 1)) ~> ZPACCSWP5FileSink"}}}