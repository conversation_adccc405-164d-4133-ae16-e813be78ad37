{"name": "DECOM- IN-Mawer Fund Benchmark SSRS reports", "properties": {"activities": [{"name": "Generate Net File", "type": "Copy", "dependsOn": [], "policy": {"timeout": "7.00:00:00", "retry": 0, "retryIntervalInSeconds": 30, "secureOutput": false, "secureInput": false}, "userProperties": [], "typeProperties": {"source": {"type": "SqlServerSource", "sqlReaderStoredProcedureName": "[OnDemandReporting].[usp_rptGetNetPerformance]", "storedProcedureParameters": {"Reportdate": {"type": "Datetime", "value": "2021-01-01"}}, "queryTimeout": "02:00:00", "partitionOption": "None"}, "sink": {"type": "DelimitedTextSink", "storeSettings": {"type": "AzureBlobFSWriteSettings"}, "formatSettings": {"type": "DelimitedTextWriteSettings", "quoteAllText": true, "fileExtension": ".txt"}}, "enableStaging": false, "translator": {"type": "TabularTranslator", "typeConversion": true, "typeConversionSettings": {"allowDataTruncation": true, "treatBooleanAsNumber": false}}}, "inputs": [{"referenceName": "MWRSRVEDW_REPORTSLOGIC_sql_SSRS", "type": "DatasetReference"}], "outputs": [{"referenceName": "AzureDataLakeStorage_BenchmarkNetreport_csv", "type": "DatasetReference", "parameters": {"FolderPath": "DDI Migration/Mawer Fund Benchmark Gross&Net performance/Net performance", "FileName": {"value": "@variables('outputfilename')", "type": "Expression"}}}]}, {"name": "Generate Gross File", "type": "Copy", "dependsOn": [], "policy": {"timeout": "7.00:00:00", "retry": 0, "retryIntervalInSeconds": 30, "secureOutput": false, "secureInput": false}, "userProperties": [], "typeProperties": {"source": {"type": "SqlServerSource", "sqlReaderStoredProcedureName": "[OnDemandReporting].[usp_rptGetGrossPerformance]", "storedProcedureParameters": {"Reportdate": {"type": "Datetime", "value": "2021-01-01"}}, "queryTimeout": "02:00:00", "partitionOption": "None"}, "sink": {"type": "DelimitedTextSink", "storeSettings": {"type": "AzureBlobFSWriteSettings"}, "formatSettings": {"type": "DelimitedTextWriteSettings", "quoteAllText": true, "fileExtension": ".txt"}}, "enableStaging": false}, "inputs": [{"referenceName": "MWRSRVEDW_REPORTSLOGIC_sql_SSRS", "type": "DatasetReference"}], "outputs": [{"referenceName": "AzureDataLakeStorage_BenchmarkGrossreport_csv", "type": "DatasetReference", "parameters": {"FolderPath": "DDI Migration/Mawer Fund Benchmark Gross&Net performance/Gross Performance", "FileName": {"value": "@variables('Filename')", "type": "Expression"}}}]}], "variables": {"Filename": {"type": "String", "defaultValue": "grossssrsreport.csv"}, "outputfilename": {"type": "String", "defaultValue": "netssrsreport.csv"}}, "folder": {"name": "Internal/DDI/IN-329 DDI Migration/IN-338 Mawer Fund Benchmark SSRS report"}, "annotations": [], "lastPublishTime": "2023-12-01T04:12:15Z"}, "type": "Microsoft.DataFactory/factories/pipelines"}